# 构建指南

## 打包方式选择

本项目支持两种打包方式：

### 1. electron-builder（推荐用于发布）
```bash
# Windows 平台
npm run build-win

# macOS 平台
npm run build-mac

# Linux 平台
npm run build-linux
```

### 2. electron-packager（推荐用于开发测试）
```bash
# 普通打包（源码可见）
npm run pack-win

# 混淆代码后打包（源码保护）
npm run pack-obfuscate

# 恢复原始代码（开发时使用）
npm run restore
```

## 源码保护

### ASAR 打包
- **当前状态**: 已启用
- **作用**: 将应用代码打包成 ASAR 文件，提供基本的源码保护
- **配置**: 在 `build-packager.js` 中的 `asar` 选项

### 代码混淆
- **工具**: javascript-obfuscator
- **使用**: `npm run pack-obfuscate` 进行混淆后打包
- **恢复**: `npm run restore` 恢复原始代码
- **保护级别**: 高级源码保护，包括控制流扁平化、死代码注入、字符串加密等

### 数据库路径处理
- **开发环境**: 数据库文件位于项目的 `data/` 目录
- **打包环境**: 数据库文件位于应用目录下的 `data/` 文件夹
- **自动迁移**: 首次启动时自动将开发环境数据库复制到应用目录
- **WAL文件处理**: 
  - WAL文件（`.db-wal`）包含未提交的事务，会一同复制
  - SHM文件（`.db-shm`）是临时共享内存文件，不需要复制，会自动重建
  - 确保数据完整性和事务一致性
- **便于管理**: 数据库文件与应用程序在同一目录，便于备份和迁移

## 打包输出

- electron-builder 输出：`dist/` 目录
- electron-packager 输出：`dist/会员管理-win32-x64/` 目录

## 注意事项

1. **图标问题**: 当前 `icon.ico` 文件可能格式不正确，已在打包配置中暂时禁用
2. **混淆模式**: 
   - 使用 `pack-obfuscate` 后源码将被混淆，难以阅读和调试
   - 混淆前会自动备份原始文件到 `backup/` 目录
   - 开发时使用 `npm run restore` 恢复原始代码
3. **开发调试**: 打包前确保应用在开发环境中正常运行
4. **依赖完整**: 确保所有 npm 依赖都已正确安装

本项目是基于 Electron 的桌面应用，支持 Windows、macOS 和 Linux 平台。

## 环境要求

- Node.js 18+
- npm 或 yarn

## 安装依赖

```bash
npm install
```

## 开发模式

```bash
npm run dev
```

## 构建应用

### Windows 构建

在 Windows 系统上运行：

```bash
npm run build-win
```

生成的文件位于 `dist/` 目录下。

### macOS 构建

**方法一：在 macOS 系统上构建**

在 macOS 系统上运行：

```bash
npm run build-mac
```

**方法二：使用 GitHub Actions（推荐）**

1. 将代码推送到 GitHub 仓库
2. GitHub Actions 会自动构建所有平台的应用
3. 在 Actions 页面下载构建好的 macOS 应用

**方法三：使用 Docker（高级用户）**

```bash
# 构建 Docker 镜像
docker build -t member-app-builder .

# 运行构建
docker run --rm -v $(pwd)/dist:/app/dist member-app-builder npm run build-mac
```

### Linux 构建

在 Linux 系统上运行：

```bash
npm run build-linux
```

## 跨平台构建说明

由于 Electron 的限制：
- Windows 应用只能在 Windows 上构建
- macOS 应用只能在 macOS 上构建  
- Linux 应用只能在 Linux 上构建

如果需要在 Windows 上生成 macOS 版本，建议使用：
1. GitHub Actions（最简单）
2. 云端 macOS 虚拟机服务
3. 本地 macOS 虚拟机（需要 macOS 许可证）

## 构建产物

- **Windows**: `dist/Member管理系统 Setup 1.0.0.exe`
- **macOS**: `dist/Member管理系统-1.0.0.dmg`
- **Linux**: `dist/Member管理系统-1.0.0.AppImage`

## 图标文件

项目已包含以下图标文件：
- `assets/icon.svg` - 通用 SVG 图标
- `assets/icon.icns` - macOS 图标（需要替换为真实的 .icns 文件）
- `assets/icon.ico` - Windows 图标（需要添加）
- `assets/icon.png` - Linux 图标（需要添加）

建议使用高分辨率的原始图标生成各平台所需的图标格式。