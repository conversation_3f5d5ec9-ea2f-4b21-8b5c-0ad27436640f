const fs = require('fs');
const path = require('path');

// 需要恢复的文件列表
const filesToRestore = [
  'src/js/login.js',
  'src/js/member-management.js',
  'src/renderer.js',
  'src/preload.js',
  'main.js',
  // 数据库相关文件
  'src/database/adminDao.js',
  'src/database/memberDao.js',
  'src/database/pointsDao.js',
  'src/database/operationLogsDao.js',
  'src/database/levelRatiosDao.js',
  'src/database/commissionDao.js'
];

const backupDir = path.join(__dirname, '..', 'backup');

if (!fs.existsSync(backupDir)) {
  console.error('备份目录不存在！请先运行混淆脚本。');
  process.exit(1);
}

console.log('开始恢复原始代码...');

filesToRestore.forEach(file => {
  const filePath = path.join(__dirname, '..', file);
  const backupPath = path.join(backupDir, file.replace(/\//g, '_'));
  
  if (fs.existsSync(backupPath)) {
    try {
      // 从备份恢复文件
      const originalCode = fs.readFileSync(backupPath, 'utf8');
      fs.writeFileSync(filePath, originalCode);
      
      console.log(`✓ 已恢复: ${file}`);
    } catch (error) {
      console.error(`✗ 恢复失败: ${file}`, error.message);
    }
  } else {
    console.warn(`⚠ 备份文件不存在: ${file}`);
  }
});

console.log('代码恢复完成！');
console.log('现在可以正常开发和调试了。');