const JavaScriptObfuscator = require('javascript-obfuscator');
const fs = require('fs');
const path = require('path');

// 需要混淆的文件列表
const filesToObfuscate = [
  'src/js/login.js',
  'src/js/member-management.js',
  'src/renderer.js',
  'src/preload.js',
  'main.js',
  // 数据库相关文件（包含敏感业务逻辑）
  'src/database/adminDao.js',
  'src/database/memberDao.js',
  'src/database/pointsDao.js',
  'src/database/operationLogsDao.js',
  'src/database/levelRatiosDao.js',
  'src/database/commissionDao.js',
  'src/database/transactionManager.js',
  'src/database/db.js',
  'src/database/init.js',
  // 工具类文件（包含系统敏感信息）
  'src/utils/logger.js',
  'src/utils/dbMigration.js'
];

// 混淆配置（生产环境专用）
const obfuscationOptions = {
  compact: true,
  controlFlowFlattening: true,
  controlFlowFlatteningThreshold: 0.75,
  deadCodeInjection: true,
  deadCodeInjectionThreshold: 0.4,
  debugProtection: true,           // 启用调试保护
  debugProtectionInterval: 2000,   // 调试保护间隔
  disableConsoleOutput: true,      // 禁用控制台输出
  identifierNamesGenerator: 'hexadecimal',
  log: false,
  renameGlobals: false,
  rotateStringArray: true,
  selfDefending: true,
  shuffleStringArray: true,
  splitStrings: true,
  splitStringsChunkLength: 10,
  stringArray: true,
  stringArrayEncoding: ['base64'],
  stringArrayThreshold: 0.75,
  transformObjectKeys: true,
  unicodeEscapeSequence: false
};

// 创建备份目录
const backupDir = path.join(__dirname, '..', 'backup');
if (!fs.existsSync(backupDir)) {
  fs.mkdirSync(backupDir, { recursive: true });
}

console.log('开始混淆代码...');
console.log(`将混淆 ${filesToObfuscate.length} 个文件`);

let successCount = 0;
let failCount = 0;

filesToObfuscate.forEach(file => {
  const filePath = path.join(__dirname, '..', file);
  const backupPath = path.join(backupDir, file.replace(/\//g, '_'));

  if (fs.existsSync(filePath)) {
    try {
      // 备份原始文件
      const originalCode = fs.readFileSync(filePath, 'utf8');
      fs.writeFileSync(backupPath, originalCode);

      // 混淆代码
      console.log(`正在混淆: ${file}...`);
      const obfuscatedCode = JavaScriptObfuscator.obfuscate(originalCode, obfuscationOptions).getObfuscatedCode();

      // 原子性写入：先写入临时文件，再重命名
      const tempPath = filePath + '.tmp';
      fs.writeFileSync(tempPath, obfuscatedCode);
      fs.renameSync(tempPath, filePath);

      console.log(`✓ 已混淆: ${file}`);
      successCount++;
    } catch (error) {
      console.error(`✗ 混淆失败: ${file}`, error.message);
      failCount++;

      // 如果混淆失败，尝试恢复原始文件
      try {
        if (fs.existsSync(backupPath)) {
          const originalCode = fs.readFileSync(backupPath, 'utf8');
          fs.writeFileSync(filePath, originalCode);
          console.log(`  ↻ 已恢复原始文件: ${file}`);
        }
      } catch (restoreError) {
        console.error(`  ✗ 恢复失败: ${file}`, restoreError.message);
      }
    }
  } else {
    console.warn(`⚠ 文件不存在: ${file}`);
    failCount++;
  }
});

console.log('\n=== 混淆结果 ===');
console.log(`✓ 成功: ${successCount} 个文件`);
console.log(`✗ 失败: ${failCount} 个文件`);
console.log('原始文件已备份到 backup/ 目录');
console.log('使用 npm run restore 可以恢复原始代码');

if (failCount > 0) {
  console.warn('\n⚠️  部分文件混淆失败，请检查错误信息');
  process.exit(1);
} else {
  console.log('\n🎉 所有文件混淆成功！');
}