const packager = require('electron-packager');
const path = require('path');
const fs = require('fs');
const readline = require('readline');

// 打包配置
const options = {
  // 应用源码目录
  dir: path.join(__dirname, '..'),
  
  // 应用名称
  name: '会员管理',
  
  // 输出目录
  out: path.join(__dirname, '..', 'dist'),
  
  // 目标平台和架构
  platform: 'win32',
  arch: 'x64',
  
  // 应用版本
  appVersion: '1.0.0',
  
  // Electron 版本
  electronVersion: '36.4.0',
  
  // 覆盖已存在的输出
  overwrite: true,
  
  // 启用 asar 打包（源码保护）
  asar: {
    unpack: '*.{node,dll}'
  },
  
  // 忽略的文件和目录
  ignore: [
    /^\/\.git/,
    /^\/node_modules\/\.bin/,
    /^\/\.trae/,
    /^\/\.github/,
    /^\/scripts/,
    /^\/backup/,
    /^\/dist/,
    /^\/tmp/,
    /README\.md$/,
    /BUILD\.md$/,
    /\.md$/,
    /start\.(bat|sh)$/,
    /package-lock\.json$/,
    /\.log$/
  ],
  
  // 应用图标（暂时禁用，图标文件可能有格式问题）
  // icon: path.join(__dirname, '..', 'assets', 'icon.ico'),
  
  // 应用信息
  appCopyright: 'Copyright © 2024',
  appBundleId: 'com.member.app',
  
  // 下载选项
  download: {
    cache: path.join(require('os').homedir(), '.electron'),
    mirror: 'https://npmmirror.com/mirrors/electron/'
  },
  
  // 临时目录（设置到项目外部避免循环复制）
  tmpdir: path.join(require('os').tmpdir(), 'electron-packager')
};

async function build() {
  try {
    console.log('开始打包应用...');
    console.log('配置信息:', {
      name: options.name,
      platform: options.platform,
      arch: options.arch,
      electronVersion: options.electronVersion
    });

    // 检查数据库文件
    const dbPath = path.join(__dirname, '..', 'data', 'member.db');
    if (fs.existsSync(dbPath)) {
      const stats = fs.statSync(dbPath);
      console.log(`\n数据库文件信息:`);
      console.log(`  路径: ${dbPath}`);
      console.log(`  大小: ${(stats.size / 1024).toFixed(2)} KB`);
      console.log(`  修改时间: ${stats.mtime}`);
      console.log(`  ⚠️  请确认这是适合生产环境的数据库文件`);
    } else {
      console.warn('⚠️  未找到数据库文件，打包后需要重新初始化数据库');
    }
    
    const appPaths = await packager(options);
    
    console.log('\n打包完成！');
    console.log('输出路径:', appPaths);
    
    // 显示打包结果信息
    appPaths.forEach(appPath => {
      const stats = fs.statSync(appPath);
      console.log(`\n应用路径: ${appPath}`);
      console.log(`创建时间: ${stats.birthtime}`);
      
      // 列出主要文件
      const files = fs.readdirSync(appPath);
      console.log('主要文件:');
      files.forEach(file => {
        const filePath = path.join(appPath, file);
        const fileStats = fs.statSync(filePath);
        const size = fileStats.isDirectory() ? '[目录]' : `${(fileStats.size / 1024 / 1024).toFixed(2)} MB`;
        console.log(`  ${file} - ${size}`);
      });
    });
    
  } catch (error) {
    console.error('打包失败:', error);
    process.exit(1);
  }
}

// 执行打包
build();