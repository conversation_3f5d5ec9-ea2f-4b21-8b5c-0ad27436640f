# Member会员管理系统 - 需求分析文档（最新版）

## 1. 会员充值体系

### 1.1 充值套餐定义
| 充值期限 | 月费(元) | 获得销售积分 |
|----------|----------|-------------|
| 半个月   | 398      | 398         |
| 一个月   | 698      | 698         |
| 二个月   | 1298     | 1298        |   会员充值 没有销售积分

### 1.2 充值规则说明
- **充值获得积分**：充值金额1:1获得销售积分
- **自己充值限制**：给自己充值会员，也没有会员积分
- **权益启动时间**：T+1（次日生效）

## 2. 积分体系

### 2.1 会员积分系统  会员积分
- **兑换比例**：10:1兑换月费（10积分兑换1元月费）
- **清空机制**：每个自然年最后一天24:00清空所有会员积分
- **获得方式**：通过分层积分奖励获得

### 2.2 销售积分系统   销售积分
- **使用方式**：1:1抵扣产品
- **获得方式**：充值获得 + 分层积分奖励

### 2.3 分层积分奖励规则
当下级会员充值时，上级会员按层级获得积分奖励：

#### 2.3.1 会员积分分层比例
- **1层**：10%
- **2层**：15%
- **3层**：5%
- **4层**：4%
- **5层**：3%
- **6层**：2%
- **7层**：1%
- **8层**：0.5%
- **9层**：0.4%
- **10层**：0.3%
- **11层**：0.2%
- **12层及以上**：0.1%

#### 2.3.2 销售积分分层比例
- **1层**：100%
- **2层**：100%
- **3层**：50%
- **4层及以上**：10%

## 3. 管理员登录系统

### 3.1 登录功能
#### 3.1.1 登录界面
- **账号输入框**：管理员账号
- **密码输入框**：管理员密码
- **登录按钮**：验证账号密码
- **记住密码**：可选功能，本地保存登录状态

#### 3.1.2 登录验证
- **账号密码验证**：验证管理员身份
- **登录状态保持**：成功登录后保持登录状态
- **自动登出**：长时间无操作自动登出
- **权限控制**：只有登录的管理员才能操作系统

#### 3.1.3 管理员管理
- **默认管理员**：系统预设默认管理员账号
  - 默认账号：admin
  - 默认密码：123456
- **管理员信息**：账号、密码、创建时间、最后登录时间
- **密码修改**：管理员可以修改自己的密码
- **安全要求**：密码需要加密存储

## 4. 系统功能需求

### 4.1 会员管理功能
#### 4.1.1 表格形式展示
- **会员列表表格**：以表格形式展示所有会员信息
- **表格字段**：
  - 会员名称
  - 会员积分余额
  - 销售积分余额
  - 会员到期日期
  - 上级会员

#### 4.1.2 基础操作功能
- **筛选功能**：支持按条件筛选会员
- **新增会员**：手动录入会员名、指定上级
- **会员充值**：支持3种充值套餐（半月、一月、二月）

#### 4.1.3 积分管理功能
- **会员积分加减**：手动调整会员积分
- **销售积分加减**：手动调整销售积分
- **积分历史查看**：
  - 销售积分历史记录
  - 会员积分历史记录

### 4.2 操作记录管理
#### 4.2.1 记录分类
- **会员积分操作记录**
- **销售积分操作记录**

#### 4.2.2 记录类型
- **手动操作**：管理员手动调整积分
- **自动操作**：系统自动分层奖励积分

#### 4.2.3 筛选功能
- **按积分类型筛选**：会员积分/销售积分
- **按操作类型筛选**：手动/自动

### 4.3 数据录入规则
- **会员名称**：文本框手动录入
- **不可导出**：系统不提供数据导出功能

## 5. 数据结构设计

### 5.1 管理员信息表
- **管理员ID**：唯一标识
- **账号**：登录账号
- **密码**：加密存储的密码
- **管理员名称**：显示名称
- **创建时间**：账号创建时间
- **最后登录时间**：最近一次登录时间
- **登录状态**：当前是否在线

### 5.2 会员信息表
- **会员ID**：唯一标识
- **会员名称**：文本字段
- **上级会员ID**：推荐关系
- **会员积分余额**：当前会员积分
- **销售积分余额**：当前销售积分
- **会员到期日期**：会员权益到期时间
- **创建时间**：会员添加时间
- **最后更新时间**：信息最后修改时间

### 5.3 积分操作记录表
- **记录ID**：唯一标识
- **会员ID**：关联会员
- **积分类型**：会员积分/销售积分
- **操作类型**：手动/自动
- **操作金额**：积分变动数量（正负数）
- **操作前余额**：操作前积分余额
- **操作后余额**：操作后积分余额
- **操作原因**：操作说明/备注
- **操作时间**：记录创建时间
- **操作员**：执行操作的管理员

### 5.4 充值记录表
- **充值ID**：唯一标识
- **会员ID**：充值会员
- **充值套餐**：半月/一月/二月
- **充值金额**：实际充值金额
- **获得积分**：获得的销售积分
- **到期日期**：会员权益到期时间
- **充值时间**：充值操作时间

## 6. 界面设计要求

### 6.1 登录界面
- **界面布局**：居中显示登录表单
- **输入控件**：账号输入框、密码输入框
- **操作按钮**：登录按钮、记住密码复选框
- **界面美化**：现代化UI设计，与主界面风格一致

### 6.2 主界面布局
- **顶部导航**：功能模块切换
- **主要内容区**：会员管理表格
- **操作按钮区**：新增、编辑、删除等操作
- **筛选区域**：条件筛选控件

### 6.3 会员管理表格
- **表格列**：会员名、会员积分、销售积分、到期日期、上级
- **操作列**：积分加减按钮、充值按钮、历史查看按钮
- **分页功能**：支持大量数据分页显示

### 6.4 弹窗界面
- **新增会员弹窗**：会员名输入、上级选择
- **积分调整弹窗**：积分类型、调整数量、操作原因
- **充值弹窗**：充值套餐选择、确认信息
- **历史记录弹窗**：积分变动历史列表

## 7. 业务流程

### 7.1 管理员登录流程
1. 打开系统，显示登录界面
2. 输入管理员账号和密码
3. 点击登录按钮进行验证
4. 验证成功后进入主界面
5. 验证失败显示错误提示

### 7.2 新增会员流程
1. 点击"新增会员"按钮
2. 填写会员名称（必填）
3. 选择上级会员（可选）
4. 确认保存，建立推荐关系链

### 7.3 会员充值流程
1. 选择要充值的会员
2. 选择充值套餐（半月398/一月698/二月1298）
3. 系统自动计算到期日期
4. 会员获得对应销售积分
5. 自动触发上级分层积分奖励

### 7.4 手动积分调整流程
1. 选择要调整的会员
2. 选择积分类型（会员积分/销售积分）
3. 输入调整数量（正数增加，负数减少）
4. 填写操作原因
5. 确认操作，生成操作记录

---

## 系统特点总结

**核心特点：**
1. **管理员登录**：需要账号密码验证才能进入系统
2. **表格化管理**：以表格形式展示和管理会员信息
3. **分层积分奖励**：12层递减的积分分成机制
4. **双积分体系**：会员积分（年底清空）+ 销售积分（永久有效）
5. **操作记录追踪**：完整的积分变动历史记录
6. **手动管理为主**：管理员手动操作，系统自动计算分成

**技术实现要点：**
- 基于Electron的桌面应用
- 本地SQLite数据库存储
- 管理员登录验证系统
- 密码加密存储
- 现代化表格UI组件
- 完整的操作日志系统
