#!/bin/bash

echo "正在启动Member管理系统..."
echo ""

# 添加Homebrew到PATH（适用于Apple Silicon和Intel Mac）
if [[ -d "/opt/homebrew/bin" ]]; then
    export PATH="/opt/homebrew/bin:$PATH"
elif [[ -d "/usr/local/bin" ]]; then
    export PATH="/usr/local/bin:$PATH"
fi

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "Node.js未安装，正在安装..."
    echo "检查是否安装了Homebrew..."
    
    if ! command -v brew &> /dev/null; then
        echo "Homebrew未找到，请确保Homebrew已正确安装并添加到PATH"
        echo "对于Apple Silicon Mac，请运行："
        echo 'echo "export PATH=/opt/homebrew/bin:$PATH" >> ~/.zshrc'
        echo "对于Intel Mac，请运行："
        echo 'echo "export PATH=/usr/local/bin:$PATH" >> ~/.zshrc'
        echo "然后重新启动终端或运行: source ~/.zshrc"
        exit 1
    fi
    
    echo "使用Homebrew安装Node.js..."
    brew install node
    
    if ! command -v node &> /dev/null; then
        echo "Node.js安装失败，请手动安装Node.js后再运行此脚本"
        echo "访问 https://nodejs.org 下载安装包"
        exit 1
    fi
fi

echo "Node.js已安装，版本: $(node --version)"
echo "npm版本: $(npm --version)"
echo ""

# 检查package.json是否存在，如果不存在则初始化
if [ ! -f "package.json" ]; then
    echo "初始化npm项目..."
    npm init -y
fi

# 安装项目依赖
echo "安装项目依赖..."
npm install

# 检查electron是否存在
if [ -f "node_modules/.bin/electron" ]; then
    echo "找到Electron，启动桌面应用..."
    ./node_modules/.bin/electron .
else
    echo "安装Electron..."
    npm install electron --save-dev
    if [ -f "node_modules/.bin/electron" ]; then
        echo "Electron安装成功，启动桌面应用..."
        ./node_modules/.bin/electron .
    else
        echo "Electron安装失败，请检查网络连接或手动运行: npm install electron --save-dev"
        exit 1
    fi
fi

echo ""
echo "按任意键继续..."
read -n 1 -s
