const { app, BrowserWindow, Menu, ipcMain } = require('electron');
const path = require('path');

// 开发环境热重载
const hotReload = require('./hot-reload');

// 导入日志工具
const logger = require('./src/utils/logger');

// 导入数据库模块
const { initDatabase, closeDatabase } = require('./src/database/init');
const adminDao = require('./src/database/adminDao');
const memberDao = require('./src/database/memberDao');
const pointsDao = require('./src/database/pointsDao');
const operationLogsDao = require('./src/database/operationLogsDao');
const levelRatiosDao = require('./src/database/levelRatiosDao');
const CommissionDao = require('./src/database/commissionDao');
const DatabaseMigration = require('./src/utils/dbMigration');

// 保持对window对象的全局引用，如果不这么做的话，当JavaScript对象被
// 垃圾回收的时候，window对象将会自动的关闭
let mainWindow;

// 全局DAO实例
let commissionDao;

function createWindow() {
  // 创建浏览器窗口
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 950, // 增加默认高度，避免会员管理出现竖向滚动条
    minWidth: 1200,
    minHeight: 750, // 增加最小高度，确保有足够空间
    webPreferences: {
      nodeIntegration: false, // 出于安全考虑，禁用node集成
      contextIsolation: true, // 启用上下文隔离
      preload: path.join(__dirname, 'src', 'preload.js'), // 预加载脚本
      webSecurity: true, // 启用web安全
      allowRunningInsecureContent: false, // 禁止运行不安全内容
      experimentalFeatures: false, // 禁用实验性功能
      enableBlinkFeatures: '', // 禁用Blink实验性功能
      disableBlinkFeatures: 'Auxclick' // 禁用某些Blink功能
    },
    icon: path.join(__dirname, 'assets/icon.png'), // 应用图标（可选）
    show: false // 先不显示窗口，等准备好后再显示
  });

  // 禁用新窗口创建
  mainWindow.webContents.setWindowOpenHandler(() => {
    return { action: 'deny' };
  });

  // 禁用导航到外部链接
  mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl);

    // 只允许本地文件导航
    if (parsedUrl.protocol !== 'file:') {
      event.preventDefault();
    }
  });

  // 加载登录页面
  mainWindow.loadFile('src/login.html');

  // 开发环境下打开开发者工具
  if (process.env.NODE_ENV === 'development') {
    mainWindow.webContents.openDevTools();
  }

  // 开发模式下禁用缓存
  if (process.env.NODE_ENV === 'development') {
    // 禁用缓存以确保JavaScript更改立即生效
    mainWindow.webContents.session.clearCache();
  }

  // 禁用网页特性，增强原生应用体验
  mainWindow.webContents.on('dom-ready', () => {
    // 禁用右键菜单（除了开发模式）
    if (process.env.NODE_ENV !== 'development') {
      mainWindow.webContents.executeJavaScript(`
        document.addEventListener('contextmenu', (e) => {
          // 只在非输入元素上禁用右键菜单
          if (!['INPUT', 'TEXTAREA'].includes(e.target.tagName)) {
            e.preventDefault();
          }
        });
      `);
    }

    // 禁用拖拽文件到窗口
    mainWindow.webContents.executeJavaScript(`
      document.addEventListener('dragover', (e) => {
        e.preventDefault();
        e.dataTransfer.dropEffect = 'none';
      });

      document.addEventListener('drop', (e) => {
        e.preventDefault();
      });

      // 禁用F5刷新和Ctrl+R刷新
      document.addEventListener('keydown', (e) => {
        if (e.key === 'F5' || (e.ctrlKey && e.key === 'r')) {
          e.preventDefault();
        }
        // 禁用开发者工具快捷键（生产环境）
        // 注意：在渲染进程中不能直接使用process对象
        if (e.key === 'F12' ||
            (e.ctrlKey && e.shiftKey && e.key === 'I') ||
            (e.ctrlKey && e.shiftKey && e.key === 'C') ||
            (e.ctrlKey && e.shiftKey && e.key === 'J') ||
            (e.ctrlKey && e.key === 'U')) {
          // 在生产环境中禁用这些快捷键
          // e.preventDefault();
        }
      });
    `);
  });

  // 当窗口准备好显示时
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // 当window被关闭，这个事件会被触发
  mainWindow.on('closed', () => {
    // 取消引用window对象，如果你的应用支持多窗口的话，
    // 通常会把多个window对象存放在一个数组里面，
    // 与此同时，你应该删除相应的元素。
    mainWindow = null;
  });

  // 打开开发者工具（开发时使用）
  // mainWindow.webContents.openDevTools();
}

// Electron会在初始化后并准备创建浏览器窗口时，调用这个函数。
// 部分API在ready事件触发后才能使用。
app.whenReady().then(async () => {
  // 只在开发环境执行数据库迁移
  // 生产环境通常直接携带完整的数据库文件
  if (!app.isPackaged) {
    try {
      const dbMigration = new DatabaseMigration();
      await dbMigration.migrate();
      logger.info('数据库迁移完成');
    } catch (error) {
      logger.error('数据库迁移失败:', error);
      // 开发环境迁移失败，退出应用
      app.quit();
      return;
    }
  }

  // 初始化数据库
  try {
    await initDatabase();
    logger.info('数据库初始化成功');

    // 初始化分成记录DAO并设置到pointsDao中
    const database = require('./src/database/db');
    commissionDao = new CommissionDao(database.getDb());
    pointsDao.setCommissionDao(commissionDao);
    logger.info('分成记录系统初始化完成');

  } catch (error) {
    logger.error('数据库初始化失败:', error);
    // 数据库初始化失败，退出应用
    app.quit();
    return;
  }

  // 设置IPC处理程序
  setupIpcHandlers();

  // 初始化热重载
  hotReload.init();

  // 创建窗口
  createWindow();
});

// 当全部窗口关闭时退出。
app.on('window-all-closed', () => {
  // 在macOS上，除非用户用Cmd + Q确定地退出，
  // 否则绝大部分应用及其菜单栏会保持激活。
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// 应用即将退出时关闭数据库连接
app.on('before-quit', () => {
  hotReload.destroy();
  closeDatabase();
});

app.on('activate', () => {
  // 在macOS上，当单击dock图标并且没有其他窗口打开时，
  // 通常在应用程序中重新创建一个窗口。
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// 在这个文件中，你可以包含应用程序剩余的所有部分的代码，
// 也可以拆分成几个文件，然后用require导入。



// 禁用默认菜单栏
app.whenReady().then(() => {
  Menu.setApplicationMenu(null);
});

// 设置IPC处理程序
function setupIpcHandlers() {
  // ==================== 系统信息相关 ====================
  
  // 获取Electron版本
  ipcMain.handle('get-electron-version', async (event) => {
    return process.versions.electron;
  });
  
  // 获取平台信息
  ipcMain.handle('get-platform', async (event) => {
    return process.platform;
  });
  
  // ==================== 管理员相关 ====================

  // 管理员登录
  ipcMain.handle('admin-login', async (event, { username, password }) => {
    try {
      const result = await adminDao.login(username, password);
      return result;
    } catch (error) {
      logger.error('管理员登录错误:', error);
      return { success: false, message: '登录失败', error: error.message };
    }
  });

  // 获取管理员信息
  ipcMain.handle('admin-get-info', async (event, adminId) => {
    try {
      const admin = await adminDao.getAdminById(adminId);
      return { success: true, admin };
    } catch (error) {
      logger.error('获取管理员信息错误:', error);
      return { success: false, message: '获取管理员信息失败', error: error.message };
    }
  });

  // 获取所有管理员列表
  ipcMain.handle('admin-get-all', async (event, operatorId) => {
    try {
      // 验证操作者权限
      const canManage = await adminDao.checkPermission(operatorId, 'canManageAdmins');
      if (!canManage) {
        return { success: false, message: '没有查看管理员列表的权限' };
      }

      const admins = await adminDao.getAllAdmins();
      return { success: true, admins };
    } catch (error) {
      logger.error('获取管理员列表错误:', error);
      return { success: false, message: '获取管理员列表失败', error: error.message };
    }
  });

  // 创建新管理员
  ipcMain.handle('admin-create', async (event, { username, password, name, roleLevel, creatorId }) => {
    try {
      const result = await adminDao.createAdmin(username, password, name, roleLevel, creatorId);

      // 不记录操作日志 - 根据需求，新增管理员不产生操作记录

      return result;
    } catch (error) {
      logger.error('创建管理员错误:', error);
      return { success: false, message: '创建管理员失败', error: error.message };
    }
  });

  // 启用/禁用管理员
  ipcMain.handle('admin-toggle-status', async (event, { adminId, isActive, operatorId }) => {
    try {
      // 在状态切换之前先获取管理员信息（包括被禁用的）
      const operator = await adminDao.getAdminByIdIncludeInactive(operatorId);
      const targetAdmin = await adminDao.getAdminByIdIncludeInactive(adminId);

      const result = await adminDao.toggleAdminStatus(adminId, isActive, operatorId);

      // 记录操作日志 - 只有superadmin才记录禁用/启用操作
      if (result.success && operator?.role_level === 1) {
        await operationLogsDao.logOperation(
          operatorId,
          operator?.name || '未知',
          isActive ? 'enable' : 'disable',
          'admin',
          adminId,
          targetAdmin?.name || '未知',
          `${isActive ? '启用' : '禁用'}管理员: ${targetAdmin?.name}`,
          { is_active: !isActive },
          { is_active: isActive }
        );
      }

      return result;
    } catch (error) {
      logger.error('管理员状态切换错误:', error);
      return { success: false, message: '操作失败', error: error.message };
    }
  });

  // 删除管理员
  ipcMain.handle('admin-delete', async (event, { adminId, operatorId }) => {
    try {
      // 在删除之前先获取管理员信息（用于日志记录）
      const operator = await adminDao.getAdminByIdIncludeInactive(operatorId);
      const targetAdmin = await adminDao.getAdminByIdIncludeInactive(adminId);

      const result = await adminDao.deleteAdmin(adminId, operatorId);

      // 记录操作日志
      if (result.success) {
        await operationLogsDao.logOperation(
          operatorId,
          operator?.name || '未知',
          'delete',
          'admin',
          adminId,
          targetAdmin?.name || '未知',
          `删除管理员: ${targetAdmin?.name}`,
          {
            username: targetAdmin?.username,
            name: targetAdmin?.name,
            role_level: targetAdmin?.role_level
          },
          null
        );
      }

      return result;
    } catch (error) {
      logger.error('删除管理员错误:', error);
      return { success: false, message: '删除管理员失败', error: error.message };
    }
  });

  // ==================== 会员相关 ====================

  // 获取所有会员
  ipcMain.handle('member-get-all', async (event) => {
    try {
      const members = await memberDao.getAllMembers();
      return { success: true, members };
    } catch (error) {
      logger.error('获取会员列表错误:', error);
      return { success: false, message: '获取会员列表失败', error: error.message };
    }
  });

  // 根据ID获取会员
  ipcMain.handle('member-get-by-id', async (event, memberId) => {
    try {
      const member = await memberDao.getMemberById(memberId);
      return { success: true, member };
    } catch (error) {
      logger.error('获取会员信息错误:', error);
      return { success: false, message: '获取会员信息失败', error: error.message };
    }
  });

  // 创建新会员
  ipcMain.handle('member-create', async (event, { name, parentId, adminId, adminName }) => {
    try {
      const result = await memberDao.createMember(name, parentId);

      // 记录操作日志
      if (result.success) {
        await operationLogsDao.logMemberOperation(
          adminId,
          adminName,
          'create',
          result.memberId,
          name,
          `创建新会员: ${name}${parentId ? `, 上级会员ID: ${parentId}` : ''}`
        );
      }

      return result;
    } catch (error) {
      logger.error('创建会员错误:', error);
      return { success: false, message: '创建会员失败', error: error.message };
    }
  });

  // 更新会员信息
  ipcMain.handle('member-update', async (event, { memberId, name, parentId, effectiveDate, expiryDate, adminId, adminName }) => {
    try {
      // 获取更新前的会员信息
      const oldMember = await memberDao.getMemberById(memberId);

      const result = await memberDao.updateMember(memberId, name, parentId, effectiveDate, expiryDate);

      // 记录操作日志
      if (result.success && oldMember) {
        let logMessage = `更新会员信息: ${oldMember.name} → ${name}`;
        if (parentId) {
          logMessage += `, 上级会员ID: ${parentId}`;
        }
        if (effectiveDate) {
          logMessage += `, 生效日期: ${effectiveDate}`;
        }
        if (expiryDate) {
          logMessage += `, 到期日期: ${expiryDate}`;
        }

        await operationLogsDao.logMemberOperation(
          adminId,
          adminName,
          'update',
          memberId,
          name,
          logMessage,
          {
            name: oldMember.name,
            parent_id: oldMember.parent_id,
            effective_date: oldMember.effective_date,
            expire_date: oldMember.expire_date
          },
          {
            name: name,
            parent_id: parentId,
            effective_date: effectiveDate,
            expire_date: expiryDate
          }
        );
      }

      return result;
    } catch (error) {
      logger.error('更新会员信息错误:', error);
      return { success: false, message: '更新会员信息失败', error: error.message };
    }
  });

  // 删除会员
  ipcMain.handle('member-delete', async (event, { memberId, adminId, adminName }) => {
    try {
      // 获取删除前的会员信息
      const member = await memberDao.getMemberById(memberId);

      const result = await memberDao.deleteMember(memberId);

      // 记录操作日志
      if (result.success && member) {
        await operationLogsDao.logMemberOperation(
          adminId,
          adminName,
          'delete',
          memberId,
          member.name,
          `删除会员: ${member.name}`,
          { name: member.name, parent_id: member.parent_id }
        );
      }

      return result;
    } catch (error) {
      logger.error('删除会员错误:', error);
      return { success: false, message: '删除会员失败', error: error.message };
    }
  });

  // 搜索会员
  ipcMain.handle('member-search', async (event, keyword) => {
    try {
      const members = await memberDao.searchMembers(keyword);
      return { success: true, members };
    } catch (error) {
      logger.error('搜索会员错误:', error);
      return { success: false, message: '搜索会员失败', error: error.message };
    }
  });

  // ==================== 应用控制相关 ====================

  // 退出登录
  ipcMain.handle('app-logout', async (event) => {
    try {
      // 重新加载登录页面
      mainWindow.loadFile('src/login.html');
      return { success: true, message: '退出登录成功' };
    } catch (error) {
      logger.error('退出登录错误:', error);
      return { success: false, message: '退出登录失败', error: error.message };
    }
  });

  // ==================== 积分管理相关 ====================

  // 会员费用充值
  ipcMain.handle('membership-recharge', async (event, memberId, packageType, amount, adminId, pointsToUse = 0, pointsSourceMemberId = null) => {
    try {
      // 获取管理员和会员信息
      const admin = await adminDao.getAdminById(adminId);
      const member = await memberDao.getMemberById(memberId);

      const result = await pointsDao.membershipRecharge(memberId, packageType, adminId, pointsToUse, pointsSourceMemberId);

      // 记录操作日志
      if (result.success && admin && member) {
        let description = '会员费用充值';
        if (pointsToUse > 0) {
          description += ` (使用积分: ${pointsToUse})`;
        }
        await operationLogsDao.logRechargeOperation(
          adminId,
          admin.name,
          memberId,
          member.name,
          packageType,
          amount,
          description
        );
      }

      return result;
    } catch (error) {
      logger.error('会员费用充值错误:', error);
      return { success: false, message: error.message || '会员费用充值失败', error: error.message };
    }
  });

  // 手动调整积分（管理员专用，不触发分成）
  ipcMain.handle('points-adjust', async (event, { memberId, pointsType, amount, reason, operatorId }) => {
    try {
      // 获取管理员和会员信息
      const admin = await adminDao.getAdminById(operatorId);
      const member = await memberDao.getMemberById(memberId);

      // 使用专门的管理员调整方法，不触发分成机制
      const result = await pointsDao.adminAdjustPoints(memberId, pointsType, amount, reason, operatorId);

      // 记录操作日志
      if (result.success && admin && member) {
        await operationLogsDao.logPointsOperation(
          operatorId,
          admin.name,
          memberId,
          member.name,
          pointsType,
          amount,
          `管理员手动调整积分: ${reason || '积分调整'}`
        );
      }

      return result;
    } catch (error) {
      logger.error('管理员积分调整错误:', error);
      return { success: false, message: '积分调整失败', error: error.message };
    }
  });

  // 手动调整积分（带分成机制）
  ipcMain.handle('points-adjust-with-commission', async (event, { memberId, pointsType, amount, reason, operatorId }) => {
    try {
      // 获取管理员和会员信息
      const admin = await adminDao.getAdminById(operatorId);
      const member = await memberDao.getMemberById(memberId);

      // 使用带分成机制的调整方法
      const result = await pointsDao.adjustPoints(memberId, pointsType, amount, reason, operatorId);

      // 记录操作日志
      if (result.success && admin && member) {
        await operationLogsDao.logPointsOperation(
          operatorId,
          admin.name,
          memberId,
          member.name,
          pointsType,
          amount,
          `管理员手动调整积分(带分成): ${reason || '积分调整'}`
        );
      }

      return result;
    } catch (error) {
      logger.error('管理员积分调整(带分成)错误:', error);
      return { success: false, message: '积分调整失败', error: error.message };
    }
  });

  // 获取积分记录
  ipcMain.handle('points-get-records', async (event, memberId, pointsType, operationType, limit) => {
    try {
      const result = await pointsDao.getPointsRecords(memberId, pointsType, operationType, limit);
      return result;
    } catch (error) {
      logger.error('获取积分记录错误:', error);
      return { success: false, message: '获取积分记录失败', error: error.message };
    }
  });

  // ==================== 操作记录相关 ====================

  // 获取操作记录
  ipcMain.handle('operation-logs-get', async (event, filters, limit, offset, operatorId) => {
    try {
      // 获取操作者信息
      const operator = await adminDao.getAdminById(operatorId);
      
      // 权限过滤逻辑：
      // 超超管理员（roleLevel=0）：能看到所有操作记录
      // 超级管理员（roleLevel=1）：只能看到普通管理员（roleLevel>=2）和自己的操作
      // 普通管理员（roleLevel>=2）：只能看到其他普通管理员的操作
      if (operator?.roleLevel === 0) {
        // 超超管理员可以看到所有记录，不需要过滤
      } else if (operator?.roleLevel === 1) {
        // 超级管理员只能看到普通管理员和自己的操作，需要排除超超管理员的操作
        const superSuperAdminIds = await adminDao.getSuperSuperAdmins();
        filters.excludeOperatorIds = superSuperAdminIds;
      } else {
        // 普通管理员只能看到其他普通管理员的操作，需要排除超超管理员和超级管理员的操作
        const superSuperAdminIds = await adminDao.getSuperSuperAdmins();
        const superAdminIds = await adminDao.getSuperAdmins();
        filters.excludeOperatorIds = [...superSuperAdminIds, ...superAdminIds];
      }
      
      const result = await operationLogsDao.getOperationLogs(filters, limit, offset);
      return result;
    } catch (error) {
      logger.error('获取操作记录错误:', error);
      return { success: false, message: '获取操作记录失败', error: error.message };
    }
  });

  // 获取操作记录总数
  ipcMain.handle('operation-logs-get-count', async (event, filters, operatorId) => {
    try {
      // 获取操作者信息
      const operator = await adminDao.getAdminById(operatorId);
      
      // 权限过滤逻辑：
      // 超超管理员（roleLevel=0）：能看到所有操作记录
      // 超级管理员（roleLevel=1）：只能看到普通管理员（roleLevel>=2）和自己的操作
      // 普通管理员（roleLevel>=2）：只能看到其他普通管理员的操作
      if (operator?.roleLevel === 0) {
        // 超超管理员可以看到所有记录，不需要过滤
      } else if (operator?.roleLevel === 1) {
        // 超级管理员只能看到普通管理员和自己的操作，需要排除超超管理员的操作
        const superSuperAdminIds = await adminDao.getSuperSuperAdmins();
        filters.excludeOperatorIds = superSuperAdminIds;
      } else {
        // 普通管理员只能看到其他普通管理员的操作，需要排除超超管理员和超级管理员的操作
        const superSuperAdminIds = await adminDao.getSuperSuperAdmins();
        const superAdminIds = await adminDao.getSuperAdmins();
        filters.excludeOperatorIds = [...superSuperAdminIds, ...superAdminIds];
      }
      
      const result = await operationLogsDao.getOperationLogsCount(filters);
      return result;
    } catch (error) {
      logger.error('获取操作记录总数错误:', error);
      return { success: false, message: '获取操作记录总数失败', error: error.message };
    }
  });

  // 获取操作统计
  ipcMain.handle('operation-logs-stats', async (event, filters, operatorId) => {
    try {
      // 获取操作者信息
      const operator = await adminDao.getAdminById(operatorId);
      
      // 权限过滤逻辑：
      // 超超管理员（roleLevel=0）：能看到所有操作记录
      // 超级管理员（roleLevel=1）：只能看到普通管理员（roleLevel>=2）和自己的操作
      // 普通管理员（roleLevel>=2）：只能看到其他普通管理员的操作
      if (operator?.roleLevel === 0) {
        // 超超管理员可以看到所有记录，不需要过滤
      } else if (operator?.roleLevel === 1) {
        // 超级管理员只能看到普通管理员和自己的操作，需要排除超超管理员的操作
        const superSuperAdminIds = await adminDao.getSuperSuperAdmins();
        filters.excludeOperatorIds = superSuperAdminIds;
      } else {
        // 普通管理员只能看到其他普通管理员的操作，需要排除超超管理员和超级管理员的操作
        const superSuperAdminIds = await adminDao.getSuperSuperAdmins();
        const superAdminIds = await adminDao.getSuperAdmins();
        filters.excludeOperatorIds = [...superSuperAdminIds, ...superAdminIds];
      }
      
      const result = await operationLogsDao.getOperationStats(filters);
      return result;
    } catch (error) {
      logger.error('获取操作统计错误:', error);
      return { success: false, message: '获取操作统计失败', error: error.message };
    }
  });

  // 修改管理员密码
  ipcMain.handle('admin-change-password', async (event, adminId, currentPassword, newPassword, operatorId) => {
    try {
      // 检查目标管理员是否存在
      const targetAdmin = await adminDao.getAdminById(adminId);
      if (!targetAdmin) {
        return { success: false, message: '管理员不存在' };
      }
      
      // 权限检查：所有管理员只能修改自己的密码
      if (operatorId !== adminId) {
        return { success: false, message: '权限不足，只能修改自己的密码' };
      }
      
      const result = await adminDao.changePassword(adminId, currentPassword, newPassword);
      return result;
    } catch (error) {
      logger.error('修改密码错误:', error);
      return { success: false, message: '修改密码失败', error: error.message };
    }
  });

  // 重置管理员密码（仅超超管理员可用）
  ipcMain.handle('admin-reset-password', async (event, params) => {
    try {
      logger.info('收到重置密码请求，参数:', params);
      
      // 解构参数
      const { targetAdminId, newPassword, operatorId } = params || {};
      
      logger.info('解构后的参数:', { targetAdminId, newPassword: newPassword ? '***' : undefined, operatorId });
      
      // 输入参数验证
      if (!targetAdminId || !newPassword || !operatorId) {
        logger.error('参数验证失败 - 缺少必要参数:', { targetAdminId, hasPassword: !!newPassword, operatorId });
        return { success: false, message: '缺少必要参数' };
      }
      
      if (typeof targetAdminId !== 'number' || targetAdminId <= 0) {
        logger.error('参数验证失败 - 无效的目标管理员ID:', { targetAdminId, type: typeof targetAdminId });
        return { success: false, message: '无效的目标管理员ID' };
      }
      
      if (typeof newPassword !== 'string' || newPassword.length < 6 || newPassword.length > 50) {
        logger.error('参数验证失败 - 密码格式错误:', { passwordLength: newPassword ? newPassword.length : 0, type: typeof newPassword });
        return { success: false, message: '密码长度必须在6-50个字符之间' };
      }
      
      if (typeof operatorId !== 'number' || operatorId <= 0) {
        logger.error('参数验证失败 - 无效的操作者ID:', { operatorId, type: typeof operatorId });
        return { success: false, message: '无效的操作者ID' };
      }
      
      logger.info('参数验证通过，开始获取管理员信息');
      
      // 获取操作者和目标管理员信息
      const operator = await adminDao.getAdminById(operatorId);
      const targetAdmin = await adminDao.getAdminById(targetAdminId);
      
      logger.info('管理员信息查询结果:', { 
        hasOperator: !!operator, 
        hasTargetAdmin: !!targetAdmin,
        operatorRole: operator ? operator.role_level : undefined,
        targetAdminName: targetAdmin ? targetAdmin.name : undefined
      });
      
      logger.info('调用 adminDao.resetAdminPassword');
      const result = await adminDao.resetAdminPassword(targetAdminId, newPassword, operatorId);
      logger.info('adminDao.resetAdminPassword 返回结果:', result);

      // 记录操作日志
      if (result.success && operator && targetAdmin) {
        logger.info('记录操作日志');
        await operationLogsDao.logOperation(
          operatorId,
          operator.name,
          'reset_password',
          'admin',
          targetAdminId,
          targetAdmin.name,
          `重置管理员密码: ${targetAdmin.name}`,
          { username: targetAdmin.username },
          null
        );
      }

      return result;
    } catch (error) {
      logger.error('重置管理员密码错误:', {
        message: error.message,
        stack: error.stack,
        params: params
      });
      return { success: false, message: '重置密码失败', error: error.message };
    }
  });

  // 退出应用
  ipcMain.handle('app-quit', async (event) => {
    app.quit();
  });

  // ==================== 积分配置相关 ====================

  // 获取所有分层比例设置
  ipcMain.handle('level-ratios-get-all', async (event, operatorId) => {
    try {
      // 验证权限：只有超级超级管理员可以访问积分配置
      const canAccess = await adminDao.canAccessPointsConfig(operatorId);
      if (!canAccess) {
        return { success: false, message: '没有访问积分配置的权限' };
      }

      const ratios = await levelRatiosDao.getAllLevelRatios();
      return { success: true, ratios };
    } catch (error) {
      logger.error('获取分层比例设置错误:', error);
      return { success: false, message: '获取分层比例设置失败', error: error.message };
    }
  });

  // 更新单个分层比例设置
  ipcMain.handle('level-ratios-update', async (event, level, salesRatio, memberRatio, description) => {
    try {
      const result = await levelRatiosDao.updateLevelRatio(level, salesRatio, memberRatio, description);
      return result;
    } catch (error) {
      logger.error('更新分层比例设置错误:', error);
      return { success: false, message: '更新分层比例设置失败', error: error.message };
    }
  });

  // 批量更新分层比例设置
  ipcMain.handle('level-ratios-batch-update', async (event, ratios, operatorId) => {
    try {
      // 验证权限
      const canAccess = await adminDao.canAccessPointsConfig(operatorId);
      if (!canAccess) {
        return { success: false, message: '没有修改积分配置的权限' };
      }

      const updateData = ratios.map(ratio => ({
        level: ratio.level,
        salesRatio: ratio.salesRatio,
        memberRatio: ratio.memberRatio,
        description: ratio.description || `第${ratio.level}层${ratio.level <= 12 ? '' : '及以上'}`
      }));

      const result = await levelRatiosDao.updateMultipleLevelRatios(updateData);

      // 记录操作日志
      if (result.success) {
        const operator = await adminDao.getAdminById(operatorId);
        await operationLogsDao.logOperation(
          operatorId,
          operator?.name || '未知',
          'update',
          'points_config',
          null,
          '积分配置',
          '批量更新分层比例设置',
          null,
          { ratios: updateData }
        );
      }

      return result;
    } catch (error) {
      logger.error('批量更新分层比例设置错误:', error);
      return { success: false, message: '批量更新分层比例设置失败', error: error.message };
    }
  });

  // 重置分层比例设置为默认值
  ipcMain.handle('level-ratios-reset', async (event) => {
    try {
      // 删除现有数据
      const db = require('./src/database/db').getDb();
      await new Promise((resolve, reject) => {
        db.run('DELETE FROM level_ratios', (err) => {
          if (err) reject(err);
          else resolve();
        });
      });

      // 重新插入默认数据
      const defaultRatios = [
        { level: 1, sales_ratio: 100.0, member_ratio: 10.0, description: '第1层：直接下级' },
        { level: 2, sales_ratio: 100.0, member_ratio: 15.0, description: '第2层：间接下级' },
        { level: 3, sales_ratio: 50.0, member_ratio: 5.0, description: '第3层' },
        { level: 4, sales_ratio: 10.0, member_ratio: 4.0, description: '第4层' },
        { level: 5, sales_ratio: 10.0, member_ratio: 3.0, description: '第5层' },
        { level: 6, sales_ratio: 10.0, member_ratio: 2.0, description: '第6层' },
        { level: 7, sales_ratio: 10.0, member_ratio: 1.0, description: '第7层' },
        { level: 8, sales_ratio: 10.0, member_ratio: 0.5, description: '第8层' },
        { level: 9, sales_ratio: 10.0, member_ratio: 0.4, description: '第9层' },
        { level: 10, sales_ratio: 10.0, member_ratio: 0.3, description: '第10层' },
        { level: 11, sales_ratio: 10.0, member_ratio: 0.2, description: '第11层' },
        { level: 12, sales_ratio: 10.0, member_ratio: 0.1, description: '第12层及以上' }
      ];

      const sql = 'INSERT INTO level_ratios (level, sales_ratio, member_ratio, description) VALUES (?, ?, ?, ?)';

      for (const ratio of defaultRatios) {
        await new Promise((resolve, reject) => {
          db.run(sql, [ratio.level, ratio.sales_ratio, ratio.member_ratio, ratio.description], (err) => {
            if (err) reject(err);
            else resolve();
          });
        });
      }

      return { success: true, message: '重置为默认配置成功' };
    } catch (error) {
      logger.error('重置分层比例设置错误:', error);
      return { success: false, message: '重置分层比例设置失败', error: error.message };
    }
  });

  // ==================== 分成记录相关 ====================

  // 获取会员分成记录
  ipcMain.handle('commission-get-member-records', async (event, memberId, filters, limit, offset) => {
    try {
      const result = await commissionDao.getMemberCommissionRecords(memberId, filters, limit, offset);
      return result;
    } catch (error) {
      logger.error('获取会员分成记录错误:', error);
      return { success: false, message: '获取会员分成记录失败', error: error.message };
    }
  });

  // 获取会员分成记录总数
  ipcMain.handle('commission-get-member-records-count', async (event, memberId, filters) => {
    try {
      const result = await commissionDao.getMemberCommissionRecordsCount(memberId, filters);
      return result;
    } catch (error) {
      logger.error('获取会员分成记录总数错误:', error);
      return { success: false, message: '获取会员分成记录总数失败', error: error.message };
    }
  });

  // 获取会员分成统计
  ipcMain.handle('commission-get-member-stats', async (event, memberId, filters) => {
    try {
      const result = await commissionDao.getMemberCommissionStats(memberId, filters);
      return result;
    } catch (error) {
      logger.error('获取会员分成统计错误:', error);
      return { success: false, message: '获取会员分成统计失败', error: error.message };
    }
  });

  // 获取系统分成统计
  ipcMain.handle('commission-get-system-stats', async (event, filters) => {
    try {
      const result = await commissionDao.getSystemCommissionStats(filters);
      return result;
    } catch (error) {
      logger.error('获取系统分成统计错误:', error);
      return { success: false, message: '获取系统分成统计失败', error: error.message };
    }
  });

  // 获取分成记录详情
  ipcMain.handle('commission-get-record-detail', async (event, recordId) => {
    try {
      const result = await commissionDao.getCommissionRecordById(recordId);
      return result;
    } catch (error) {
      logger.error('获取分成记录详情错误:', error);
      return { success: false, message: '获取分成记录详情失败', error: error.message };
    }
  });

  // 获取所有分成记录
  ipcMain.handle('commission-get-all-records', async (event, filters, limit, offset) => {
    try {
      const result = await commissionDao.getAllCommissionRecords(filters, limit, offset);
      return result;
    } catch (error) {
      logger.error('获取所有分成记录错误:', error);
      return { success: false, message: '获取所有分成记录失败', error: error.message };
    }
  });

  // 获取所有分成记录总数
  ipcMain.handle('commission-get-all-records-count', async (event, filters) => {
    try {
      const result = await commissionDao.getAllCommissionRecordsCount(filters);
      return result;
    } catch (error) {
      logger.error('获取所有分成记录总数错误:', error);
      return { success: false, message: '获取所有分成记录总数失败', error: error.message };
    }
  });

  // 获取会员分成排行榜
  ipcMain.handle('commission-get-member-ranking', async (event, filters, limit, offset) => {
    try {
      const result = await commissionDao.getMemberCommissionRanking(filters, limit, offset);
      return result;
    } catch (error) {
      logger.error('获取会员分成排行榜错误:', error);
      return { success: false, message: '获取会员分成排行榜失败', error: error.message };
    }
  });

  // 获取会员分成排行榜总数
  ipcMain.handle('commission-get-member-ranking-count', async (event, filters) => {
    try {
      const result = await commissionDao.getMemberCommissionRankingCount(filters);
      return result;
    } catch (error) {
      logger.error('获取会员分成排行榜总数错误:', error);
      return { success: false, message: '获取会员分成排行榜总数失败', error: error.message };
    }
  });

  logger.info('IPC处理程序设置完成');
}
