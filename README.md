# Member管理系统

基于Electron构建的现代化桌面会员管理应用程序。

## 项目结构

```
member/
├── main.js              # Electron主进程文件
├── package.json         # 项目配置文件
├── README.md           # 项目说明文档
├── src/                # 源代码目录
│   ├── index.html      # 主页面
│   ├── renderer.js     # 渲染进程脚本
│   └── preload.js      # 预加载脚本
├── assets/             # 资源文件目录
│   └── icon.png        # 应用图标
├── dist/               # 构建输出目录
└── node_modules/       # 依赖包目录
```

## 功能特性

- 🎯 现代化的用户界面设计
- 📊 高效的会员数据管理
- 🔒 企业级安全保障
- ⚡ 跨平台高性能运行
- 🎨 响应式界面布局

## 开发环境要求

- Node.js >= 16.0.0
- npm >= 8.0.0

## 安装和运行

### 1. 安装依赖

```bash
npm install
```

### 2. 开发模式运行

```bash
npm start
# 或者
npm run dev
```

### 3. 构建应用

```bash
# 构建所有平台
npm run build

# 构建Windows版本
npm run build-win

# 构建macOS版本
npm run build-mac

# 构建Linux版本
npm run build-linux
```

## 可用脚本

- `npm start` - 启动应用程序
- `npm run dev` - 开发模式启动
- `npm run build` - 构建所有平台的安装包
- `npm run build-win` - 构建Windows安装包
- `npm run build-mac` - 构建macOS安装包
- `npm run build-linux` - 构建Linux安装包

## 技术栈

- **Electron** - 跨平台桌面应用框架
- **HTML5/CSS3** - 现代化前端技术
- **JavaScript** - 应用逻辑开发
- **Node.js** - 后端运行环境

## 开发指南

### 主要文件说明

- **main.js**: Electron主进程，负责创建窗口和应用生命周期管理
- **src/preload.js**: 预加载脚本，提供安全的主进程与渲染进程通信
- **src/renderer.js**: 渲染进程脚本，处理用户界面交互
- **src/index.html**: 应用主页面，包含用户界面布局

### 安全考虑

- 禁用了nodeIntegration以提高安全性
- 启用了contextIsolation进行进程隔离
- 使用preload脚本进行安全的API暴露

## 许可证

ISC License

## 作者

Member Team

## 版本历史

- v1.0.0 - 初始版本
  - 基础Electron应用框架
  - 现代化UI设计
  - 基本的会员管理功能框架
