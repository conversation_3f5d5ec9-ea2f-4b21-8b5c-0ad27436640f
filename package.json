{"name": "member", "version": "1.0.0", "description": "Member管理系统 - 基于Electron的桌面应用", "main": "main.js", "scripts": {"start": "electron .", "dev": "cross-env NODE_ENV=development electron . --dev", "hot": "cross-env NODE_ENV=development electron . --dev", "build": "electron-builder", "build-win": "electron-builder --win", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux", "pack-win": "node scripts/build-packager.js", "pack-obfuscate": "node scripts/obfuscate.js && node scripts/build-packager.js", "restore": "node scripts/restore.js", "test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "http://*************:3001/zs851/member.git"}, "keywords": ["electron", "desktop", "member", "management", "system"], "author": "Member Team", "license": "ISC", "type": "commonjs", "build": {"appId": "com.member.app", "productName": "会员管理", "directories": {"output": "dist"}, "files": ["main.js", "src/**/*", "assets/**/*", "package.json", "hot-reload.js", "data/**/*"], "win": {"target": "portable", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}}, "dependencies": {"bcryptjs": "^2.4.3", "sqlite3": "^5.1.7", "xlsx": "^0.18.5"}, "devDependencies": {"cross-env": "^7.0.3", "electron": "^36.7.4", "electron-builder": "^26.0.12", "electron-packager": "^17.1.2", "javascript-obfuscator": "^4.1.1"}}