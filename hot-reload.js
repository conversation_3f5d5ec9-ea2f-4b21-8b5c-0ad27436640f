const fs = require('fs');
const path = require('path');
const { BrowserWindow } = require('electron');

class SimpleHotReload {
  constructor() {
    this.watchers = [];
    this.isEnabled = process.env.NODE_ENV === 'development' || process.argv.includes('--dev');
  }

  init() {
    if (!this.isEnabled) {
      console.log('热重载未启用（非开发模式）');
      return;
    }

    console.log('🔥 简单热重载已启用');
    this.watchFiles();
  }

  watchFiles() {
    // 监听src目录下的所有文件
    const srcDir = path.join(__dirname, 'src');
    this.watchDirectory(srcDir);
  }

  watchDirectory(dir) {
    try {
      const watcher = fs.watch(dir, { recursive: true }, (eventType, filename) => {
        if (filename && this.shouldReload(filename)) {
          console.log(`📁 文件变化: ${filename} (${eventType})`);
          this.reloadRenderer();
        }
      });

      this.watchers.push(watcher);
      console.log(`👀 正在监听目录: ${dir}`);
    } catch (error) {
      console.log(`无法监听目录 ${dir}:`, error.message);
    }
  }

  shouldReload(filename) {
    // 只重载特定类型的文件
    const extensions = ['.html', '.css', '.js'];
    const ext = path.extname(filename);
    
    // 忽略某些文件
    const ignorePatterns = [
      /node_modules/,
      /\.git/,
      /\.db$/,
      /\.log$/,
      /\.tmp$/
    ];

    for (const pattern of ignorePatterns) {
      if (pattern.test(filename)) {
        return false;
      }
    }

    return extensions.includes(ext);
  }

  reloadRenderer() {
    const windows = BrowserWindow.getAllWindows();
    windows.forEach(window => {
      if (window && !window.isDestroyed()) {
        console.log('🔄 重新加载页面...');
        window.webContents.reloadIgnoringCache();
      }
    });
  }

  destroy() {
    this.watchers.forEach(watcher => {
      if (watcher) {
        watcher.close();
      }
    });
    this.watchers = [];
    console.log('🛑 热重载已停止');
  }
}

module.exports = new SimpleHotReload();
