const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// 在渲染进程中暴露受保护的方法
contextBridge.exposeInMainWorld('electronAPI', {
  // 系统信息
  getVersion: () => {
    // 通过IPC从主进程获取版本信息
    return ipcRenderer.invoke('get-electron-version');
  },
  
  getPlatform: () => {
    return ipcRenderer.invoke('get-platform');
  },

  // 窗口控制
  minimizeWindow: () => {
    ipcRenderer.invoke('window-minimize');
  },

  maximizeWindow: () => {
    ipcRenderer.invoke('window-maximize');
  },

  closeWindow: () => {
    ipcRenderer.invoke('window-close');
  },

  // 文件操作
  openFile: () => {
    return ipcRenderer.invoke('dialog-open-file');
  },

  saveFile: (data) => {
    return ipcRenderer.invoke('dialog-save-file', data);
  },

  // ==================== 管理员相关API ====================

  // 管理员登录
  adminLogin: (username, password) => {
    return ipcRenderer.invoke('admin-login', { username, password });
  },



  // 获取管理员信息
  adminGetInfo: (adminId) => {
    return ipcRenderer.invoke('admin-get-info', adminId);
  },

  // 获取所有管理员列表
  adminGetAll: (operatorId) => {
    return ipcRenderer.invoke('admin-get-all', operatorId);
  },

  // 创建新管理员
  adminCreate: (adminData) => {
    return ipcRenderer.invoke('admin-create', adminData);
  },

  // 启用/禁用管理员
  adminToggleStatus: (data) => {
    return ipcRenderer.invoke('admin-toggle-status', data);
  },

  // 删除管理员
  adminDelete: (data) => {
    return ipcRenderer.invoke('admin-delete', data);
  },

  // 修改管理员密码
  adminChangePassword: (adminId, currentPassword, newPassword, operatorId) => {
    return ipcRenderer.invoke('admin-change-password', adminId, currentPassword, newPassword, operatorId);
  },

  // 重置管理员密码（仅超超管理员可用）
  adminResetPassword: (params) => {
    return ipcRenderer.invoke('admin-reset-password', params);
  },

  // ==================== 会员相关API ====================

  // 获取所有会员
  memberGetAll: () => {
    return ipcRenderer.invoke('member-get-all');
  },

  // 根据ID获取会员
  memberGetById: (memberId) => {
    return ipcRenderer.invoke('member-get-by-id', memberId);
  },

  // 创建新会员
  memberCreate: (memberData) => {
    return ipcRenderer.invoke('member-create', memberData);
  },

  // 更新会员信息
  memberUpdate: (memberData) => {
    return ipcRenderer.invoke('member-update', memberData);
  },

  // 删除会员
  memberDelete: (memberData) => {
    return ipcRenderer.invoke('member-delete', memberData);
  },

  // 搜索会员
  memberSearch: (keyword) => {
    return ipcRenderer.invoke('member-search', keyword);
  },

  // ==================== 积分相关API ====================

  // 手动调整积分
  pointsAdjust: (memberId, pointsType, amount, reason, operatorId) => {
    return ipcRenderer.invoke('points-adjust', { memberId, pointsType, amount, reason, operatorId });
  },

  // 手动调整积分（带分成机制）
  pointsAdjustWithCommission: (memberId, pointsType, amount, reason, operatorId) => {
    return ipcRenderer.invoke('points-adjust-with-commission', { memberId, pointsType, amount, reason, operatorId });
  },

  // 会员充值
  pointsRecharge: (memberId, packageType, amount, operatorId) => {
    return ipcRenderer.invoke('points-recharge', { memberId, packageType, amount, operatorId });
  },

  // 会员费用充值
  membershipRecharge: (memberId, packageType, amount, operatorId, pointsToUse, pointsSourceMemberId) => {
    return ipcRenderer.invoke('membership-recharge', memberId, packageType, amount, operatorId, pointsToUse, pointsSourceMemberId);
  },

  // 获取积分操作记录
  pointsGetRecords: (memberId = null, pointsType = null, operationType = null, limit = 100) => {
    return ipcRenderer.invoke('points-get-records', { memberId, pointsType, operationType, limit });
  },

  // ==================== 操作记录相关API ====================

  // 获取操作记录
  operationLogsGet: (filters = {}, limit = 100, offset = 0, operatorId) => {
    return ipcRenderer.invoke('operation-logs-get', filters, limit, offset, operatorId);
  },

  // 获取操作记录总数
  operationLogsGetCount: (filters = {}, operatorId) => {
    return ipcRenderer.invoke('operation-logs-get-count', filters, operatorId);
  },

  // 获取操作统计
  operationLogsStats: (filters = {}, operatorId) => {
    return ipcRenderer.invoke('operation-logs-stats', filters, operatorId);
  },

  // ==================== 积分配置相关API ====================

  // 获取所有分层比例设置
  levelRatiosGetAll: (operatorId) => {
    return ipcRenderer.invoke('level-ratios-get-all', operatorId);
  },

  // 更新单个分层比例设置
  levelRatiosUpdate: (level, salesRatio, memberRatio, description = null) => {
    return ipcRenderer.invoke('level-ratios-update', level, salesRatio, memberRatio, description);
  },

  // 批量更新分层比例设置
  levelRatiosBatchUpdate: (ratios, operatorId) => {
    return ipcRenderer.invoke('level-ratios-batch-update', ratios, operatorId);
  },

  // 重置分层比例设置为默认值
  levelRatiosReset: () => {
    return ipcRenderer.invoke('level-ratios-reset');
  },

  // ==================== 分成记录相关API ====================

  // 获取会员分成记录
  commissionGetMemberRecords: (memberId, filters = {}, limit = 50, offset = 0) => {
    return ipcRenderer.invoke('commission-get-member-records', memberId, filters, limit, offset);
  },

  // 获取会员分成记录总数
  commissionGetMemberRecordsCount: (memberId, filters = {}) => {
    return ipcRenderer.invoke('commission-get-member-records-count', memberId, filters);
  },

  // 获取会员分成统计
  commissionGetMemberStats: (memberId, filters = {}) => {
    return ipcRenderer.invoke('commission-get-member-stats', memberId, filters);
  },

  // 获取系统分成统计
  commissionGetSystemStats: (filters = {}) => {
    return ipcRenderer.invoke('commission-get-system-stats', filters);
  },

  // 获取分成记录详情
  commissionGetRecordDetail: (recordId) => {
    return ipcRenderer.invoke('commission-get-record-detail', recordId);
  },

  // 获取所有分成记录
  commissionGetAllRecords: (filters = {}, limit = 50, offset = 0) => {
    return ipcRenderer.invoke('commission-get-all-records', filters, limit, offset);
  },

  // 获取所有分成记录总数
  commissionGetAllRecordsCount: (filters = {}) => {
    return ipcRenderer.invoke('commission-get-all-records-count', filters);
  },

  // 获取会员分成排行榜
  commissionGetMemberRanking: (filters = {}, limit = 10, offset = 0) => {
    return ipcRenderer.invoke('commission-get-member-ranking', filters, limit, offset);
  },

  // 获取会员分成排行榜总数
  commissionGetMemberRankingCount: (filters = {}) => {
    return ipcRenderer.invoke('commission-get-member-ranking-count', filters);
  },

  // ==================== 应用控制相关API ====================

  // 退出登录
  appLogout: () => {
    return ipcRenderer.invoke('app-logout');
  },

  // 退出应用
  appQuit: () => {
    return ipcRenderer.invoke('app-quit');
  },

  // 通知系统
  showNotification: (title, body) => {
    return ipcRenderer.invoke('show-notification', { title, body });
  },

  // 应用设置
  getSettings: () => {
    return ipcRenderer.invoke('get-settings');
  },

  saveSettings: (settings) => {
    return ipcRenderer.invoke('save-settings', settings);
  },

  // 事件监听
  onMenuAction: (callback) => {
    ipcRenderer.on('menu-action', callback);
  },

  onWindowResize: (callback) => {
    ipcRenderer.on('window-resize', callback);
  },

  // 移除事件监听
  removeAllListeners: (channel) => {
    ipcRenderer.removeAllListeners(channel);
  }
});

// 当DOM加载完成时
window.addEventListener('DOMContentLoaded', () => {
  // 可以在这里添加一些初始化代码
  console.log('预加载脚本已加载');
  
  // 更新版本信息
  const versionElement = document.querySelector('.version');
  if (versionElement) {
    // 异步获取版本信息
    window.electronAPI.getVersion().then(electronVersion => {
      versionElement.textContent = `版本：v1.0.0 (Electron ${electronVersion})`;
    }).catch(() => {
      versionElement.textContent = `版本：v1.0.0 (Electron unknown)`;
    });
  }
});
