// 渲染进程的JavaScript代码

// 等待DOM加载完成
document.addEventListener('DOMContentLoaded', () => {
    console.log('Renderer process loaded');
    
    // 初始化应用
    initializeApp();
});

// 初始化应用
function initializeApp() {
    // 检查electronAPI是否可用
    if (window.electronAPI) {
        console.log('Electron API available');
        
        // 获取平台信息
        const platform = window.electronAPI.getPlatform();
        console.log('Platform:', platform);
        
        // 更新状态栏
        updateStatusBar();
    } else {
        console.log('Running in browser mode');
    }
    
    // 添加按钮事件监听
    setupEventListeners();
}

// 设置事件监听器
function setupEventListeners() {
    // 如果有electronAPI，设置菜单事件监听
    if (window.electronAPI) {
        window.electronAPI.onMenuAction((event, action) => {
            handleMenuAction(action);
        });
    }
}

// 处理菜单操作
function handleMenuAction(action) {
    switch (action) {
        case 'new':
            console.log('新建操作');
            break;
        case 'open':
            console.log('打开操作');
            break;
        default:
            console.log('未知菜单操作:', action);
    }
}

// 更新状态栏
function updateStatusBar() {
    const statusElement = document.querySelector('.status-bar span:first-child');
    if (statusElement) {
        statusElement.textContent = '状态：系统已就绪';
    }
}

// 按钮点击事件处理函数
function startManagement() {
    console.log('开始管理');
    
    // 显示通知
    if (window.electronAPI) {
        window.electronAPI.showNotification('系统提示', '会员管理功能即将开启');
    }
    
    // 这里可以添加跳转到管理页面的逻辑
    showMessage('会员管理功能正在开发中...', 'info');
}

function viewReports() {
    console.log('查看报表');
    
    // 这里可以添加打开报表页面的逻辑
    showMessage('报表功能正在开发中...', 'info');
}

function openSettings() {
    console.log('打开设置');
    
    // 这里可以添加打开设置页面的逻辑
    showMessage('设置功能正在开发中...', 'info');
}

// 显示消息提示
function showMessage(message, type = 'info') {
    // 创建消息元素
    const messageDiv = document.createElement('div');
    messageDiv.className = `message message-${type}`;
    messageDiv.textContent = message;
    
    // 添加样式
    messageDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 10px;
        color: white;
        font-weight: 600;
        z-index: 1000;
        animation: slideIn 0.3s ease-out;
        max-width: 300px;
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
    `;
    
    // 根据类型设置背景色
    switch (type) {
        case 'success':
            messageDiv.style.background = 'linear-gradient(45deg, #4facfe, #00f2fe)';
            break;
        case 'error':
            messageDiv.style.background = 'linear-gradient(45deg, #f093fb, #f5576c)';
            break;
        case 'warning':
            messageDiv.style.background = 'linear-gradient(45deg, #ffecd2, #fcb69f)';
            messageDiv.style.color = '#333';
            break;
        default:
            messageDiv.style.background = 'linear-gradient(45deg, #667eea, #764ba2)';
    }
    
    // 添加动画样式
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        @keyframes slideOut {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(style);
    
    // 添加到页面
    document.body.appendChild(messageDiv);
    
    // 3秒后自动移除
    setTimeout(() => {
        messageDiv.style.animation = 'slideOut 0.3s ease-out';
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.parentNode.removeChild(messageDiv);
            }
        }, 300);
    }, 3000);
}

// 键盘快捷键处理
document.addEventListener('keydown', (event) => {
    // Ctrl+N 新建
    if (event.ctrlKey && event.key === 'n') {
        event.preventDefault();
        startManagement();
    }
    
    // Ctrl+O 打开
    if (event.ctrlKey && event.key === 'o') {
        event.preventDefault();
        viewReports();
    }
    
    // Ctrl+, 设置
    if (event.ctrlKey && event.key === ',') {
        event.preventDefault();
        openSettings();
    }
    
    // F11 全屏
    if (event.key === 'F11') {
        event.preventDefault();
        if (window.electronAPI) {
            // 这里可以添加全屏切换逻辑
            console.log('切换全屏模式');
        }
    }
});

// 窗口大小改变处理
window.addEventListener('resize', () => {
    console.log('Window resized:', window.innerWidth, 'x', window.innerHeight);
});

// 页面卸载时清理
window.addEventListener('beforeunload', () => {
    if (window.electronAPI) {
        // 清理事件监听器
        window.electronAPI.removeAllListeners('menu-action');
        window.electronAPI.removeAllListeners('window-resize');
    }
});
