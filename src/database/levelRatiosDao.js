const database = require('./db');
const transactionManager = require('./transactionManager');

class LevelRatiosDao {
    constructor() {
        this.db = null;
    }

    // 获取数据库实例
    getDb() {
        if (!this.db) {
            this.db = database.getDb();
        }
        return this.db;
    }

    // 获取所有分层比例设置
    async getAllLevelRatios() {
        return new Promise((resolve, reject) => {
            const sql = `
                SELECT * FROM level_ratios 
                ORDER BY level ASC
            `;

            this.getDb().all(sql, (err, rows) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(rows);
                }
            });
        });
    }

    // 根据层级获取比例设置
    async getLevelRatio(level) {
        return new Promise((resolve, reject) => {
            const sql = `
                SELECT * FROM level_ratios 
                WHERE level = ?
            `;

            this.getDb().get(sql, [level], (err, row) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(row);
                }
            });
        });
    }

    // 更新分层比例设置
    async updateLevelRatio(level, salesRatio, memberRatio, description) {
        return new Promise((resolve, reject) => {
            const sql = `
                UPDATE level_ratios 
                SET sales_ratio = ?, member_ratio = ?, description = ?, updated_at = CURRENT_TIMESTAMP
                WHERE level = ?
            `;

            this.getDb().run(sql, [salesRatio, memberRatio, description, level], function(err) {
                if (err) {
                    reject(err);
                } else {
                    if (this.changes > 0) {
                        resolve({ success: true, message: '比例设置更新成功' });
                    } else {
                        resolve({ success: false, message: '未找到要更新的层级' });
                    }
                }
            });
        });
    }

    // 批量更新分层比例设置
    async updateMultipleLevelRatios(ratiosData, txId = null) {
        console.log('开始批量更新层级比例:', ratiosData);
        
        try {
            for (const ratio of ratiosData) {
                const { level, memberRatio, salesRatio } = ratio;
                
                // 检查层级是否存在
                let existing;
                if (txId) {
                    // 在事务中执行查询需要特殊处理
                    existing = await new Promise((resolve, reject) => {
                        const db = transactionManager.getDb();
                        db.all('SELECT * FROM level_ratios WHERE level = ?', [level], (err, rows) => {
                            if (err) reject(err);
                            else resolve(rows);
                        });
                    });
                } else {
                    existing = await this.runQuery(
                        'SELECT * FROM level_ratios WHERE level = ?',
                        [level]
                    );
                }

                if (existing.length > 0) {
                    // 更新现有记录
                    if (txId) {
                        await transactionManager.executeInTransaction(
                            txId,
                            'UPDATE level_ratios SET member_ratio = ?, sales_ratio = ?, updated_at = CURRENT_TIMESTAMP WHERE level = ?',
                            [memberRatio, salesRatio, level]
                        );
                    } else {
                        await this.runQuery(
                            'UPDATE level_ratios SET member_ratio = ?, sales_ratio = ?, updated_at = CURRENT_TIMESTAMP WHERE level = ?',
                            [memberRatio, salesRatio, level]
                        );
                    }
                    console.log(`层级 ${level} 比例已更新: 会员比例=${memberRatio}, 销售比例=${salesRatio}`);
                } else {
                    // 插入新记录
                    if (txId) {
                        await transactionManager.executeInTransaction(
                            txId,
                            'INSERT INTO level_ratios (level, member_ratio, sales_ratio, created_at, updated_at) VALUES (?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)',
                            [level, memberRatio, salesRatio]
                        );
                    } else {
                        await this.runQuery(
                            'INSERT INTO level_ratios (level, member_ratio, sales_ratio, created_at, updated_at) VALUES (?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)',
                            [level, memberRatio, salesRatio]
                        );
                    }
                    console.log(`层级 ${level} 比例已创建: 会员比例=${memberRatio}, 销售比例=${salesRatio}`);
                }
            }

            return {
                success: true,
                message: '层级比例批量更新成功',
                updatedCount: ratiosData.length
            };

        } catch (error) {
            console.error('批量更新层级比例失败:', error);
            throw error;
        }
    }

    // 辅助方法：将数据库操作转换为Promise
    runQuery(sql, params = []) {
        return new Promise((resolve, reject) => {
            const db = this.getDb();
            
            // 对于事务控制语句，使用exec方法
            if (sql.trim().toUpperCase().startsWith('BEGIN') || 
                sql.trim().toUpperCase().startsWith('COMMIT') || 
                sql.trim().toUpperCase().startsWith('ROLLBACK')) {
                db.exec(sql, (err) => {
                    if (err) {
                        console.error('LevelRatios transaction command failed:', sql, err);
                        reject(err);
                    } else {
                        console.log('LevelRatios transaction command executed:', sql);
                        resolve({ changes: 0, lastID: 0 });
                    }
                });
            } else {
                // 对于普通SQL语句，使用run方法
                db.run(sql, params, function(err) {
                    if (err) {
                        console.error('LevelRatios SQL execution failed:', sql, params, err);
                        reject(err);
                    } else {
                        resolve({ changes: this.changes, lastID: this.lastID });
                    }
                });
            }
        });
    }

    // 获取指定层级范围内的比例设置
    async getLevelRatiosInRange(startLevel, endLevel) {
        return new Promise((resolve, reject) => {
            const sql = `
                SELECT * FROM level_ratios 
                WHERE level >= ? AND level <= ?
                ORDER BY level ASC
            `;

            this.getDb().all(sql, [startLevel, endLevel], (err, rows) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(rows);
                }
            });
        });
    }

    // 计算会员的上级分成
    async calculateUplineCommissions(memberId, amount, pointsType = 'sales') {
        return new Promise(async (resolve, reject) => {
            try {
                // 获取会员的上级链
                const memberDao = require('./memberDao');
                const uplineChain = await this.getUplineChain(memberId);
                
                if (uplineChain.length === 0) {
                    resolve([]);
                    return;
                }

                // 获取分层比例设置
                const maxLevel = Math.min(uplineChain.length, 12);
                const ratios = await this.getLevelRatiosInRange(1, maxLevel);

                // 计算每个上级的分成，只有有效期内的会员才能获得分成
                const commissions = [];
                for (let i = 0; i < uplineChain.length && i < 12; i++) {
                    const ratio = ratios.find(r => r.level === i + 1);
                    if (ratio) {
                        // 检查上级会员是否在有效期内
                        const uplineMember = uplineChain[i];
                        const memberStatusInfo = memberDao.getMemberStatusInfo(uplineMember);

                        if (!memberStatusInfo.isActive) {
                            console.log(`跳过非有效期上级会员: ${uplineMember.name} (ID: ${uplineMember.id}), 状态: ${memberStatusInfo.message}`);
                            continue; // 跳过非有效期的上级会员
                        }

                        const percentage = pointsType === 'sales' ? ratio.sales_ratio : ratio.member_ratio;
                        const commission = Math.round((amount * percentage) / 100 * 10) / 10; // 四舍五入到一位小数

                        console.log(`有效期内上级会员获得分成: ${uplineMember.name} (ID: ${uplineMember.id}), 分成: ${commission}`);

                        commissions.push({
                            memberId: uplineChain[i].id,
                            memberName: uplineChain[i].name,
                            level: i + 1,
                            percentage: percentage,
                            commission: commission
                        });
                    }
                }

                resolve(commissions);
            } catch (error) {
                reject(error);
            }
        });
    }

    // 获取会员的上级链
    async getUplineChain(memberId) {
        return new Promise(async (resolve, reject) => {
            try {
                const memberDao = require('./memberDao');
                const uplineChain = [];
                let currentMemberId = memberId;

                // 获取当前会员信息
                let currentMember = await memberDao.getMemberById(currentMemberId);
                
                // 向上追溯上级链
                while (currentMember && currentMember.parent_id && uplineChain.length < 12) {
                    const parentMember = await memberDao.getMemberById(currentMember.parent_id);
                    if (parentMember) {
                        uplineChain.push(parentMember);
                        currentMember = parentMember;
                    } else {
                        break;
                    }
                }

                resolve(uplineChain);
            } catch (error) {
                reject(error);
            }
        });
    }
}

module.exports = new LevelRatiosDao();
