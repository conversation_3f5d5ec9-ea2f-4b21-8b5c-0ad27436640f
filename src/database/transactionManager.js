const database = require('./db');

class TransactionManager {
    constructor() {
        this.activeTransactions = new Map(); // 存储活跃的事务
    }

    // 获取数据库实例
    getDb() {
        return database.getDb();
    }

    // 开始事务
    async beginTransaction(transactionId = null) {
        const txId = transactionId || this.generateTransactionId();
        
        if (this.activeTransactions.has(txId)) {
            console.log(`Transaction ${txId} already exists, reusing...`);
            return txId;
        }

        return new Promise((resolve, reject) => {
            const db = this.getDb();
            
            // 检查是否已经在事务中
            db.get('PRAGMA journal_mode', (err, row) => {
                if (err) {
                    console.error('Failed to check database state:', err);
                    reject(err);
                    return;
                }
                
                // 尝试开始事务，如果已经在事务中会失败
                db.exec('BEGIN TRANSACTION', (err) => {
                    if (err && err.message && err.message.includes('cannot start a transaction within a transaction')) {
                        // 如果已经在事务中，直接标记为成功并重用现有事务
                        console.log(`Database already in transaction, reusing for ${txId}`);
                        this.activeTransactions.set(txId, {
                            startTime: Date.now(),
                            operations: [],
                            reused: true
                        });
                        resolve(txId);
                    } else if (err) {
                        console.error('Failed to begin transaction:', err);
                        reject(err);
                    } else {
                        console.log(`Transaction ${txId} started`);
                        this.activeTransactions.set(txId, {
                            startTime: Date.now(),
                            operations: []
                        });
                        resolve(txId);
                    }
                });
            });
        });
    }

    // 提交事务
    async commitTransaction(transactionId) {
        if (!this.activeTransactions.has(transactionId)) {
            throw new Error(`Transaction ${transactionId} not found`);
        }

        const transaction = this.activeTransactions.get(transactionId);
        
        // 如果是重用的事务，不执行COMMIT，只清理记录
        if (transaction.reused) {
            console.log(`Transaction ${transactionId} was reused, skipping commit`);
            this.activeTransactions.delete(transactionId);
            return Promise.resolve();
        }

        return new Promise((resolve, reject) => {
            const db = this.getDb();
            db.exec('COMMIT', (err) => {
                if (err) {
                    console.error(`Failed to commit transaction ${transactionId}:`, err);
                    reject(err);
                } else {
                    console.log(`Transaction ${transactionId} committed successfully`);
                    this.activeTransactions.delete(transactionId);
                    resolve();
                }
            });
        });
    }

    // 回滚事务
    async rollbackTransaction(transactionId) {
        if (!this.activeTransactions.has(transactionId)) {
            console.warn(`Transaction ${transactionId} not found for rollback`);
            return;
        }

        const transaction = this.activeTransactions.get(transactionId);
        
        // 如果是重用的事务，不执行ROLLBACK，只清理记录
        if (transaction.reused) {
            console.log(`Transaction ${transactionId} was reused, skipping rollback`);
            this.activeTransactions.delete(transactionId);
            return Promise.resolve();
        }

        return new Promise((resolve, reject) => {
            const db = this.getDb();
            db.exec('ROLLBACK', (err) => {
                if (err) {
                    console.error('Failed to rollback transaction:', err);
                    reject(err);
                } else {
                    console.log(`Transaction ${transactionId} rolled back`);
                    this.activeTransactions.delete(transactionId);
                    resolve();
                }
            });
        });
    }

    // 在事务中执行SQL
    async executeInTransaction(transactionId, sql, params = []) {
        if (!this.activeTransactions.has(transactionId)) {
            throw new Error(`Transaction ${transactionId} not found`);
        }

        const self = this; // 保存this引用
        return new Promise((resolve, reject) => {
            const db = this.getDb();
            db.run(sql, params, function(err) {
                if (err) {
                    console.error(`SQL execution failed in transaction ${transactionId}:`, sql, params, err);
                    reject(err);
                } else {
                    // 记录操作
                    const transaction = self.activeTransactions.get(transactionId);
                    if (transaction) {
                        transaction.operations.push({
                            sql,
                            params,
                            timestamp: Date.now()
                        });
                    }
                    resolve({ lastID: this.lastID, changes: this.changes });
                }
            });
        });
    }

    // 执行事务包装的操作
    async withTransaction(operation, transactionId = null) {
        const txId = transactionId || this.generateTransactionId();
        
        try {
            await this.beginTransaction(txId);
            const result = await operation(txId);
            await this.commitTransaction(txId);
            return result;
        } catch (error) {
            console.error(`Transaction ${txId} failed:`, error);
            await this.rollbackTransaction(txId);
            throw error;
        }
    }

    // 检查事务是否存在
    hasTransaction(transactionId) {
        return this.activeTransactions.has(transactionId);
    }

    // 生成事务ID
    generateTransactionId() {
        return `tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    // 获取活跃事务信息
    getActiveTransactions() {
        const transactions = {};
        for (const [id, info] of this.activeTransactions) {
            transactions[id] = {
                startTime: info.startTime,
                duration: Date.now() - info.startTime,
                operationCount: info.operations.length
            };
        }
        return transactions;
    }

    // 清理超时事务（可选的安全机制）
    async cleanupTimeoutTransactions(timeoutMs = 30000) {
        const now = Date.now();
        const timeoutTransactions = [];
        
        for (const [id, info] of this.activeTransactions) {
            if (now - info.startTime > timeoutMs) {
                timeoutTransactions.push(id);
            }
        }

        for (const txId of timeoutTransactions) {
            console.warn(`Cleaning up timeout transaction: ${txId}`);
            await this.rollbackTransaction(txId);
        }

        return timeoutTransactions.length;
    }
}

module.exports = new TransactionManager();