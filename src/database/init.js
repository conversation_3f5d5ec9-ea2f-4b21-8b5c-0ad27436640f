const database = require('./db');

// 数据库初始化函数
async function initDatabase() {
    try {
        console.log('Initializing database...');
        await database.init();
        console.log('Database initialization completed');
        return { success: true, message: 'Database initialization successful' };
    } catch (error) {
        console.error('Database initialization failed:', error);
        return { success: false, message: 'Database initialization failed', error: error.message };
    }
}

// 关闭数据库连接
function closeDatabase() {
    database.close();
}

// 测试数据库连接
async function testConnection() {
    try {
        const db = database.getDb();
        if (!db) {
            throw new Error('数据库未初始化');
        }

        return new Promise((resolve, reject) => {
            db.get('SELECT 1 as test', (err, row) => {
                if (err) {
                    reject(err);
                } else {
                    resolve({ success: true, message: '数据库连接正常' });
                }
            });
        });
    } catch (error) {
        return { success: false, message: '数据库连接失败', error: error.message };
    }
}

// 获取数据库统计信息
async function getDatabaseStats() {
    try {
        const db = database.getDb();
        if (!db) {
            throw new Error('数据库未初始化');
        }

        return new Promise((resolve, reject) => {
            const stats = {};
            
            // 获取各表的记录数
            const queries = [
                { table: 'admins', key: 'adminCount' },
                { table: 'members', key: 'memberCount' },
                { table: 'points_records', key: 'pointsRecordCount' },
                { table: 'recharge_records', key: 'rechargeRecordCount' }
            ];

            let completed = 0;
            
            queries.forEach(query => {
                db.get(`SELECT COUNT(*) as count FROM ${query.table}`, (err, row) => {
                    if (err) {
                        reject(err);
                        return;
                    }
                    
                    stats[query.key] = row.count;
                    completed++;
                    
                    if (completed === queries.length) {
                        resolve(stats);
                    }
                });
            });
        });
    } catch (error) {
        throw error;
    }
}

// 清空所有数据（保留表结构）
async function clearAllData() {
    try {
        const db = database.getDb();
        if (!db) {
            throw new Error('数据库未初始化');
        }

        return new Promise((resolve, reject) => {
            db.serialize(() => {
                db.run('BEGIN TRANSACTION');
                
                // 清空所有表数据
                const tables = ['points_records', 'recharge_records', 'members', 'admins'];
                let completed = 0;
                
                tables.forEach(table => {
                    db.run(`DELETE FROM ${table}`, (err) => {
                        if (err) {
                            db.run('ROLLBACK');
                            reject(err);
                            return;
                        }
                        
                        completed++;
                        if (completed === tables.length) {
                            // 重新插入默认管理员
                            database.insertDefaultAdmin().then(() => {
                                db.run('COMMIT');
                                resolve({ success: true, message: '数据清空成功' });
                            }).catch(error => {
                                db.run('ROLLBACK');
                                reject(error);
                            });
                        }
                    });
                });
            });
        });
    } catch (error) {
        throw error;
    }
}

// 备份数据库
async function backupDatabase() {
    try {
        const fs = require('fs');
        const path = require('path');
        
        const sourceFile = path.join(__dirname, '../../data/member.db');
        const backupDir = path.join(__dirname, '../../backup');
        
        // 确保备份目录存在
        if (!fs.existsSync(backupDir)) {
            fs.mkdirSync(backupDir, { recursive: true });
        }
        
        // 使用东八区时间生成时间戳
        const now = new Date();
        const timestamp = now.toLocaleString('zh-CN', {
            timeZone: 'Asia/Shanghai',
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        }).replace(/[\/\s:]/g, match => {
            if (match === '/') return '-';
            if (match === ' ') return '_';
            if (match === ':') return '-';
            return match;
        });
        const backupFile = path.join(backupDir, `member_backup_${timestamp}.db`);
        
        // 复制数据库文件
        fs.copyFileSync(sourceFile, backupFile);
        
        return { 
            success: true, 
            message: '数据库备份成功', 
            backupFile: backupFile 
        };
    } catch (error) {
        return { 
            success: false, 
            message: '数据库备份失败', 
            error: error.message 
        };
    }
}

// 恢复数据库
async function restoreDatabase(backupFile) {
    try {
        const fs = require('fs');
        const path = require('path');
        
        const targetFile = path.join(__dirname, '../../data/member.db');
        
        // 检查备份文件是否存在
        if (!fs.existsSync(backupFile)) {
            throw new Error('备份文件不存在');
        }
        
        // 关闭当前数据库连接
        database.close();
        
        // 复制备份文件
        fs.copyFileSync(backupFile, targetFile);
        
        // 重新初始化数据库
        await database.init();
        
        return { 
            success: true, 
            message: '数据库恢复成功' 
        };
    } catch (error) {
        return { 
            success: false, 
            message: '数据库恢复失败', 
            error: error.message 
        };
    }
}

module.exports = {
    initDatabase,
    closeDatabase,
    testConnection,
    getDatabaseStats,
    clearAllData,
    backupDatabase,
    restoreDatabase
};
