const database = require('./db');
const operationLogsDao = require('./operationLogsDao');
const logger = require('../utils/logger');

class MemberDao {
    constructor() {
        // 不再缓存数据库实例
    }

    // 获取数据库实例
    getDb() {
        return database.getDb();
    }

    // 检查会员是否在有效期内
    isMemberActive(member) {
        if (!member) return false;

        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

        // 检查是否有生效日期和到期日期
        if (!member.effective_date || !member.expire_date) {
            return false; // 未购买会员或数据不完整
        }

        const effectiveDate = new Date(member.effective_date);
        const expireDate = new Date(member.expire_date);

        // 检查日期是否有效
        if (isNaN(effectiveDate.getTime()) || isNaN(expireDate.getTime())) {
            return false;
        }

        const effectiveDay = new Date(effectiveDate.getFullYear(), effectiveDate.getMonth(), effectiveDate.getDate());
        const expireDay = new Date(expireDate.getFullYear(), expireDate.getMonth(), expireDate.getDate());

        // 当前日期必须在生效日期和到期日期之间（包含边界）
        return today >= effectiveDay && today <= expireDay;
    }

    // 获取会员状态信息
    getMemberStatusInfo(member) {
        if (!member) {
            return { isActive: false, status: 'unknown', message: '会员信息不存在', canUsePoints: false };
        }

        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

        // 未购买会员
        if (!member.effective_date || !member.expire_date) {
            return {
                isActive: false,
                status: 'not_purchased',
                message: '未开通会员',
                canUsePoints: false
            };
        }

        const effectiveDate = new Date(member.effective_date);
        const expireDate = new Date(member.expire_date);

        // 检查日期是否有效
        if (isNaN(effectiveDate.getTime()) || isNaN(expireDate.getTime())) {
            return {
                isActive: false,
                status: 'invalid_date',
                message: '会员日期数据异常',
                canUsePoints: false
            };
        }

        const effectiveDay = new Date(effectiveDate.getFullYear(), effectiveDate.getMonth(), effectiveDate.getDate());
        const expireDay = new Date(expireDate.getFullYear(), expireDate.getMonth(), expireDate.getDate());

        // 未生效
        if (today < effectiveDay) {
            return {
                isActive: false,
                status: 'not_effective',
                message: `会员将于 ${effectiveDate.toLocaleDateString('zh-CN', { timeZone: 'Asia/Shanghai' })} 生效`,
                canUsePoints: false
            };
        }

        // 已过期
        if (today > expireDay) {
            return {
                isActive: false,
                status: 'expired',
                message: `会员已于 ${expireDate.toLocaleDateString('zh-CN', { timeZone: 'Asia/Shanghai' })} 过期`,
                canUsePoints: false
            };
        }

        // 有效期内
        return {
            isActive: true,
            status: 'active',
            message: `会员有效期至 ${expireDate.toLocaleDateString('zh-CN', { timeZone: 'Asia/Shanghai' })}`,
            canUsePoints: true
        };
    }

    // 获取所有会员列表
    async getAllMembers() {
        return new Promise((resolve, reject) => {
            const sql = `
                SELECT
                    m.id,
                    m.name,
                    m.parent_id,
                    p.name as parent_name,
                    m.member_points,
                    m.sales_points,
                    m.effective_date,
                    m.expire_date,
                    m.created_at,
                    m.updated_at
                FROM members m
                LEFT JOIN members p ON m.parent_id = p.id
                ORDER BY m.created_at DESC
            `;

            this.getDb().all(sql, [], (err, rows) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(rows);
                }
            });
        });
    }

    // 根据ID获取会员信息
    async getMemberById(memberId) {
        return new Promise((resolve, reject) => {
            const sql = `
                SELECT
                    m.id,
                    m.name,
                    m.parent_id,
                    p.name as parent_name,
                    m.member_points,
                    m.sales_points,
                    m.effective_date,
                    m.expire_date,
                    m.created_at,
                    m.updated_at
                FROM members m
                LEFT JOIN members p ON m.parent_id = p.id
                WHERE m.id = ?
            `;

            this.getDb().get(sql, [memberId], (err, row) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(row);
                }
            });
        });
    }

    // 创建新会员
    async createMember(name, parentId = null) {
        return new Promise((resolve, reject) => {
            // 先检查会员名是否已存在
            const checkSql = `SELECT COUNT(*) as count FROM members WHERE name = ?`;

            this.getDb().get(checkSql, [name], (err, row) => {
                if (err) {
                    reject(err);
                    return;
                }

                if (row.count > 0) {
                    resolve({ success: false, message: '会员名已存在' });
                    return;
                }

                // 插入新会员
                const insertSql = `
                    INSERT INTO members (name, parent_id)
                    VALUES (?, ?)
                `;

                this.getDb().run(insertSql, [name, parentId], function(err) {
                    if (err) {
                        reject(err);
                    } else {
                        resolve({
                            success: true,
                            message: '会员创建成功',
                            memberId: this.lastID
                        });
                    }
                });
            });
        });
    }

    // 更新会员信息
    async updateMember(memberId, name, parentId, effectiveDate = null, expiryDate = null) {
        return new Promise((resolve, reject) => {
            // 检查新名称是否与其他会员重复
            const checkSql = `SELECT COUNT(*) as count FROM members WHERE name = ? AND id != ?`;

            this.getDb().get(checkSql, [name, memberId], (err, row) => {
                if (err) {
                    reject(err);
                    return;
                }

                if (row.count > 0) {
                    resolve({ success: false, message: '会员名已存在' });
                    return;
                }

                // 更新会员信息
                const updateSql = `
                    UPDATE members
                    SET name = ?, parent_id = ?, effective_date = ?, expire_date = ?, updated_at = datetime('now', 'localtime')
                    WHERE id = ?
                `;

                this.getDb().run(updateSql, [name, parentId, effectiveDate, expiryDate, memberId], (err) => {
                    if (err) {
                        reject(err);
                    } else {
                        resolve({ success: true, message: '会员信息更新成功' });
                    }
                });
            });
        });
    }

    // 删除会员
    async deleteMember(memberId) {
        return new Promise((resolve, reject) => {
            logger.info('开始删除会员', `ID: ${memberId}`, `类型: ${typeof memberId}`);

            // 确保memberId是数字
            const memberIdNum = parseInt(memberId);
            if (isNaN(memberIdNum)) {
                logger.error('无效的会员ID:', memberId);
                resolve({ success: false, message: '无效的会员ID' });
                return;
            }

            // 先检查会员是否存在
            const checkExistSql = `SELECT id, name FROM members WHERE id = ?`;
            this.getDb().get(checkExistSql, [memberIdNum], (err, member) => {
                if (err) {
                    logger.error('检查会员是否存在时出错:', err);
                    reject(err);
                    return;
                }

                if (!member) {
                    logger.info('会员不存在', `ID: ${memberIdNum}`);
                    resolve({ success: false, message: '会员不存在' });
                    return;
                }

                logger.info('找到会员:', member.name, `ID: ${member.id}`);

                // 检查是否有下级会员
                const checkChildrenSql = `SELECT COUNT(*) as count FROM members WHERE parent_id = ?`;
                this.getDb().get(checkChildrenSql, [memberIdNum], (err, row) => {
                    if (err) {
                        logger.error('检查下级会员时出错:', err);
                        reject(err);
                        return;
                    }

                    logger.info('下级会员数量:', row.count);
                    if (row.count > 0) {
                        logger.info('该会员有下级会员，无法删除');
                        resolve({ success: false, message: '该会员有下级会员，无法删除' });
                        return;
                    }

                    // 删除会员
                    const deleteSql = `DELETE FROM members WHERE id = ?`;
                    logger.info('执行删除SQL:', deleteSql, '参数:', [memberIdNum]);

                    this.getDb().run(deleteSql, [memberIdNum], function(err) {
                        if (err) {
                            logger.error('删除会员时出错:', err);
                            reject(err);
                        } else {
                            logger.info('删除成功，影响行数:', this.changes);
                            if (this.changes > 0) {
                                resolve({ success: true, message: '会员删除成功' });
                            } else {
                                resolve({ success: false, message: '删除失败，未找到要删除的会员' });
                            }
                        }
                    });
                });
            });
        });
    }

    // 更新会员积分
    async updateMemberPoints(memberId, memberPoints, salesPoints) {
        return new Promise((resolve, reject) => {
            const sql = `
                UPDATE members
                SET member_points = ?, sales_points = ?, updated_at = datetime('now', 'localtime')
                WHERE id = ?
            `;

            this.getDb().run(sql, [memberPoints, salesPoints, memberId], (err) => {
                if (err) {
                    reject(err);
                } else {
                    resolve({ success: true, message: '积分更新成功' });
                }
            });
        });
    }

    // 搜索会员
    async searchMembers(keyword) {
        return new Promise((resolve, reject) => {
            const sql = `
                SELECT
                    m.id,
                    m.name,
                    m.parent_id,
                    p.name as parent_name,
                    m.member_points,
                    m.sales_points,
                    m.expire_date,
                    m.created_at
                FROM members m
                LEFT JOIN members p ON m.parent_id = p.id
                WHERE m.name LIKE ?
                ORDER BY m.created_at DESC
            `;

            this.getDb().all(sql, [`%${keyword}%`], (err, rows) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(rows);
                }
            });
        });
    }
}

module.exports = new MemberDao();