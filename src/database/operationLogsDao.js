const database = require('./db');

class OperationLogsDao {
    constructor() {
        this.db = null;
    }

    // 获取数据库实例
    getDb() {
        if (!this.db) {
            this.db = database.getDb();
        }
        return this.db;
    }

    // 记录操作日志
    async logOperation(operatorId, operatorName, operationType, targetType, targetId, targetName, operationDetails, oldData = null, newData = null, ipAddress = null, userAgent = null) {
        return new Promise((resolve, reject) => {
            const sql = `
                INSERT INTO operation_logs 
                (operator_id, operator_name, operation_type, target_type, target_id, target_name, operation_details, old_data, new_data, ip_address, user_agent)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `;

            this.getDb().run(sql, [
                operatorId,
                operatorName,
                operationType,
                targetType,
                targetId,
                targetName,
                operationDetails,
                oldData ? JSON.stringify(oldData) : null,
                newData ? JSON.stringify(newData) : null,
                ipAddress,
                userAgent
            ], function(err) {
                if (err) {
                    console.error('Failed to log operation:', err);
                    reject(err);
                } else {
                    console.log('Operation logged successfully:', operationType);
                    resolve({ success: true, logId: this.lastID });
                }
            });
        });
    }

    // 获取操作记录列表
    async getOperationLogs(filters = {}, limit = 100, offset = 0) {
        return new Promise((resolve, reject) => {
            let sql = `
                SELECT 
                    ol.*,
                    a.name as admin_name
                FROM operation_logs ol
                LEFT JOIN admins a ON ol.operator_id = a.id
                WHERE 1=1
            `;

            const params = [];

            // 添加过滤条件
            if (filters.operatorId) {
                sql += ' AND ol.operator_id = ?';
                params.push(filters.operatorId);
            }

            if (filters.operationType) {
                sql += ' AND ol.operation_type = ?';
                params.push(filters.operationType);
            }

            if (filters.targetType) {
                sql += ' AND ol.target_type = ?';
                params.push(filters.targetType);
            }

            if (filters.targetId) {
                sql += ' AND ol.target_id = ?';
                params.push(filters.targetId);
            }

            if (filters.startDate) {
                sql += ' AND ol.created_at >= ?';
                params.push(filters.startDate);
            }

            if (filters.endDate) {
                sql += ' AND ol.created_at <= ?';
                params.push(filters.endDate);
            }

            if (filters.keyword) {
                sql += ' AND (ol.operation_details LIKE ? OR ol.target_name LIKE ?)';
                params.push(`%${filters.keyword}%`, `%${filters.keyword}%`);
            }

            // 添加排除操作者ID的过滤条件
            if (filters.excludeOperatorIds && filters.excludeOperatorIds.length > 0) {
                const placeholders = filters.excludeOperatorIds.map(() => '?').join(',');
                sql += ` AND ol.operator_id NOT IN (${placeholders})`;
                params.push(...filters.excludeOperatorIds);
            }

            sql += ' ORDER BY ol.created_at DESC LIMIT ? OFFSET ?';
            params.push(limit, offset);

            this.getDb().all(sql, params, (err, rows) => {
                if (err) {
                    reject(err);
                } else {
                    // 解析JSON数据
                    const logs = rows.map(row => ({
                        ...row,
                        old_data: row.old_data ? JSON.parse(row.old_data) : null,
                        new_data: row.new_data ? JSON.parse(row.new_data) : null
                    }));
                    resolve({ success: true, logs: logs });
                }
            });
        });
    }

    // 获取操作记录总数
    async getOperationLogsCount(filters = {}) {
        return new Promise((resolve, reject) => {
            let sql = `
                SELECT COUNT(*) as count
                FROM operation_logs
                WHERE 1=1
            `;

            const params = [];

            // 添加过滤条件
            if (filters.operatorId) {
                sql += ' AND operator_id = ?';
                params.push(filters.operatorId);
            }

            if (filters.operationType) {
                sql += ' AND operation_type = ?';
                params.push(filters.operationType);
            }

            if (filters.targetType) {
                sql += ' AND target_type = ?';
                params.push(filters.targetType);
            }

            if (filters.targetId) {
                sql += ' AND target_id = ?';
                params.push(filters.targetId);
            }

            if (filters.startDate) {
                sql += ' AND created_at >= ?';
                params.push(filters.startDate);
            }

            if (filters.endDate) {
                sql += ' AND created_at <= ?';
                params.push(filters.endDate);
            }

            if (filters.keyword) {
                sql += ' AND (operation_details LIKE ? OR target_name LIKE ?)';
                params.push(`%${filters.keyword}%`, `%${filters.keyword}%`);
            }

            // 添加排除操作者ID的过滤条件
            if (filters.excludeOperatorIds && filters.excludeOperatorIds.length > 0) {
                const placeholders = filters.excludeOperatorIds.map(() => '?').join(',');
                sql += ` AND operator_id NOT IN (${placeholders})`;
                params.push(...filters.excludeOperatorIds);
            }

            this.getDb().get(sql, params, (err, row) => {
                if (err) {
                    reject(err);
                } else {
                    resolve({ success: true, count: row.count });
                }
            });
        });
    }

    // 获取操作记录统计
    async getOperationStats(filters = {}) {
        return new Promise((resolve, reject) => {
            let sql = `
                SELECT
                    operation_type,
                    COUNT(*) as count
                FROM operation_logs
                WHERE 1=1
            `;

            const params = [];

            // 添加过滤条件
            if (filters.startDate) {
                sql += ' AND created_at >= ?';
                params.push(filters.startDate);
            }

            if (filters.endDate) {
                sql += ' AND created_at <= ?';
                params.push(filters.endDate);
            }

            // 添加排除操作者ID的过滤条件
            if (filters.excludeOperatorIds && filters.excludeOperatorIds.length > 0) {
                const placeholders = filters.excludeOperatorIds.map(() => '?').join(',');
                sql += ` AND operator_id NOT IN (${placeholders})`;
                params.push(...filters.excludeOperatorIds);
            }

            sql += ' GROUP BY operation_type ORDER BY count DESC';

            this.getDb().all(sql, params, (err, rows) => {
                if (err) {
                    reject(err);
                } else {
                    resolve({ success: true, stats: rows });
                }
            });
        });
    }

    // 清理旧的操作记录（保留最近N天的记录）
    async cleanOldLogs(daysToKeep = 90) {
        return new Promise((resolve, reject) => {
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

            // 使用东八区时间格式
            const localCutoffDate = cutoffDate.toLocaleString('zh-CN', {
                timeZone: 'Asia/Shanghai',
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            }).replace(/\//g, '-').replace(/,/g, '');

            const sql = `
                DELETE FROM operation_logs
                WHERE created_at < ?
            `;

            this.getDb().run(sql, [localCutoffDate], function(err) {
                if (err) {
                    reject(err);
                } else {
                    console.log(`清理了 ${this.changes} 条旧操作记录`);
                    resolve({ success: true, deletedCount: this.changes });
                }
            });
        });
    }

    // 记录会员相关操作的便捷方法
    async logMemberOperation(operatorId, operatorName, operationType, memberId, memberName, details, oldData = null, newData = null) {
        return this.logOperation(
            operatorId,
            operatorName,
            operationType,
            'member',
            memberId,
            memberName,
            details,
            oldData,
            newData
        );
    }

    // 记录充值相关操作的便捷方法
    async logRechargeOperation(operatorId, operatorName, memberId, memberName, packageType, amount, details) {
        // 将英文套餐类型转换为中文
        const packageTypeMap = {
            'half_month': '半月套餐',
            'one_month': '一月套餐',
            'two_months': '两月套餐'
        };
        
        const chinesePackageType = packageTypeMap[packageType] || packageType;
        
        return this.logOperation(
            operatorId,
            operatorName,
            'recharge',
            'member',
            memberId,
            memberName,
            `${details} - 套餐: ${chinesePackageType}, 金额: ¥${amount}`
        );
    }

    // 记录积分操作的便捷方法
    async logPointsOperation(operatorId, operatorName, memberId, memberName, pointsType, amount, details) {
        return this.logOperation(
            operatorId,
            operatorName,
            'points_adjust',
            'member',
            memberId,
            memberName,
            `${details} - 类型: ${pointsType === 'member' ? '会员积分' : '销售积分'}, 数量: ${amount > 0 ? '+' : ''}${amount}`
        );
    }
}

module.exports = new OperationLogsDao();
