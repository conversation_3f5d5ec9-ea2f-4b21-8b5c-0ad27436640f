const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const bcrypt = require('bcryptjs');
const { app } = require('electron');

class Database {
    constructor() {
        this.db = null;
        // 在打包环境下，数据库文件放在应用目录下的 data 文件夹
        // 需要使用 process.resourcesPath 来获取应用资源目录
        if (app && app.isPackaged) {
            // 打包后使用应用目录下的 data 文件夹
            const appPath = path.dirname(process.execPath);
            this.dbPath = path.join(appPath, 'data', 'member.db');
        } else {
            // 开发环境使用项目目录
            this.dbPath = path.join(__dirname, '../../data/member.db');
        }
    }

    // 初始化数据库连接
    async init() {
        return new Promise((resolve, reject) => {
            // 确保data目录存在
            const fs = require('fs');
            const dataDir = path.dirname(this.dbPath);
            if (!fs.existsSync(dataDir)) {
                fs.mkdirSync(dataDir, { recursive: true });
            }

            this.db = new sqlite3.Database(this.dbPath, (err) => {
                if (err) {
                    console.error('数据库连接失败:', err.message);
                    reject(err);
                } else {
                    console.log('数据库连接成功');
                    // 启用外键约束和WAL模式
                    this.db.run('PRAGMA foreign_keys = ON');
                    this.db.run('PRAGMA journal_mode = WAL');
                    this.createTables().then(resolve).catch(reject);
                }
            });
        });
    }

    // 创建所有数据表
    async createTables() {
        const tables = [
            this.createAdminTable(),
            this.createMemberTable(),
            this.createPointsRecordTable(),
            this.createRechargeRecordTable(),
            this.createLevelRatiosTable(),
            this.createOperationLogsTable(),
            this.createCommissionRecordsTable()
        ];

        try {
            await Promise.all(tables);
            await this.insertDefaultAdmin();
            await this.insertDefaultLevelRatios();
            await this.runMigrations();
            console.log('所有数据库表创建成功');
        } catch (error) {
            console.error('创建数据库表失败:', error);
            throw error;
        }
    }

    // 创建管理员表
    createAdminTable() {
        return new Promise((resolve, reject) => {
            const sql = `
                CREATE TABLE IF NOT EXISTS admins (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    password TEXT NOT NULL,
                    name TEXT NOT NULL,
                    role_level INTEGER DEFAULT 2,
                    created_by INTEGER,
                    can_create_admin INTEGER DEFAULT 0,
                    created_at DATETIME DEFAULT (datetime('now', 'localtime')),
                    last_login_at DATETIME,
                    is_active INTEGER DEFAULT 1,
                    FOREIGN KEY (created_by) REFERENCES admins (id)
                )
            `;

            this.db.run(sql, (err) => {
                if (err) {
                    console.error('创建管理员表失败:', err);
                    reject(err);
                } else {
                    console.log('管理员表创建成功');
                    resolve();
                }
            });
        });
    }

    // 创建会员表
    createMemberTable() {
        return new Promise((resolve, reject) => {
            const sql = `
                CREATE TABLE IF NOT EXISTS members (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    parent_id INTEGER,
                    member_points REAL DEFAULT 0,
                    sales_points REAL DEFAULT 0,
                    effective_date DATETIME,
                    expire_date DATETIME,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (parent_id) REFERENCES members (id)
                )
            `;
            
            this.db.run(sql, (err) => {
                if (err) {
                    console.error('创建会员表失败:', err);
                    reject(err);
                } else {
                    console.log('会员表创建成功');
                    resolve();
                }
            });
        });
    }

    // 创建积分操作记录表
    createPointsRecordTable() {
        return new Promise((resolve, reject) => {
            const sql = `
                CREATE TABLE IF NOT EXISTS points_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    member_id INTEGER NOT NULL,
                    points_type TEXT NOT NULL CHECK (points_type IN ('member', 'sales')),
                    operation_type TEXT NOT NULL CHECK (operation_type IN ('manual', 'auto')),
                    amount REAL NOT NULL,
                    balance_before REAL NOT NULL,
                    balance_after REAL NOT NULL,
                    reason TEXT,
                    operator_id INTEGER,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (member_id) REFERENCES members (id),
                    FOREIGN KEY (operator_id) REFERENCES admins (id)
                )
            `;
            
            this.db.run(sql, (err) => {
                if (err) {
                    console.error('创建积分记录表失败:', err);
                    reject(err);
                } else {
                    console.log('积分记录表创建成功');
                    resolve();
                }
            });
        });
    }

    // 创建充值记录表
    createRechargeRecordTable() {
        return new Promise((resolve, reject) => {
            const sql = `
                CREATE TABLE IF NOT EXISTS recharge_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    member_id INTEGER NOT NULL,
                    package_type TEXT NOT NULL CHECK (package_type IN ('half_month', 'one_month', 'two_months')),
                    total_amount REAL NOT NULL,
                    points_used REAL DEFAULT 0,
                    cash_amount REAL NOT NULL,
                    points_gained REAL NOT NULL,
                    expire_date DATETIME NOT NULL,
                    operator_id INTEGER,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (member_id) REFERENCES members (id),
                    FOREIGN KEY (operator_id) REFERENCES admins (id)
                )
            `;
            
            this.db.run(sql, (err) => {
                if (err) {
                    console.error('创建充值记录表失败:', err);
                    reject(err);
                } else {
                    console.log('充值记录表创建成功');
                    resolve();
                }
            });
        });
    }

    // 创建分层比例设置表
    createLevelRatiosTable() {
        return new Promise((resolve, reject) => {
            const sql = `
                CREATE TABLE IF NOT EXISTS level_ratios (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    level INTEGER NOT NULL UNIQUE,
                    sales_ratio REAL NOT NULL DEFAULT 0,
                    member_ratio REAL NOT NULL DEFAULT 0,
                    description TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            `;

            this.db.run(sql, (err) => {
                if (err) {
                    console.error('创建分层比例表失败:', err);
                    reject(err);
                } else {
                    console.log('分层比例表创建成功');
                    resolve();
                }
            });
        });
    }

    // 插入默认管理员账号
    async insertDefaultAdmin() {
        return new Promise((resolve, reject) => {
            // 检查是否已存在超级超级管理员
            this.db.get('SELECT COUNT(*) as count FROM admins WHERE username = ?', ['superadmin'], async (err, row) => {
                if (err) {
                    reject(err);
                    return;
                }

                let needCreateSuperAdmin = row.count === 0;

                // 检查是否已存在普通管理员
                this.db.get('SELECT COUNT(*) as count FROM admins WHERE username = ?', ['admin'], async (err, adminRow) => {
                    if (err) {
                        reject(err);
                        return;
                    }

                    let needCreateAdmin = adminRow.count === 0;

                    // 创建需要的管理员账号
                    try {
                        let superAdminId = null;

                        // 1. 创建超级超级管理员 (如果不存在)
                        if (needCreateSuperAdmin) {
                            const superAdminPassword = await bcrypt.hash('superadmin123', 10);
                            const superAdminSql = `
                                INSERT INTO admins (username, password, name, role_level, can_create_admin)
                                VALUES (?, ?, ?, ?, ?)
                            `;

                            superAdminId = await new Promise((resolve, reject) => {
                                this.db.run(superAdminSql, ['superadmin', superAdminPassword, '超级超级管理员', 0, 1], function(err) {
                                    if (err) {
                                        reject(err);
                                    } else {
                                        console.log('超级超级管理员创建成功 (superadmin/superadmin123) - 隐藏账号');
                                        resolve(this.lastID);
                                    }
                                });
                            });
                        } else {
                            // 获取现有超级超级管理员ID
                            const superAdmin = await new Promise((resolve, reject) => {
                                this.db.get('SELECT id FROM admins WHERE username = ?', ['superadmin'], (err, row) => {
                                    if (err) reject(err);
                                    else resolve(row);
                                });
                            });
                            superAdminId = superAdmin ? superAdmin.id : 1;
                        }

                        // 2. 创建超级管理员 (如果不存在)
                        if (needCreateAdmin) {
                            const adminPassword = await bcrypt.hash('123456', 10);
                            const adminSql = `
                                INSERT INTO admins (username, password, name, role_level, can_create_admin, created_by)
                                VALUES (?, ?, ?, ?, ?, ?)
                            `;

                            this.db.run(adminSql, ['admin', adminPassword, '系统管理员', 1, 1, superAdminId], (err) => {
                                if (err) {
                                    console.error('创建默认管理员失败:', err);
                                    reject(err);
                                } else {
                                    console.log('默认管理员创建成功 (admin/123456)');
                                    resolve();
                                }
                            });
                        } else {
                            console.log('管理员账号已存在');
                            resolve();
                        }
                    } catch (error) {
                        console.error('密码加密失败:', error);
                        reject(error);
                    }
                });
            });
        });
    }

    // 插入默认分层比例设置
    async insertDefaultLevelRatios() {
        return new Promise((resolve, reject) => {
            // 先检查是否已存在比例设置
            this.db.get('SELECT COUNT(*) as count FROM level_ratios', (err, row) => {
                if (err) {
                    reject(err);
                    return;
                }

                if (row.count > 0) {
                    console.log('分层比例已存在，跳过创建');
                    resolve();
                    return;
                }

                // 创建默认的12层比例设置（根据需求文档）
                const defaultRatios = [
                    // 会员积分分层比例
                    { level: 1, sales_ratio: 100.0, member_ratio: 10.0, description: '第1层：直接下级' },
                    { level: 2, sales_ratio: 100.0, member_ratio: 15.0, description: '第2层：间接下级' },
                    { level: 3, sales_ratio: 50.0, member_ratio: 5.0, description: '第3层' },
                    { level: 4, sales_ratio: 10.0, member_ratio: 4.0, description: '第4层' },
                    { level: 5, sales_ratio: 10.0, member_ratio: 3.0, description: '第5层' },
                    { level: 6, sales_ratio: 10.0, member_ratio: 2.0, description: '第6层' },
                    { level: 7, sales_ratio: 10.0, member_ratio: 1.0, description: '第7层' },
                    { level: 8, sales_ratio: 10.0, member_ratio: 0.5, description: '第8层' },
                    { level: 9, sales_ratio: 10.0, member_ratio: 0.4, description: '第9层' },
                    { level: 10, sales_ratio: 10.0, member_ratio: 0.3, description: '第10层' },
                    { level: 11, sales_ratio: 10.0, member_ratio: 0.2, description: '第11层' },
                    { level: 12, sales_ratio: 10.0, member_ratio: 0.1, description: '第12层及以上' }
                ];

                const sql = `
                    INSERT INTO level_ratios (level, sales_ratio, member_ratio, description)
                    VALUES (?, ?, ?, ?)
                `;

                let completed = 0;
                const total = defaultRatios.length;

                defaultRatios.forEach(ratio => {
                    this.db.run(sql, [ratio.level, ratio.sales_ratio, ratio.member_ratio, ratio.description], (err) => {
                        if (err) {
                            console.error('插入默认比例失败:', err);
                            reject(err);
                            return;
                        }

                        completed++;
                        if (completed === total) {
                            console.log('默认分层比例创建成功');
                            resolve();
                        }
                    });
                });
            });
        });
    }

    // 创建操作记录表
    createOperationLogsTable() {
        return new Promise((resolve, reject) => {
            const sql = `
                CREATE TABLE IF NOT EXISTS operation_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    operator_id INTEGER NOT NULL,
                    operator_name TEXT NOT NULL,
                    operation_type TEXT NOT NULL,
                    target_type TEXT NOT NULL,
                    target_id INTEGER,
                    target_name TEXT,
                    operation_details TEXT,
                    old_data TEXT,
                    new_data TEXT,
                    ip_address TEXT,
                    user_agent TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (operator_id) REFERENCES admins (id)
                )
            `;

            this.db.run(sql, (err) => {
                if (err) {
                    console.error('创建操作日志表失败:', err);
                    reject(err);
                } else {
                    console.log('操作日志表创建成功');
                    resolve();
                }
            });
        });
    }

    // 创建分成记录表
    createCommissionRecordsTable() {
        return new Promise((resolve, reject) => {
            const sql = `
                CREATE TABLE IF NOT EXISTS commission_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    source_member_id INTEGER NOT NULL,
                    source_member_name TEXT NOT NULL,
                    beneficiary_member_id INTEGER NOT NULL,
                    beneficiary_member_name TEXT NOT NULL,
                    level INTEGER NOT NULL,
                    points_type TEXT NOT NULL CHECK (points_type IN ('member', 'sales')),
                    base_amount REAL NOT NULL,
                    commission_rate REAL NOT NULL,
                    commission_amount REAL NOT NULL,
                    trigger_type TEXT NOT NULL CHECK (trigger_type IN ('recharge', 'manual_adjust', 'admin_sales_adjust')),
                    trigger_record_id INTEGER,
                    balance_before REAL NOT NULL,
                    balance_after REAL NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (source_member_id) REFERENCES members (id),
                    FOREIGN KEY (beneficiary_member_id) REFERENCES members (id)
                )
            `;

            this.db.run(sql, (err) => {
                if (err) {
                    console.error('创建分成记录表失败:', err);
                    reject(err);
                } else {
                    console.log('分成记录表创建成功');
                    resolve();
                }
            });
        });
    }

    // 运行数据库迁移
    async runMigrations() {
        try {
            // 迁移会员表字段
            await this.migrateMembersTable();
            // 迁移管理员权限字段
            await this.migrateAdminPermissions();
            console.log('所有数据库迁移完成');
        } catch (error) {
            console.error('数据库迁移失败:', error);
            throw error;
        }
    }

    // 迁移会员表字段
    async migrateMembersTable() {
        return new Promise((resolve, reject) => {
            // 检查是否存在effective_date字段
            this.db.all("PRAGMA table_info(members)", (err, columns) => {
                if (err) {
                    reject(err);
                    return;
                }

                const hasEffectiveDate = columns.some(col => col.name === 'effective_date');

                if (!hasEffectiveDate) {
                    console.log('添加会员表 effective_date 字段...');
                    this.db.run("ALTER TABLE members ADD COLUMN effective_date DATETIME", (err) => {
                        if (err) {
                            console.error('添加 effective_date 字段失败:', err);
                            reject(err);
                        } else {
                            console.log('effective_date 字段添加成功');
                            resolve();
                        }
                    });
                } else {
                    console.log('effective_date 字段已存在');
                    resolve();
                }
            });
        });
    }

    // 迁移管理员权限字段
    async migrateAdminPermissions() {
        return new Promise((resolve, reject) => {
            // 检查是否已有权限相关字段
            this.db.all("PRAGMA table_info(admins)", (err, columns) => {
                if (err) {
                    reject(err);
                    return;
                }

                const hasRoleLevel = columns.some(col => col.name === 'role_level');
                const hasCreatedBy = columns.some(col => col.name === 'created_by');
                const hasCanCreateAdmin = columns.some(col => col.name === 'can_create_admin');

                if (hasRoleLevel && hasCreatedBy && hasCanCreateAdmin) {
                    console.log('管理员权限字段已存在，跳过迁移');
                    resolve();
                    return;
                }

                console.log('开始添加管理员权限字段...');

                // 添加缺失的字段
                const migrations = [];

                if (!hasRoleLevel) {
                    migrations.push("ALTER TABLE admins ADD COLUMN role_level INTEGER DEFAULT 2");
                }
                if (!hasCreatedBy) {
                    migrations.push("ALTER TABLE admins ADD COLUMN created_by INTEGER");
                }
                if (!hasCanCreateAdmin) {
                    migrations.push("ALTER TABLE admins ADD COLUMN can_create_admin INTEGER DEFAULT 0");
                }

                // 执行迁移
                this.executeMigrations(migrations).then(() => {
                    // 更新现有管理员的权限级别
                    this.updateExistingAdminPermissions().then(() => {
                        console.log('管理员权限字段迁移完成');
                        resolve();
                    }).catch(reject);
                }).catch(reject);
            });
        });
    }

    // 执行迁移SQL
    async executeMigrations(migrations) {
        return new Promise((resolve, reject) => {
            if (migrations.length === 0) {
                resolve();
                return;
            }

            let completed = 0;
            migrations.forEach(sql => {
                this.db.run(sql, (err) => {
                    if (err) {
                        console.error('迁移失败:', sql, err);
                        reject(err);
                        return;
                    }

                    completed++;
                    if (completed === migrations.length) {
                        resolve();
                    }
                });
            });
        });
    }

    // 更新现有管理员的权限级别
    async updateExistingAdminPermissions() {
        return new Promise((resolve, reject) => {
            // 将现有的 admin 账号设置为超级管理员
            const sql = `
                UPDATE admins
                SET role_level = 1, can_create_admin = 1
                WHERE username = 'admin' AND (role_level IS NULL OR role_level = 2)
            `;

            this.db.run(sql, (err) => {
                if (err) {
                    reject(err);
                } else {
                    console.log('现有管理员权限已更新');
                    resolve();
                }
            });
        });
    }

    // 关闭数据库连接
    close() {
        if (this.db) {
            this.db.close((err) => {
                if (err) {
                    console.error('Failed to close database:', err);
                } else {
                    console.log('Database connection closed');
                }
            });
        }
    }

    // 获取数据库实例
    getDb() {
        return this.db;
    }
}

// 创建单例实例
const database = new Database();

module.exports = database;
