class CommissionDao {
    constructor(db) {
        this.db = db;
    }

    // 辅助方法：Promise化的数据库运行（避免事务嵌套）
    runQuery(sql, params = []) {
        return new Promise((resolve, reject) => {
            // 在事务内部使用普通的run方法
            this.db.run(sql, params, function(err) {
                if (err) {
                    console.error('Commission SQL execution failed:', sql, params, err);
                    reject(err);
                } else {
                    resolve(this);
                }
            });
        });
    }

    // 创建分成记录
    createCommissionRecord(commissionData) {
        return new Promise((resolve, reject) => {
            const {
                sourceMemberId,
                sourceMemberName,
                beneficiaryMemberId,
                beneficiaryMemberName,
                level,
                pointsType,
                baseAmount,
                commissionRate,
                commissionAmount,
                triggerType,
                triggerRecordId,
                balanceBefore,
                balanceAfter
            } = commissionData;

            const sql = `
                INSERT INTO commission_records (
                    source_member_id, source_member_name, beneficiary_member_id, beneficiary_member_name,
                    level, points_type, base_amount, commission_rate, commission_amount,
                    trigger_type, trigger_record_id, balance_before, balance_after
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `;

            // 使用runQuery方法来避免事务嵌套问题
            this.runQuery(sql, [
                sourceMemberId, sourceMemberName, beneficiaryMemberId, beneficiaryMemberName,
                level, pointsType, baseAmount, commissionRate, commissionAmount,
                triggerType, triggerRecordId, balanceBefore, balanceAfter
            ]).then(result => {
                resolve({
                    success: true,
                    commissionId: result.lastID,
                    message: 'Commission record created successfully'
                });
            }).catch(err => {
                console.error('Failed to create commission record:', err);
                reject(err);
            });
        });
    }

    // 批量创建分成记录（不管理事务，在已有事务中执行）
    createBatchCommissionRecords(commissionRecords) {
        return new Promise(async (resolve, reject) => {
            try {
                if (commissionRecords.length === 0) {
                    resolve({ success: true, message: '没有分成记录需要创建' });
                    return;
                }

                // 顺序创建分成记录，不使用自己的事务管理
                for (let i = 0; i < commissionRecords.length; i++) {
                    try {
                        await this.createCommissionRecord(commissionRecords[i]);
                    } catch (error) {
                        console.error(`Failed to create commission record ${i}:`, error);
                        throw new Error(`Failed to create commission record ${i}: ${error.message}`);
                    }
                }

                resolve({
                    success: true,
                    message: `Successfully created ${commissionRecords.length} commission records`
                });

            } catch (error) {
                console.error('Failed to create batch commission records:', error);
                reject(error);
            }
        });
    }

    // 获取会员的分成记录（作为受益人）
    getMemberCommissionRecords(memberId, filters = {}, limit = 50, offset = 0) {
        return new Promise((resolve, reject) => {
            let sql = `
                SELECT * FROM commission_records 
                WHERE beneficiary_member_id = ?
            `;
            let params = [memberId];

            // 添加过滤条件
            if (filters.pointsType) {
                sql += ' AND points_type = ?';
                params.push(filters.pointsType);
            }

            if (filters.triggerType) {
                sql += ' AND trigger_type = ?';
                params.push(filters.triggerType);
            }

            if (filters.startDate) {
                sql += ' AND created_at >= ?';
                params.push(filters.startDate);
            }

            if (filters.endDate) {
                sql += ' AND created_at <= ?';
                params.push(filters.endDate);
            }

            sql += ' ORDER BY created_at DESC LIMIT ? OFFSET ?';
            params.push(limit, offset);

            this.db.all(sql, params, (err, rows) => {
                if (err) {
                    console.error('Failed to get member commission records:', err);
                    reject(err);
                } else {
                    resolve({
                        success: true,
                        records: rows || []
                    });
                }
            });
        });
    }

    // 获取会员分成记录总数
    getMemberCommissionRecordsCount(memberId, filters = {}) {
        return new Promise((resolve, reject) => {
            let sql = `
                SELECT COUNT(*) as count FROM commission_records 
                WHERE beneficiary_member_id = ?
            `;
            let params = [memberId];

            // 添加过滤条件
            if (filters.pointsType) {
                sql += ' AND points_type = ?';
                params.push(filters.pointsType);
            }

            if (filters.triggerType) {
                sql += ' AND trigger_type = ?';
                params.push(filters.triggerType);
            }

            if (filters.startDate) {
                sql += ' AND created_at >= ?';
                params.push(filters.startDate);
            }

            if (filters.endDate) {
                sql += ' AND created_at <= ?';
                params.push(filters.endDate);
            }

            this.db.get(sql, params, (err, row) => {
                if (err) {
                    console.error('Failed to get member commission records count:', err);
                    reject(err);
                } else {
                    resolve({
                        success: true,
                        count: row ? row.count : 0
                    });
                }
            });
        });
    }

    // 获取会员分成统计
    getMemberCommissionStats(memberId, filters = {}) {
        return new Promise((resolve, reject) => {
            let sql = `
                SELECT 
                    points_type,
                    COUNT(*) as record_count,
                    SUM(commission_amount) as total_commission,
                    AVG(commission_amount) as avg_commission,
                    MIN(commission_amount) as min_commission,
                    MAX(commission_amount) as max_commission
                FROM commission_records 
                WHERE beneficiary_member_id = ?
            `;
            let params = [memberId];

            // 添加过滤条件
            if (filters.startDate) {
                sql += ' AND created_at >= ?';
                params.push(filters.startDate);
            }

            if (filters.endDate) {
                sql += ' AND created_at <= ?';
                params.push(filters.endDate);
            }

            sql += ' GROUP BY points_type';

            this.db.all(sql, params, (err, rows) => {
                if (err) {
                    console.error('Failed to get member commission stats:', err);
                    reject(err);
                } else {
                    resolve({
                        success: true,
                        stats: rows || []
                    });
                }
            });
        });
    }

    // 获取系统分成统计
    getSystemCommissionStats(filters = {}) {
        return new Promise((resolve, reject) => {
            let sql = `
                SELECT 
                    points_type,
                    COUNT(*) as record_count,
                    SUM(commission_amount) as total_commission
                FROM commission_records 
                WHERE 1=1
            `;
            let params = [];

            // 添加过滤条件
            if (filters.startDate) {
                sql += ' AND created_at >= ?';
                params.push(filters.startDate);
            }

            if (filters.endDate) {
                sql += ' AND created_at <= ?';
                params.push(filters.endDate);
            }

            if (filters.pointsType) {
                sql += ' AND points_type = ?';
                params.push(filters.pointsType);
            }

            sql += ' GROUP BY points_type';

            this.db.all(sql, params, (err, rows) => {
                if (err) {
                    console.error('Failed to get system commission stats:', err);
                    reject(err);
                } else {
                    // 处理统计数据
                    let totalMemberCommission = 0;
                    let totalSalesCommission = 0;
                    let totalRecords = 0;

                    rows.forEach(row => {
                        if (row.points_type === 'member') {
                            totalMemberCommission = parseFloat(row.total_commission || 0);
                        } else if (row.points_type === 'sales') {
                            totalSalesCommission = parseFloat(row.total_commission || 0);
                        }
                        totalRecords += parseInt(row.record_count || 0);
                    });

                    resolve({
                        success: true,
                        stats: {
                            totalMemberCommission: totalMemberCommission.toFixed(2),
                            totalSalesCommission: totalSalesCommission.toFixed(2),
                            totalRecords: totalRecords
                        }
                    });
                }
            });
        });
    }

    // 获取分成记录详情
    getCommissionRecordById(recordId) {
        return new Promise((resolve, reject) => {
            const sql = 'SELECT * FROM commission_records WHERE id = ?';
            
            this.db.get(sql, [recordId], (err, row) => {
                if (err) {
                    console.error('Failed to get commission record:', err);
                    reject(err);
                } else {
                    resolve({
                        success: true,
                        record: row || null
                    });
                }
            });
        });
    }

    // 获取所有分成记录（用于系统统计页面）
    getAllCommissionRecords(filters = {}, limit = 50, offset = 0) {
        return new Promise((resolve, reject) => {
            let sql = `
                SELECT cr.*,
                       sm.name as source_member_name,
                       bm.name as beneficiary_member_name
                FROM commission_records cr
                LEFT JOIN members sm ON cr.source_member_id = sm.id
                LEFT JOIN members bm ON cr.beneficiary_member_id = bm.id
                WHERE 1=1
            `;
            const params = [];

            // 添加筛选条件
            if (filters.startDate) {
                sql += ' AND DATE(cr.created_at) >= ?';
                params.push(filters.startDate);
            }
            if (filters.endDate) {
                sql += ' AND DATE(cr.created_at) <= ?';
                params.push(filters.endDate);
            }
            if (filters.pointsType) {
                sql += ' AND cr.points_type = ?';
                params.push(filters.pointsType);
            }
            if (filters.triggerType) {
                sql += ' AND cr.trigger_type = ?';
                params.push(filters.triggerType);
            }
            if (filters.memberId) {
                sql += ' AND cr.beneficiary_member_id = ?';
                params.push(filters.memberId);
            }

            sql += ' ORDER BY cr.created_at DESC LIMIT ? OFFSET ?';
            params.push(limit, offset);

            this.db.all(sql, params, (err, rows) => {
                if (err) {
                    reject(err);
                } else {
                    resolve({
                        success: true,
                        records: rows || [],
                        totalCount: rows ? rows.length : 0
                    });
                }
            });
        });
    }

    // 获取所有分成记录总数
    getAllCommissionRecordsCount(filters = {}) {
        return new Promise((resolve, reject) => {
            let sql = 'SELECT COUNT(*) as count FROM commission_records WHERE 1=1';
            const params = [];

            // 添加筛选条件
            if (filters.startDate) {
                sql += ' AND DATE(created_at) >= ?';
                params.push(filters.startDate);
            }
            if (filters.endDate) {
                sql += ' AND DATE(created_at) <= ?';
                params.push(filters.endDate);
            }
            if (filters.pointsType) {
                sql += ' AND points_type = ?';
                params.push(filters.pointsType);
            }
            if (filters.triggerType) {
                sql += ' AND trigger_type = ?';
                params.push(filters.triggerType);
            }
            if (filters.memberId) {
                sql += ' AND beneficiary_member_id = ?';
                params.push(filters.memberId);
            }

            this.db.get(sql, params, (err, row) => {
                if (err) {
                    reject(err);
                } else {
                    resolve({
                        success: true,
                        count: row ? row.count : 0
                    });
                }
            });
        });
    }

    // 获取会员分成排行榜
    getMemberCommissionRanking(filters = {}, limit = 20, offset = 0) {
        return new Promise((resolve, reject) => {
            let sql = `
                SELECT 
                    cr.beneficiary_member_id as member_id,
                    m.name as member_name,
                    COUNT(*) as commission_count,
                    SUM(cr.commission_amount) as total_commission,
                    AVG(cr.commission_amount) as avg_commission
                FROM commission_records cr
                LEFT JOIN members m ON cr.beneficiary_member_id = m.id
                WHERE 1=1
            `;
            const params = [];

            // 添加筛选条件
            if (filters.startDate) {
                sql += ' AND DATE(cr.created_at) >= ?';
                params.push(filters.startDate);
            }
            if (filters.endDate) {
                sql += ' AND DATE(cr.created_at) <= ?';
                params.push(filters.endDate);
            }
            if (filters.pointsType) {
                sql += ' AND cr.points_type = ?';
                params.push(filters.pointsType);
            }

            sql += ' GROUP BY cr.beneficiary_member_id, m.name';
            sql += ' ORDER BY total_commission DESC, commission_count DESC';
            sql += ' LIMIT ? OFFSET ?';
            params.push(limit, offset);

            this.db.all(sql, params, (err, rows) => {
                if (err) {
                    reject(err);
                } else {
                    // 格式化数据
                    const ranking = rows.map(row => ({
                        member_id: row.member_id,
                        member_name: row.member_name || '未知会员',
                        commission_count: row.commission_count || 0,
                        total_commission: parseFloat(row.total_commission || 0).toFixed(2),
                        avg_commission: parseFloat(row.avg_commission || 0).toFixed(2)
                    }));

                    resolve({
                        success: true,
                        ranking: ranking
                    });
                }
            });
        });
    }

    // 获取会员分成排行榜总数
    getMemberCommissionRankingCount(filters = {}) {
        return new Promise((resolve, reject) => {
            let sql = `
                SELECT COUNT(DISTINCT cr.beneficiary_member_id) as count
                FROM commission_records cr
                WHERE 1=1
            `;
            const params = [];

            // 添加筛选条件
            if (filters.startDate) {
                sql += ' AND DATE(cr.created_at) >= ?';
                params.push(filters.startDate);
            }
            if (filters.endDate) {
                sql += ' AND DATE(cr.created_at) <= ?';
                params.push(filters.endDate);
            }
            if (filters.pointsType) {
                sql += ' AND cr.points_type = ?';
                params.push(filters.pointsType);
            }

            this.db.get(sql, params, (err, row) => {
                if (err) {
                    reject(err);
                } else {
                    resolve({
                        success: true,
                        count: row ? row.count : 0
                    });
                }
            });
        });
    }
}

module.exports = CommissionDao;
