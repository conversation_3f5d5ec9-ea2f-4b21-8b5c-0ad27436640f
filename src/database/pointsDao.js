const database = require('./db');
const memberDao = require('./memberDao');
const adminDao = require('./adminDao');
const transactionManager = require('./transactionManager');

// 辅助函数：将 Date 对象转换为东八区的 SQLite 格式字符串
function toLocalTimeString(date) {
    if (!date) return null;

    // 创建一个新的 Date 对象，确保使用东八区时间
    const localDate = new Date(date.toLocaleString('en-US', { timeZone: 'Asia/Shanghai' }));

    // 格式化为 SQLite 兼容的格式 (YYYY-MM-DD HH:MM:SS)
    const year = localDate.getFullYear();
    const month = String(localDate.getMonth() + 1).padStart(2, '0');
    const day = String(localDate.getDate()).padStart(2, '0');
    const hours = String(localDate.getHours()).padStart(2, '0');
    const minutes = String(localDate.getMinutes()).padStart(2, '0');
    const seconds = String(localDate.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

class PointsDao {
    constructor() {
        // 不再缓存数据库实例
        this.commissionDao = null; // 将在main.js中设置
    }

    // 设置分成记录DAO
    setCommissionDao(commissionDao) {
        this.commissionDao = commissionDao;
    }

    // 获取数据库实例
    getDb() {
        return database.getDb();
    }

    // 手动调整积分
    async adjustPoints(memberId, pointsType, amount, reason, operatorId) {
        return new Promise(async (resolve, reject) => {
            try {
                // 获取当前会员信息
                const member = await memberDao.getMemberById(memberId);
                if (!member) {
                    resolve({ success: false, message: '会员不存在' });
                    return;
                }

                const currentBalance = pointsType === 'member' ? member.member_points : member.sales_points;
                const newBalance = currentBalance + amount;

                // 检查余额不能为负数
                if (newBalance < 0) {
                    resolve({ success: false, message: '积分余额不足' });
                    return;
                }

                try {
                    // 开始事务
                    await this.runQuery('BEGIN TRANSACTION');

                    try {
                        // 更新会员积分
                        const updateSql = pointsType === 'member'
                            ? `UPDATE members SET member_points = ?, updated_at = datetime('now', 'localtime') WHERE id = ?`
                            : `UPDATE members SET sales_points = ?, updated_at = datetime('now', 'localtime') WHERE id = ?`;

                        await this.runQuery(updateSql, [newBalance, memberId]);

                        // 记录积分变动
                        const recordSql = `
                            INSERT INTO points_records 
                            (member_id, points_type, operation_type, amount, balance_before, balance_after, reason, operator_id)
                            VALUES (?, ?, 'manual', ?, ?, ?, ?, ?)
                        `;

                        const recordResult = await this.runQuery(recordSql, [
                            memberId, pointsType, amount, currentBalance, newBalance, reason, operatorId
                        ]);

                        // 如果是增加积分且有分成DAO，触发分成奖励
                        if (amount > 0 && this.commissionDao) {
                            try {
                                const recordId = recordResult.lastID;
                                await this.processHierarchyRewards(memberId, amount, operatorId, 'manual_adjust', recordId, pointsType);
                            } catch (commissionError) {
                                console.error('分成处理失败:', commissionError);
                                // 分成失败不影响积分调整的成功
                            }
                        }

                        // 提交事务
                        await this.runQuery('COMMIT');

                        resolve({
                            success: true,
                            message: '积分调整成功',
                            newBalance: newBalance
                        });

                    } catch (innerError) {
                        console.error('积分调整事务处理失败:', innerError);
                        try {
                            await this.runQuery('ROLLBACK');
                        } catch (rollbackError) {
                            console.error('事务回滚失败:', rollbackError);
                        }
                        reject(innerError);
                    }

                } catch (error) {
                    console.error('积分调整操作失败:', error);
                    reject(error);
                }
            } catch (error) {
                reject(error);
            }
        });
    }

    // 管理员手动调整积分（不触发分成机制）
    async adminAdjustPoints(memberId, pointsType, amount, reason, operatorId) {
        return new Promise(async (resolve, reject) => {
            try {
                // 严格的参数验证
                if (!memberId || !pointsType || amount === undefined || amount === null || !operatorId) {
                    resolve({ success: false, message: '参数无效：缺少必需字段' });
                    return;
                }

                // 确保只操作单个用户，禁止批量操作
                if (Array.isArray(memberId)) {
                    resolve({ success: false, message: '管理员调整不允许批量操作' });
                    return;
                }

                // 验证积分类型
                if (!['member', 'sales'].includes(pointsType)) {
                    resolve({ success: false, message: '无效的积分类型' });
                    return;
                }

                // 验证会员ID为正整数
                const memberIdNum = parseInt(memberId);
                if (!Number.isInteger(memberIdNum) || memberIdNum <= 0) {
                    resolve({ success: false, message: '无效的会员ID' });
                    return;
                }

                // 验证调整金额为数字
                const amountNum = parseFloat(amount);
                if (!Number.isFinite(amountNum)) {
                    resolve({ success: false, message: '无效的金额数值' });
                    return;
                }

                // 获取当前会员信息
                const member = await memberDao.getMemberById(memberIdNum);
                if (!member) {
                    resolve({ success: false, message: '会员不存在' });
                    return;
                }

                const currentBalance = pointsType === 'member' ? member.member_points : member.sales_points;
                const newBalance = currentBalance + amountNum;

                // 检查余额不能为负数
                if (newBalance < 0) {
                    resolve({ success: false, message: '积分余额不足' });
                    return;
                }

                try {
                    // 开始事务
                    await this.runQuery('BEGIN TRANSACTION');

                    try {
                        // 更新会员积分
                        const updateSql = pointsType === 'member'
                            ? `UPDATE members SET member_points = ?, updated_at = datetime('now', 'localtime') WHERE id = ?`
                            : `UPDATE members SET sales_points = ?, updated_at = datetime('now', 'localtime') WHERE id = ?`;

                        await this.runQuery(updateSql, [newBalance, memberIdNum]);

                        // 记录积分变动 - 标记为管理员手动调整
                        const recordSql = `
                            INSERT INTO points_records
                            (member_id, points_type, operation_type, amount, balance_before, balance_after, reason, operator_id)
                            VALUES (?, ?, 'manual', ?, ?, ?, ?, ?)
                        `;

                        const recordResult = await this.runQuery(recordSql, [
                            memberIdNum, pointsType, amountNum, currentBalance, newBalance,
                            `管理员手动调整: ${reason || '积分调整'}`, operatorId
                        ]);

                        // 管理员手动调整不触发任何分成机制
                        // 这确保了操作范围严格限制在目标用户，不会影响其他用户
                        console.log('管理员手动积分调整完成，未触发分成机制:', {
                            memberId: memberIdNum,
                            memberName: member.name,
                            pointsType,
                            amount: amountNum,
                            currentBalance,
                            newBalance,
                            recordId: recordResult.lastID,
                            operatorId,
                            reason: reason || '积分调整'
                        });

                        // 提交事务
                        await this.runQuery('COMMIT');

                        resolve({
                            success: true,
                            message: '管理员积分调整完成',
                            newBalance: newBalance,
                            recordId: recordResult.lastID
                        });

                    } catch (innerError) {
                        console.error('管理员积分调整事务处理失败:', innerError);
                        try {
                            await this.runQuery('ROLLBACK');
                        } catch (rollbackError) {
                            console.error('事务回滚失败:', rollbackError);
                        }
                        reject(innerError);
                    }

                } catch (error) {
                    console.error('管理员积分调整操作失败:', error);
                    reject(error);
                }
            } catch (error) {
                reject(error);
            }
        });
    }

    // 充值获得积分（自动分层奖励）
    async rechargePoints(memberId, packageType, amount, operatorId) {
        return new Promise(async (resolve, reject) => {
            try {
                // 充值套餐配置
                const packages = {
                    'half_month': { amount: 398, points: 398, days: 15 },
                    'one_month': { amount: 698, points: 698, days: 30 },
                    'two_months': { amount: 1298, points: 1298, days: 60 }
                };

                const packageInfo = packages[packageType];
                if (!packageInfo) {
                    resolve({ success: false, message: '无效的充值套餐' });
                    return;
                }

                // 获取会员信息
                const member = await memberDao.getMemberById(memberId);
                if (!member) {
                    resolve({ success: false, message: '会员不存在' });
                    return;
                }

                // 计算到期日期
                const currentDate = new Date();
                const expireDate = new Date(currentDate.getTime() + packageInfo.days * 24 * 60 * 60 * 1000);

                // 开始事务 - 重构为不使用serialize的版本
                console.log('开始数据库事务处理...');

                try {
                    // 开始事务
                    await this.runQuery('BEGIN TRANSACTION');

                    try {
                        // 更新会员到期日期（充值者本人不获得积分，只延长有效期）
                        const updateMemberSql = `
                            UPDATE members
                            SET expire_date = ?, updated_at = datetime('now', 'localtime')
                            WHERE id = ?
                        `;

                        await this.runQuery(updateMemberSql, [toLocalTimeString(expireDate), memberId]);

                        // 记录充值记录
                        const rechargeSql = `
                            INSERT INTO recharge_records
                            (member_id, package_type, total_amount, points_used, cash_amount, points_gained, expire_date, operator_id)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                        `;

                        const rechargeResult = await this.runQuery(rechargeSql, [
                            memberId, packageType, packageInfo.amount, 0, packageInfo.amount, 0, // 充值者本人不获得积分
                            toLocalTimeString(expireDate), operatorId
                        ]);

                        // 获取充值记录ID
                        const rechargeRecordId = rechargeResult.lastID;

                        // 充值者本人不获得积分，无需记录积分变动

                        // 处理分层积分奖励（传递充值记录ID）- 只分成会员积分，使用充值金额作为基础
                        console.log('开始处理分层积分奖励:', { memberId, amount: packageInfo.amount, operatorId, rechargeRecordId });
                        try {
                            const rewardResult = await this.processHierarchyRewards(memberId, packageInfo.amount, operatorId, 'recharge', rechargeRecordId, 'member');
                            console.log('分层积分奖励处理结果:', rewardResult);
                        } catch (rewardError) {
                            console.error('分层积分奖励处理失败:', rewardError);
                            // 分成失败不影响充值成功
                        }

                        // 提交事务
                        console.log('准备提交事务...');
                        await this.runQuery('COMMIT');
                        console.log('事务提交成功');
                        
                        // 事务提交后记录操作日志
                        try {
                            // 记录积分抵扣操作日志
                            if (pointsToUse > 0) {
                                const sourceMember = await memberDao.getMemberById(pointsSourceMemberId || memberId);
                                const targetMember = await memberDao.getMemberById(memberId);
                                const operator = await adminDao.getAdminById(operatorId);
                                if (pointsSourceMemberId && pointsSourceMemberId !== memberId) {
                                    // A用户给C用户充值，使用A用户的积分
                                    await operationLogsDao.logPointsOperation(
                                        operatorId,
                                        operator ? operator.name : '系统',
                                        pointsSourceMemberId,
                                        sourceMember ? sourceMember.name : '未知用户',
                                        'member',
                                        -pointsToUse,
                                        `为${targetMember ? targetMember.name : '用户'}充值使用积分抵扣`
                                    );
                                } else {
                                    // 用户为自己充值使用积分
                                    await operationLogsDao.logPointsOperation(
                                        operatorId,
                                        operator ? operator.name : '系统',
                                        pointsSourceMemberId || memberId,
                                        sourceMember ? sourceMember.name : '未知用户',
                                        'member',
                                        -pointsToUse,
                                        '充值使用积分抵扣'
                                    );
                                }
                            }
                            
                            // 记录充值操作日志
                            let rechargeDetails = '会员费用充值';
                            if (pointsToUse > 0) {
                                const sourceMember = await memberDao.getMemberById(pointsSourceMemberId || memberId);
                                if (pointsSourceMemberId && pointsSourceMemberId !== memberId) {
                                    rechargeDetails += `，使用${sourceMember ? sourceMember.name : '用户'}的${pointsToUse}积分抵扣`;
                                } else {
                                    rechargeDetails += `，使用${pointsToUse}积分抵扣`;
                                }
                            }
                            
                            await operationLogsDao.logRechargeOperation(
                                operatorId,
                                operator ? operator.name : '系统',
                                memberId,
                                targetMember ? targetMember.name : '未知用户',
                                packageType,
                                actualAmount,
                                rechargeDetails
                            );
                        } catch (logError) {
                            console.error('记录操作日志失败:', logError);
                            // 日志记录失败不影响充值成功
                        }

                        resolve({
                            success: true,
                            message: '充值成功',
                            pointsGained: 0, // 充值者本人不获得积分
                            expireDate: expireDate
                        });

                    } catch (innerError) {
                        console.error('充值事务处理失败:', innerError);
                        try {
                            await this.runQuery('ROLLBACK');
                        } catch (rollbackError) {
                            console.error('事务回滚失败:', rollbackError);
                        }
                        reject(innerError);
                    }

                } catch (error) {
                    console.error('充值操作失败:', error);
                    reject(error);
                }
            } catch (error) {
                reject(error);
            }
        });
    }

    // 获取套餐信息
    getPackageInfo(packageType) {
        const packages = {
            'half_month': { price: 398, days: 15 },
            'one_month': { price: 698, days: 30 },
            'two_months': { price: 1298, days: 60 }
        };
        return packages[packageType] || null;
    }

    // 会员费用充值方法（使用事务管理器）
    async membershipRecharge(memberId, packageType, operatorId, pointsToUse = 0, pointsSourceMemberId = null) {
        console.log('开始会员费用充值:', { memberId, packageType, operatorId, pointsToUse, pointsSourceMemberId });
        
        // 验证套餐类型
        const packageInfo = this.getPackageInfo(packageType);
        if (!packageInfo) {
            throw new Error('无效的套餐类型');
        }

        // 验证积分使用（pointsToUse现在是积分抵扣金额，不是积分数量）
        if (pointsToUse > 0) {
            // 限制积分抵扣金额不能超过套餐价格
            const maxPointsDeductionAmount = Math.min(pointsToUse, packageInfo.price);
            if (pointsToUse > packageInfo.price) {
                console.log(`积分抵扣金额 ${pointsToUse} 超过套餐价格 ${packageInfo.price}，自动调整为 ${maxPointsDeductionAmount}`);
                pointsToUse = maxPointsDeductionAmount;
            }
            
            // 将积分抵扣金额转换为积分数量（1元=10积分）
            const pointsToDeduct = pointsToUse * 10;
            
            // 验证积分来源
            const sourceId = pointsSourceMemberId || memberId;
            const sourceMember = await memberDao.getMemberById(sourceId);
            if (!sourceMember) {
                throw new Error('积分来源用户不存在');
            }
            
            if (sourceMember.member_points < pointsToDeduct) {
                throw new Error('积分余额不足');
            }
        }

        // 获取会员信息
        const targetMember = await memberDao.getMemberById(memberId);
        if (!targetMember) {
            throw new Error('会员不存在');
        }

        // 计算实际支付金额
        const actualAmount = packageInfo.price - pointsToUse;

        // 计算新的到期日期
        const currentDate = new Date();
        const currentExpireDate = targetMember.expire_date ? new Date(targetMember.expire_date) : null;
        const currentEffectiveDate = targetMember.effective_date ? new Date(targetMember.effective_date) : null;
        
        let startDate = null;
        let newExpireDate;
        
        if (!currentExpireDate || currentExpireDate <= currentDate) {
            // 首次充值或已过期，从明天开始计算
            startDate = new Date(currentDate);
            startDate.setDate(startDate.getDate() + 1);
            startDate.setHours(0, 0, 0, 0);
            
            newExpireDate = new Date(startDate);
            newExpireDate.setDate(newExpireDate.getDate() + packageInfo.days - 1);
            newExpireDate.setHours(23, 59, 59, 999);
        } else {
            // 续费，从当前到期日期的下一天开始计算
            newExpireDate = new Date(currentExpireDate);
            newExpireDate.setDate(newExpireDate.getDate() + packageInfo.days);
        }

        console.log('日期计算结果:', {
            currentDate: toLocalTimeString(currentDate),
            currentExpireDate: currentExpireDate ? toLocalTimeString(currentExpireDate) : null,
            startDate: startDate ? toLocalTimeString(startDate) : null,
            newExpireDate: toLocalTimeString(newExpireDate),
            packageDays: packageInfo.days
        });

        // 使用事务管理器执行整个充值操作
        return await transactionManager.withTransaction(async (txId) => {
            console.log(`事务 ${txId} 已开始`);

            // 如果使用积分抵扣，先扣除积分
            if (pointsToUse > 0) {
                // 将积分抵扣金额转换为积分数量（1元=10积分）
                const pointsToDeduct = pointsToUse * 10;
                
                console.log('开始积分扣除:', { 
                    sourceId: pointsSourceMemberId || memberId, 
                    pointsDeductionAmount: pointsToUse,
                    pointsToDeduct: pointsToDeduct
                });
                
                const sourceId = pointsSourceMemberId || memberId;
                const sourceMember = await memberDao.getMemberById(sourceId);
                const currentBalance = sourceMember.member_points;
                const newBalance = currentBalance - pointsToDeduct;
                
                // 扣除积分
                await transactionManager.executeInTransaction(
                    txId,
                    'UPDATE members SET member_points = ?, updated_at = datetime(\'now\', \'localtime\') WHERE id = ? AND member_points >= ?',
                    [newBalance, sourceId, pointsToDeduct]
                );
                console.log(`已扣除 ${pointsToDeduct} 积分，抵扣金额 ${pointsToUse} 元`);

                // 记录积分变动
                await transactionManager.executeInTransaction(
                    txId,
                    'INSERT INTO points_records (member_id, points_type, operation_type, amount, balance_before, balance_after, reason, operator_id, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, datetime(\'now\', \'localtime\'))',
                    [sourceId, 'member', 'manual', -pointsToDeduct, currentBalance, newBalance, `充值使用积分抵扣，抵扣金额${pointsToUse}元`, operatorId]
                );
                console.log('积分变动记录已创建');
            }

            // 更新会员生效日期和到期日期
            if (startDate === null) {
                // 续费情况：只更新到期日期，不更新生效日期
                await transactionManager.executeInTransaction(
                    txId,
                    'UPDATE members SET expire_date = ?, updated_at = datetime(\'now\', \'localtime\') WHERE id = ?',
                    [toLocalTimeString(newExpireDate), memberId]
                );
            } else {
                // 首次充值或过期重新充值：更新生效日期和到期日期
                await transactionManager.executeInTransaction(
                    txId,
                    'UPDATE members SET effective_date = ?, expire_date = ?, updated_at = datetime(\'now\', \'localtime\') WHERE id = ?',
                    [toLocalTimeString(startDate), toLocalTimeString(newExpireDate), memberId]
                );
            }
            console.log('会员信息已更新');

            // 插入充值记录
            const result = await transactionManager.executeInTransaction(
                txId,
                'INSERT INTO recharge_records (member_id, package_type, total_amount, points_used, cash_amount, points_gained, expire_date, operator_id, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, datetime(\'now\', \'localtime\'))',
                [memberId, packageType, packageInfo.price, pointsToUse || 0, actualAmount, 0, toLocalTimeString(newExpireDate), operatorId]
            );
            const rechargeId = result.lastID;
            console.log('充值记录已创建, ID:', rechargeId);

            // 处理分层积分奖励（传递事务ID）
            console.log('开始处理分层积分奖励');
            let rewardResult;
            try {
                // 积分抵扣不影响分层奖励的发放，分层奖励基于套餐原价计算
                rewardResult = await this.processHierarchyRewards(memberId, packageInfo.price, operatorId, 'recharge', rechargeId, 'member', null, txId);
                console.log('分层积分奖励处理完成');
            } catch (rewardError) {
                console.error('分层积分奖励处理失败:', rewardError);
                // 分成失败不影响充值成功，设置默认结果
                rewardResult = { affectedMembers: [] };
            }

            console.log(`事务 ${txId} 即将提交`);

            return {
                success: true,
                message: '会员费用充值成功，权益将于明天生效',
                startDate: startDate || currentEffectiveDate,
                newExpireDate: newExpireDate,
                daysAdded: packageInfo.days,
                affectedMembers: rewardResult.affectedMembers || []
            };
        });
    }

    // 处理分层积分奖励（新版本，使用分成记录系统）
    async processHierarchyRewards(memberId, basePoints, operatorId, triggerType = 'recharge', triggerRecordId = null, pointsType = null, excludeMemberId = null, txId = null) {
        console.log('=== 进入 processHierarchyRewards ===');
        console.log('参数:', { memberId, basePoints, operatorId, triggerType, triggerRecordId, pointsType, excludeMemberId });

        try {
            // 获取源会员信息
            const sourceMember = await memberDao.getMemberById(memberId);
            console.log('源会员信息:', sourceMember);
            if (!sourceMember) {
                throw new Error('Source member not found');
            }

            // 获取levelRatiosDao实例
            const levelRatiosDao = require('./levelRatiosDao');

            let allCommissions = [];

            // 根据积分类型计算对应的分成
            console.log('开始计算分成, pointsType:', pointsType);
            if (pointsType === 'member') {
                // 只计算会员积分分成
                console.log('计算会员积分分成...');
                const memberCommissions = await levelRatiosDao.calculateUplineCommissions(memberId, basePoints, 'member');
                console.log('会员积分分成结果:', memberCommissions);
                allCommissions = memberCommissions;
            } else if (pointsType === 'sales') {
                // 只计算销售积分分成
                console.log('计算销售积分分成...');
                const salesCommissions = await levelRatiosDao.calculateUplineCommissions(memberId, basePoints, 'sales');
                console.log('销售积分分成结果:', salesCommissions);
                allCommissions = salesCommissions;
            } else {
                // 如果没有指定类型，默认为会员积分分成
                console.log('未指定积分类型，默认计算会员积分分成...');
                const memberCommissions = await levelRatiosDao.calculateUplineCommissions(memberId, basePoints, 'member');
                console.log('会员积分分成结果:', memberCommissions);
                allCommissions = memberCommissions;
            }

            console.log('所有分成记录:', allCommissions);

            const commissionRecords = [];

            // 根据传入的积分类型确定积分类型
            const commissionPointsType = pointsType || 'sales'; // 默认为销售积分
            
            // 处理每个分成
            for (const commission of allCommissions) {
                // 如果受益人是被排除的用户（如积分来源用户），跳过
                if (excludeMemberId && commission.memberId === excludeMemberId) {
                    console.log(`跳过被排除的用户: ${commission.memberId}`);
                    continue;
                }
                
                // 获取受益人当前积分
                const beneficiary = await memberDao.getMemberById(commission.memberId);
                if (!beneficiary) continue;

                const currentBalance = commissionPointsType === 'member' ? beneficiary.member_points : beneficiary.sales_points;
                const newBalance = currentBalance + commission.commission;

                // 更新受益人积分 - 根据是否有事务ID选择执行方式
                if (txId) {
                    // 使用事务管理器
                    if (commissionPointsType === 'member') {
                        await transactionManager.executeInTransaction(
                            txId,
                            'UPDATE members SET member_points = ?, updated_at = datetime(\'now\', \'localtime\') WHERE id = ?',
                            [newBalance, commission.memberId]
                        );
                    } else {
                        await transactionManager.executeInTransaction(
                            txId,
                            'UPDATE members SET sales_points = ?, updated_at = datetime(\'now\', \'localtime\') WHERE id = ?',
                            [newBalance, commission.memberId]
                        );
                    }

                    // 记录积分变动
                    await transactionManager.executeInTransaction(
                        txId,
                        'INSERT INTO points_records (member_id, points_type, operation_type, amount, balance_before, balance_after, reason, operator_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
                        [commission.memberId, commissionPointsType, 'auto', commission.commission, currentBalance, newBalance, `因${sourceMember.name}充值获得${commission.level}级分层奖励积分`, operatorId]
                    );
                } else {
                    // 使用传统的runQuery方法
                    if (commissionPointsType === 'member') {
                        await this.runQuery(`
                            UPDATE members
                            SET member_points = ?, updated_at = datetime('now', 'localtime')
                            WHERE id = ?
                        `, [newBalance, commission.memberId]);
                    } else {
                        await this.runQuery(`
                            UPDATE members
                            SET sales_points = ?, updated_at = datetime('now', 'localtime')
                            WHERE id = ?
                        `, [newBalance, commission.memberId]);
                    }

                    // 记录积分变动
                    await this.runQuery(`
                        INSERT INTO points_records
                        (member_id, points_type, operation_type, amount, balance_before, balance_after, reason, operator_id)
                        VALUES (?, ?, 'auto', ?, ?, ?, ?, ?)
                    `, [
                        commission.memberId, commissionPointsType, commission.commission, currentBalance, newBalance,
                        `因${sourceMember.name}充值获得${commission.level}级分层奖励积分`, operatorId
                    ]);
                }
                
                // 暂时跳过日志记录，避免事务嵌套问题
                // 日志记录将在事务提交后统一处理
                console.log(`分层奖励积分发放: ${commission.memberName} 获得 ${commission.commission} ${commissionPointsType}积分`);

                // 创建分成记录
                if (this.commissionDao) {
                    const commissionRecord = {
                        sourceMemberId: memberId,
                        sourceMemberName: sourceMember.name,
                        beneficiaryMemberId: commission.memberId,
                        beneficiaryMemberName: commission.memberName,
                        level: commission.level,
                        pointsType: commissionPointsType,
                        baseAmount: basePoints,
                        commissionRate: commission.percentage,
                        commissionAmount: commission.commission,
                        triggerType: triggerType,
                        triggerRecordId: triggerRecordId,
                        balanceBefore: currentBalance,
                        balanceAfter: newBalance
                    };

                    commissionRecords.push(commissionRecord);
                }
            }

            // 批量创建分成记录
            if (this.commissionDao && commissionRecords.length > 0) {
                await this.commissionDao.createBatchCommissionRecords(commissionRecords);
            }

            // 计算分成统计
            let totalMemberCommission = 0;
            let totalSalesCommission = 0;

            if (pointsType === 'sales') {
                totalSalesCommission = allCommissions.reduce((sum, c) => sum + c.commission, 0);
            } else {
                // 会员积分分成或未指定类型时默认为会员积分
                totalMemberCommission = allCommissions.reduce((sum, c) => sum + c.commission, 0);
            }

            // 收集受影响的会员信息（只包含实际处理的会员，排除被跳过的用户）
            const affectedMembers = [];
            for (const commission of allCommissions) {
                // 如果受益人是被排除的用户，跳过
                if (excludeMemberId && commission.memberId === excludeMemberId) {
                    continue;
                }
                
                const beneficiary = await memberDao.getMemberById(commission.memberId);
                if (beneficiary) {
                    affectedMembers.push({
                        id: beneficiary.id,
                        name: beneficiary.name,
                        level: commission.level,
                        commission: commission.commission,
                        pointsType: commissionPointsType
                    });
                }
            }

            return {
                success: true,
                commissionsProcessed: allCommissions.length,
                totalMemberCommission: totalMemberCommission,
                totalSalesCommission: totalSalesCommission,
                affectedMembers: affectedMembers
            };

        } catch (error) {
            console.error('Failed to process hierarchy rewards:', error);
            throw error;
        }
    }



    // 获取积分操作记录
    async getPointsRecords(memberId = null, pointsType = null, operationType = null, limit = 100) {
        return new Promise((resolve, reject) => {
            let sql = `
                SELECT
                    pr.id,
                    pr.member_id,
                    m.name as member_name,
                    pr.points_type,
                    pr.operation_type,
                    pr.amount,
                    pr.balance_before,
                    pr.balance_after,
                    pr.reason,
                    pr.operator_id,
                    a.name as operator_name,
                    pr.created_at
                FROM points_records pr
                LEFT JOIN members m ON pr.member_id = m.id
                LEFT JOIN admins a ON pr.operator_id = a.id
                WHERE 1=1
            `;

            const params = [];

            if (memberId) {
                sql += ' AND pr.member_id = ?';
                params.push(memberId);
            }

            if (pointsType) {
                sql += ' AND pr.points_type = ?';
                params.push(pointsType);
            }

            if (operationType) {
                sql += ' AND pr.operation_type = ?';
                params.push(operationType);
            }

            sql += ' ORDER BY pr.created_at DESC LIMIT ?';
            params.push(limit);

            this.getDb().all(sql, params, (err, rows) => {
                if (err) {
                    reject(err);
                } else {
                    resolve({ success: true, records: rows });
                }
            });
        });
    }

    // 辅助方法：Promise化的数据库运行
    runQuery(sql, params = []) {
        return new Promise((resolve, reject) => {
            const db = this.getDb();
            
            // 对于事务控制语句，使用exec方法
            if (sql.trim().toUpperCase().startsWith('BEGIN') || 
                sql.trim().toUpperCase().startsWith('COMMIT') || 
                sql.trim().toUpperCase().startsWith('ROLLBACK')) {
                db.exec(sql, (err) => {
                    if (err) {
                        console.error('Transaction command failed:', sql, err);
                        reject(err);
                    } else {
                        console.log('Transaction command executed:', sql);
                        resolve({ changes: 0, lastID: 0 });
                    }
                });
            } else {
                // 对于普通SQL语句，使用run方法
                db.run(sql, params, function(err) {
                    if (err) {
                        console.error('SQL execution failed:', sql, params, err);
                        reject(err);
                    } else {
                        resolve(this);
                    }
                });
            }
        });
    }
}

module.exports = new PointsDao();
