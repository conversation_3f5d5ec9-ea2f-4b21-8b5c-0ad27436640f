const bcrypt = require('bcryptjs');
const database = require('./db');

class AdminDao {
    constructor() {
        // 不再缓存数据库实例
    }

    // 获取数据库实例
    getDb() {
        return database.getDb();
    }

    // 管理员登录验证
    async login(username, password) {
        return new Promise((resolve, reject) => {
            const sql = `
                SELECT id, username, password, name, role_level, can_create_admin, is_active
                FROM admins
                WHERE username = ? AND is_active = 1
            `;

            this.getDb().get(sql, [username], async (err, row) => {
                if (err) {
                    reject(err);
                    return;
                }

                if (!row) {
                    resolve({ success: false, message: '用户名不存在' });
                    return;
                }

                try {
                    const isValidPassword = await bcrypt.compare(password, row.password);
                    if (isValidPassword) {
                        // 更新最后登录时间
                        await this.updateLastLogin(row.id);

                        resolve({
                            success: true,
                            admin: {
                                id: row.id,
                                username: row.username,
                                name: row.name,
                                roleLevel: row.role_level !== null && row.role_level !== undefined ? row.role_level : 2,
                                canCreateAdmin: row.can_create_admin || 0,
                                permissions: this.getPermissionsByRole(row.role_level !== null && row.role_level !== undefined ? row.role_level : 2)
                            }
                        });
                    } else {
                        resolve({ success: false, message: '密码错误' });
                    }
                } catch (error) {
                    reject(error);
                }
            });
        });
    }

    // 更新最后登录时间
    async updateLastLogin(adminId) {
        return new Promise((resolve, reject) => {
            const sql = `
                UPDATE admins 
                SET last_login_at = CURRENT_TIMESTAMP 
                WHERE id = ?
            `;
            
            this.getDb().run(sql, [adminId], (err) => {
                if (err) {
                    reject(err);
                } else {
                    resolve();
                }
            });
        });
    }

    // 修改密码
    async changePassword(adminId, oldPassword, newPassword) {
        return new Promise((resolve, reject) => {
            // 验证旧密码
            const sql = `SELECT password FROM admins WHERE id = ?`;
            
            this.getDb().get(sql, [adminId], async (err, row) => {
                if (err) {
                    reject(err);
                    return;
                }

                if (!row) {
                    resolve({ success: false, message: '管理员不存在' });
                    return;
                }

                try {
                    const isValidPassword = await bcrypt.compare(oldPassword, row.password);
                    if (!isValidPassword) {
                        resolve({ success: false, message: '原密码错误' });
                        return;
                    }

                    // 加密新密码
                    const hashedNewPassword = await bcrypt.hash(newPassword, 10);

                    // 更新密码
                    const updateSql = `UPDATE admins SET password = ? WHERE id = ?`;
                    this.getDb().run(updateSql, [hashedNewPassword, adminId], (err) => {
                        if (err) {
                            reject(err);
                        } else {
                            resolve({ success: true, message: '密码修改成功' });
                        }
                    });
                } catch (error) {
                    reject(error);
                }
            });
        });
    }

    // 根据角色级别获取权限
    getPermissionsByRole(roleLevel) {
        const permissions = {
            canViewMembers: true,
            canManageMembers: true,
            canAdjustPoints: true,
            canRecharge: true,
            canViewHierarchy: true,
            canViewRecords: true,
            canViewCommissionStats: true,
            canViewPointsConfig: false,
            canCreateAdmin: false,
            canManageAdmins: false,
            canViewSystemSettings: false
        };

        if (roleLevel === 0) {
            // 超级超级管理员：所有权限
            return {
                ...permissions,
                canViewPointsConfig: true,
                canCreateAdmin: true,
                canManageAdmins: true,
                canViewSystemSettings: true
            };
        } else if (roleLevel === 1) {
            // 超级管理员：除积分配置外的所有权限
            return {
                ...permissions,
                canCreateAdmin: true,
                canManageAdmins: true,
                canViewSystemSettings: true
            };
        } else {
            // 普通管理员：基础权限
            return permissions;
        }
    }

    // 获取管理员信息
    async getAdminById(adminId) {
        return new Promise((resolve, reject) => {
            const sql = `
                SELECT id, username, name, role_level, can_create_admin, created_at, last_login_at
                FROM admins
                WHERE id = ? AND is_active = 1
            `;

            this.getDb().get(sql, [adminId], (err, row) => {
                if (err) {
                    reject(err);
                } else {
                    if (row) {
                        // 统一字段名：将role_level转换为roleLevel以保持前后端一致
                        const roleLevel = row.role_level !== null && row.role_level !== undefined ? row.role_level : 2;
                        row.roleLevel = roleLevel;
                        row.permissions = this.getPermissionsByRole(roleLevel);
                    }
                    resolve(row);
                }
            });
        });
    }

    // 根据ID获取管理员信息（包括被禁用的）
    async getAdminByIdIncludeInactive(adminId) {
        return new Promise((resolve, reject) => {
            const sql = `
                SELECT id, username, name, role_level, can_create_admin, created_at, last_login_at, is_active
                FROM admins
                WHERE id = ?
            `;

            this.getDb().get(sql, [adminId], (err, row) => {
                if (err) {
                    reject(err);
                } else {
                    if (row) {
                        row.permissions = this.getPermissionsByRole(row.role_level !== null && row.role_level !== undefined ? row.role_level : 2);
                    }
                    resolve(row);
                }
            });
        });
    }

    // 获取所有管理员列表（过滤掉超级超级管理员）
    async getAllAdmins(includeHidden = false) {
        return new Promise((resolve, reject) => {
            let sql = `
                SELECT
                    a.id,
                    a.username,
                    a.name,
                    a.role_level,
                    a.can_create_admin,
                    a.created_by,
                    c.name as creator_name,
                    a.created_at,
                    a.last_login_at,
                    a.is_active
                FROM admins a
                LEFT JOIN admins c ON a.created_by = c.id
            `;

            // 默认过滤掉超级超级管理员（role_level = 0）
            if (!includeHidden) {
                sql += ' WHERE a.role_level > 0 OR a.role_level IS NULL';
            }

            sql += ' ORDER BY a.created_at DESC';

            this.getDb().all(sql, [], (err, rows) => {
                if (err) {
                    reject(err);
                } else {
                    // 添加权限信息
                    const adminsWithPermissions = rows.map(admin => ({
                        ...admin,
                        permissions: this.getPermissionsByRole(admin.role_level !== null && admin.role_level !== undefined ? admin.role_level : 2),
                        roleName: this.getRoleName(admin.role_level !== null && admin.role_level !== undefined ? admin.role_level : 2)
                    }));
                    resolve(adminsWithPermissions);
                }
            });
        });
    }

    // 获取角色名称
    getRoleName(roleLevel) {
        switch (roleLevel) {
            case 0: return '超超管理员';
            case 1: return '超级管理员';
            case 2: return '普通管理员';
            default: return '普通管理员';
        }
    }

    // 创建新管理员
    async createAdmin(username, password, name, roleLevel = 2, creatorId) {
        return new Promise((resolve, reject) => {
            // 先验证创建者权限
            this.getAdminById(creatorId).then(creator => {
                if (!creator) {
                    resolve({ success: false, message: '创建者不存在' });
                    return;
                }

                // 权限验证
                if (!creator.can_create_admin) {
                    resolve({ success: false, message: '没有创建管理员的权限' });
                    return;
                }

                // 强制只能创建普通管理员
                if (roleLevel !== 2) {
                    console.log(`创建者 ${creator.username} 尝试创建角色级别 ${roleLevel} 的管理员，强制改为普通管理员`);
                    roleLevel = 2;
                }

                // 检查用户名是否已存在
                const checkSql = `SELECT COUNT(*) as count FROM admins WHERE username = ?`;

                this.getDb().get(checkSql, [username], async (err, row) => {
                    if (err) {
                        reject(err);
                        return;
                    }

                    if (row.count > 0) {
                        resolve({ success: false, message: '用户名已存在' });
                        return;
                    }

                    try {
                        // 加密密码
                        const hashedPassword = await bcrypt.hash(password, 10);

                        // 普通管理员不能创建其他管理员
                        const canCreateAdmin = 0;

                        // 插入新管理员
                        const insertSql = `
                            INSERT INTO admins (username, password, name, role_level, can_create_admin, created_by)
                            VALUES (?, ?, ?, ?, ?, ?)
                        `;

                        this.getDb().run(insertSql, [username, hashedPassword, name, roleLevel, canCreateAdmin, creatorId], function(err) {
                            if (err) {
                                reject(err);
                            } else {
                                resolve({
                                    success: true,
                                    message: '普通管理员创建成功',
                                    adminId: this.lastID
                                });
                            }
                        });
                    } catch (error) {
                        reject(error);
                    }
                });
            }).catch(reject);
        });
    }

    // 禁用/启用管理员
    async toggleAdminStatus(adminId, isActive, operatorId) {
        return new Promise((resolve, reject) => {
            // 验证操作者权限
            this.getAdminById(operatorId).then(operator => {
                if (!operator || !operator.permissions.canManageAdmins) {
                    resolve({ success: false, message: '没有管理管理员的权限' });
                    return;
                }

                // 不能操作超级超级管理员
                this.getAdminById(adminId).then(targetAdmin => {
                    if (targetAdmin && targetAdmin.role_level === 0) {
                        resolve({ success: false, message: '不能操作超级超级管理员' });
                        return;
                    }

                    // 不能操作同级或更高级别的管理员
                    if (targetAdmin && operator.role_level >= targetAdmin.role_level) {
                        resolve({ success: false, message: '不能操作同级或更高级别的管理员' });
                        return;
                    }

                    const sql = `UPDATE admins SET is_active = ? WHERE id = ?`;

                    this.getDb().run(sql, [isActive ? 1 : 0, adminId], (err) => {
                        if (err) {
                            reject(err);
                        } else {
                            resolve({
                                success: true,
                                message: isActive ? '管理员已启用' : '管理员已禁用'
                            });
                        }
                    });
                }).catch(reject);
            }).catch(reject);
        });
    }

    // 验证管理员权限
    async checkPermission(adminId, permission) {
        try {
            const admin = await this.getAdminById(adminId);
            if (!admin) {
                return false;
            }
            return admin.permissions[permission] || false;
        } catch (error) {
            console.error('权限验证失败:', error);
            return false;
        }
    }

    // 验证是否可以访问积分配置
    async canAccessPointsConfig(adminId) {
        return this.checkPermission(adminId, 'canViewPointsConfig');
    }

    // 验证是否可以创建管理员
    async canCreateAdmin(adminId) {
        return this.checkPermission(adminId, 'canCreateAdmin');
    }

    // 获取所有超级管理员的ID列表
    async getSuperAdmins() {
        return new Promise((resolve, reject) => {
            const sql = `SELECT id FROM admins WHERE role_level = 1 AND is_active = 1`;
            
            this.getDb().all(sql, [], (err, rows) => {
                if (err) {
                    reject(err);
                } else {
                    const superAdminIds = rows.map(row => row.id);
                    resolve(superAdminIds);
                }
            });
        });
    }

    // 获取所有超超管理员的ID列表
    async getSuperSuperAdmins() {
        return new Promise((resolve, reject) => {
            const sql = `SELECT id FROM admins WHERE role_level = 0 AND is_active = 1`;
            
            this.getDb().all(sql, [], (err, rows) => {
                if (err) {
                    reject(err);
                } else {
                    const superSuperAdminIds = rows.map(row => row.id);
                    resolve(superSuperAdminIds);
                }
            });
        });
    }

    // 删除管理员
    async deleteAdmin(adminId, operatorId) {
        return new Promise((resolve, reject) => {
            // 先验证操作者权限
            this.getAdminById(operatorId).then(operator => {
                if (!operator || !operator.permissions.canManageAdmins) {
                    resolve({ success: false, message: '没有管理管理员的权限' });
                    return;
                }

                // 获取目标管理员信息
                this.getAdminByIdIncludeInactive(adminId).then(targetAdmin => {
                    if (!targetAdmin) {
                        resolve({ success: false, message: '管理员不存在' });
                        return;
                    }

                    // 不能删除超超管理员
                    if (targetAdmin.role_level === 0) {
                        resolve({ success: false, message: '不能删除超超管理员' });
                        return;
                    }

                    // 不能删除自己
                    if (adminId === operatorId) {
                        resolve({ success: false, message: '不能删除自己' });
                        return;
                    }

                    // 超级管理员不能删除其他超级管理员
                    if (operator.role_level === 1 && targetAdmin.role_level === 1) {
                        resolve({ success: false, message: '不能删除同级管理员' });
                        return;
                    }

                    // 只能删除普通管理员
                    if (targetAdmin.role_level !== 2) {
                        resolve({ success: false, message: '只能删除普通管理员' });
                        return;
                    }

                    // 在删除前处理外键约束问题：将该管理员的操作记录转移给当前操作的管理员
                    // 转移操作记录
                    const transferSql = `UPDATE operation_logs SET operator_id = ?, operator_name = ? WHERE operator_id = ?`;
                    
                    this.getDb().run(transferSql, [operatorId, operator.name, adminId], (transferErr) => {
                        if (transferErr) {
                            console.error('转移操作记录失败:', transferErr);
                            reject(transferErr);
                            return;
                        }

                        console.log(`已将管理员 ${targetAdmin.name}(ID:${adminId}) 的操作记录转移给 ${operator.name}(ID:${operatorId})`);

                        // 现在可以安全删除管理员
                        const deleteSql = `DELETE FROM admins WHERE id = ?`;

                        this.getDb().run(deleteSql, [adminId], (deleteErr) => {
                            if (deleteErr) {
                                reject(deleteErr);
                            } else {
                                resolve({
                                    success: true,
                                    message: '管理员删除成功，相关操作记录已转移给当前操作员'
                                });
                            }
                        });
                    });
                }).catch(reject);
            }).catch(reject);
        });
    }

    // 重置其他管理员密码（仅超超管理员可用）
    async resetAdminPassword(targetAdminId, newPassword, operatorId) {
        const logger = require('../utils/logger');
        
        return new Promise((resolve, reject) => {
            logger.info('resetAdminPassword 开始执行:', { targetAdminId, operatorId, hasPassword: !!newPassword });
            
            // 先验证操作者权限
            this.getAdminById(operatorId).then(operator => {
                logger.info('获取操作者信息结果:', { hasOperator: !!operator, operatorId });
                
                if (!operator) {
                    logger.error('操作者不存在:', { operatorId });
                    resolve({ success: false, message: '操作者不存在' });
                    return;
                }

                // 只有超超管理员才能重置他人密码
                const operatorRoleLevel = operator.role_level !== null && operator.role_level !== undefined ? operator.role_level : 2;
                logger.info('操作者权限检查:', { operatorRoleLevel, operatorName: operator.name });
                
                if (operatorRoleLevel !== 0) {
                    logger.error('权限不足:', { operatorRoleLevel, operatorName: operator.name });
                    resolve({ success: false, message: '只有超超管理员才能重置他人密码' });
                    return;
                }

                // 获取目标管理员信息
                this.getAdminById(targetAdminId).then(targetAdmin => {
                    logger.info('获取目标管理员信息结果:', { hasTargetAdmin: !!targetAdmin, targetAdminId });
                    
                    if (!targetAdmin) {
                        logger.error('目标管理员不存在:', { targetAdminId });
                        resolve({ success: false, message: '目标管理员不存在' });
                        return;
                    }

                    // 不能重置自己的密码（应该使用修改密码功能）
                    if (targetAdminId === operatorId) {
                        logger.error('尝试重置自己的密码:', { targetAdminId, operatorId });
                        resolve({ success: false, message: '不能重置自己的密码，请使用修改密码功能' });
                        return;
                    }

                    logger.info('开始加密新密码');
                    // 加密新密码
                    bcrypt.hash(newPassword, 10).then(hashedPassword => {
                        logger.info('密码加密完成，开始更新数据库');
                        // 更新密码
                        const sql = `UPDATE admins SET password = ? WHERE id = ?`;
                        this.getDb().run(sql, [hashedPassword, targetAdminId], (err) => {
                            if (err) {
                                logger.error('数据库更新失败:', err);
                                reject(err);
                            } else {
                                logger.info('密码重置成功:', { targetAdminName: targetAdmin.name });
                                resolve({
                                    success: true,
                                    message: `已成功重置管理员 ${targetAdmin.name} 的密码`
                                });
                            }
                        });
                    }).catch(err => {
                        logger.error('密码加密失败:', err);
                        reject(err);
                    });
                }).catch(err => {
                    logger.error('获取目标管理员信息失败:', err);
                    reject(err);
                });
            }).catch(err => {
                logger.error('获取操作者信息失败:', err);
                reject(err);
            });
        });
    }
}

module.exports = new AdminDao();
