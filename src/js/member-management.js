// 会员管理系统主界面逻辑
class MemberManagement {
    constructor() {
        this.currentAdmin = null;
        this.members = [];
        this.allMembers = []; // 存储所有会员数据
        this.originalAllMembers = []; // 存储完整的会员数据（用于层级关系图）
        this.currentTab = 'members';
        this.operationLogs = [];
        this.currentPage = 1;
        this.pageSize = 10;
        this.totalMembersCount = 0; // 会员总数
        this.isSearching = false; // 是否处于搜索状态
        this.currentLogsPage = 1;
        this.currentLogsFilters = {};
        this.totalLogsCount = 0;
        this.selectedTreeNodeId = null; // 当前选中的树节点ID

        // 分成统计相关属性
        this.commissionCurrentPage = 1;
        this.commissionPageSize = 10;
        this.commissionTotalCount = 0;
        this.commissionFilters = {};
        
        // 分成排行榜分页相关
        this.rankingCurrentPage = 1;
        this.rankingPageSize = 10;
        this.rankingTotalCount = 0;

        this.init();
    }

    // 格式化积分数值，保留一位小数
    formatPoints(points) {
        if (points === null || points === undefined) {
            return '0.0';
        }
        return parseFloat(points).toFixed(1);
    }

    // 时区处理工具函数 - 统一使用东八区时间
    formatDateTimeWithTimezone(dateTimeString, options = {}) {
        if (!dateTimeString) {
            return '-';
        }

        try {
            const date = new Date(dateTimeString);
            if (isNaN(date.getTime())) {
                return '-';
            }

            // 默认选项：东八区时间，中文格式
            const defaultOptions = {
                timeZone: 'Asia/Shanghai',
                locale: 'zh-CN',
                ...options
            };

            return date.toLocaleString(defaultOptions.locale, {
                timeZone: defaultOptions.timeZone,
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            });
        } catch (error) {
            console.error('格式化日期时间失败:', error);
            return '-';
        }
    }

    // 格式化日期（仅日期部分）- 东八区
    formatDateWithTimezone(dateTimeString, options = {}) {
        if (!dateTimeString) {
            return '-';
        }

        try {
            const date = new Date(dateTimeString);
            if (isNaN(date.getTime())) {
                return '-';
            }

            const defaultOptions = {
                timeZone: 'Asia/Shanghai',
                locale: 'zh-CN',
                ...options
            };

            return date.toLocaleDateString(defaultOptions.locale, {
                timeZone: defaultOptions.timeZone,
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
            });
        } catch (error) {
            console.error('格式化日期失败:', error);
            return '-';
        }
    }

    // 检查会员是否在有效期内
    isMemberActive(member) {
        if (!member) return false;

        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

        // 检查是否有生效日期和到期日期
        if (!member.effective_date || !member.expire_date) {
            return false; // 未购买会员或数据不完整
        }

        const effectiveDate = new Date(member.effective_date);
        const expireDate = new Date(member.expire_date);

        // 检查日期是否有效
        if (isNaN(effectiveDate.getTime()) || isNaN(expireDate.getTime())) {
            return false;
        }

        const effectiveDay = new Date(effectiveDate.getFullYear(), effectiveDate.getMonth(), effectiveDate.getDate());
        const expireDay = new Date(expireDate.getFullYear(), expireDate.getMonth(), expireDate.getDate());

        // 当前日期必须在生效日期和到期日期之间（包含边界）
        return today >= effectiveDay && today <= expireDay;
    }

    // 获取会员状态信息
    getMemberStatusInfo(member) {
        if (!member) {
            return { isActive: false, status: 'unknown', message: '会员信息不存在' };
        }

        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

        // 未购买会员
        if (!member.effective_date || !member.expire_date) {
            return {
                isActive: false,
                status: 'not_purchased',
                message: '未开通会员',
                canUsePoints: false
            };
        }

        const effectiveDate = new Date(member.effective_date);
        const expireDate = new Date(member.expire_date);

        // 检查日期是否有效
        if (isNaN(effectiveDate.getTime()) || isNaN(expireDate.getTime())) {
            return {
                isActive: false,
                status: 'invalid_date',
                message: '会员日期数据异常',
                canUsePoints: false
            };
        }

        const effectiveDay = new Date(effectiveDate.getFullYear(), effectiveDate.getMonth(), effectiveDate.getDate());
        const expireDay = new Date(expireDate.getFullYear(), expireDate.getMonth(), expireDate.getDate());

        // 未生效
        if (today < effectiveDay) {
            return {
                isActive: false,
                status: 'not_effective',
                message: `会员将于 ${this.formatDateWithTimezone(member.effective_date)} 生效`,
                canUsePoints: false
            };
        }

        // 已过期
        if (today > expireDay) {
            return {
                isActive: false,
                status: 'expired',
                message: `会员已于 ${this.formatDateWithTimezone(member.expire_date)} 过期`,
                canUsePoints: false
            };
        }

        // 有效期内
        return {
            isActive: true,
            status: 'active',
            message: `会员有效期至 ${this.formatDateWithTimezone(member.expire_date)}`,
            canUsePoints: true
        };
    }

    async init() {
        // 检查登录状态
        if (!this.checkLoginStatus()) {
            window.location.href = 'login.html';
            return;
        }

        // 初始化权限控制
        this.initPermissionControl();

        // 初始化界面
        this.initUI();

        // 绑定事件
        this.bindEvents();
        this.bindOperationLogsEvents();
        this.bindRechargeModalEvents();

        // 加载数据
        await this.loadMembers(1);
    }

    checkLoginStatus() {
        const isLoggedIn = localStorage.getItem('isLoggedIn');
        const adminData = localStorage.getItem('currentAdmin');

        // 强制检查登录状态，禁用自动登录
        if (isLoggedIn !== 'true' || !adminData) {
            // 清除可能残留的登录状态
            localStorage.removeItem('isLoggedIn');
            localStorage.removeItem('currentAdmin');
            return false;
        }

        try {
            this.currentAdmin = JSON.parse(adminData);
            return true;
        } catch (error) {
            console.error('解析管理员数据失败:', error);
            // 清除无效的登录状态
            localStorage.removeItem('isLoggedIn');
            localStorage.removeItem('currentAdmin');
            return false;
        }
    }

    // 初始化权限控制
    initPermissionControl() {
        if (!this.currentAdmin) {
            console.warn('管理员信息不存在');
            return;
        }

        if (!this.currentAdmin.permissions) {
            // 如果没有权限信息，根据角色级别生成默认权限
            this.currentAdmin.permissions = this.getPermissionsByRole(this.currentAdmin.roleLevel || 2);
        }

        const permissions = this.currentAdmin.permissions;

        // 控制导航标签页显示
        this.controlTabVisibility(permissions);

        // 控制功能按钮显示
        this.controlButtonVisibility(permissions);

        // 显示管理员信息和权限级别
        this.displayAdminInfo();
    }

    // 根据角色级别获取权限（前端版本）
    getPermissionsByRole(roleLevel) {
        const permissions = {
            canViewMembers: true,
            canManageMembers: true,
            canAdjustPoints: true,
            canRecharge: true,
            canViewHierarchy: true,
            canViewRecords: true,
            canViewCommissionStats: true,
            canViewPointsConfig: false,
            canCreateAdmin: false,
            canManageAdmins: false,
            canViewSystemSettings: false
        };

        if (roleLevel === 0) {
            // 超超管理员：所有权限
            return {
                ...permissions,
                canViewPointsConfig: true,
                canCreateAdmin: true,
                canManageAdmins: true,
                canViewSystemSettings: true
            };
        } else if (roleLevel === 1) {
            // 超级管理员：除积分配置外的所有权限
            return {
                ...permissions,
                canCreateAdmin: true,
                canManageAdmins: true,
                canViewSystemSettings: true
            };
        } else {
            // 普通管理员：基础权限
            return permissions;
        }
    }

    // 控制标签页可见性
    controlTabVisibility(permissions) {
        // 积分配置标签页只对超超管理员可见
        const pointsConfigTab = document.querySelector('[data-tab="points-config"]');
        if (pointsConfigTab) {
            if (permissions.canViewPointsConfig) {
                pointsConfigTab.style.display = '';
                pointsConfigTab.style.removeProperty('display');
            } else {
                pointsConfigTab.style.display = 'none';
            }
        }

        // 管理员管理标签页
        const adminManageTab = document.querySelector('[data-tab="admin-manage"]');
        if (adminManageTab) {
            if (permissions.canManageAdmins) {
                adminManageTab.style.display = '';
                adminManageTab.style.removeProperty('display');
            } else {
                adminManageTab.style.display = 'none';
            }
        }
    }

    // 控制按钮可见性
    controlButtonVisibility(permissions) {
        // 控制新增管理员按钮
        const addAdminBtn = document.getElementById('addAdminBtn');
        if (addAdminBtn) {
            if (permissions.canCreateAdmin) {
                addAdminBtn.style.display = '';
            } else {
                addAdminBtn.style.display = 'none';
            }
        }
    }

    // 显示管理员信息
    displayAdminInfo() {
        const adminNameElement = document.getElementById('adminName');
        if (adminNameElement && this.currentAdmin) {
            const roleText = this.getRoleDisplayText(this.currentAdmin.roleLevel);
            adminNameElement.textContent = `${this.currentAdmin.name} (${roleText})`;
        }
    }

    // 获取角色显示文本
    getRoleDisplayText(roleLevel) {
        switch (roleLevel) {
            case 0: return '超超管理员';
            case 1: return '超级管理员';
            case 2: return '普通管理员';
            default: return '管理员';
        }
    }

    initUI() {
        // 显示管理员名称
        const adminNameElement = document.getElementById('adminName');
        if (adminNameElement && this.currentAdmin) {
            adminNameElement.textContent = this.currentAdmin.name || this.currentAdmin.username;
        }

        // 初始化标签页
        this.initializeTab();
    }

    bindEvents() {
        // 设置按钮
        const settingsBtn = document.getElementById('settingsBtn');
        if (settingsBtn) {
            settingsBtn.addEventListener('click', () => {
                this.showChangePasswordModal();
            });
        }

        // 退出登录按钮
        const logoutBtn = document.getElementById('logoutBtn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', () => {
                this.handleLogout();
            });
        }

        // 标签页切换
        const navTabs = document.querySelectorAll('.nav-tab');
        navTabs.forEach(tab => {
            tab.addEventListener('click', (e) => {
                const tabName = e.currentTarget.dataset.tab;
                this.showTab(tabName);
            });
        });

        // 管理员管理相关事件
        this.bindAdminManageEvents();

        // 新增会员按钮
        const addMemberBtn = document.getElementById('addMemberBtn');
        if (addMemberBtn) {
            addMemberBtn.addEventListener('click', () => {
                this.showAddMemberModal();
            });
        }

        // 导出Excel按钮
        const exportExcelBtn = document.getElementById('exportExcelBtn');
        if (exportExcelBtn) {
            exportExcelBtn.addEventListener('click', () => {
                this.exportMembersToExcel();
            });
        }

        // 搜索功能
        const searchBtn = document.getElementById('searchBtn');
        const memberSearch = document.getElementById('memberSearch');
        if (searchBtn) {
            searchBtn.addEventListener('click', () => {
                this.searchMembers();
            });
        }
        if (memberSearch) {
            // 回车键搜索
            memberSearch.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.searchMembers();
                }
            });
            
            // 实时搜索 - 输入时触发
            let searchTimeout;
            memberSearch.addEventListener('input', (e) => {
                // 清除之前的定时器
                if (searchTimeout) {
                    clearTimeout(searchTimeout);
                }
                
                // 设置新的定时器，延迟300ms执行搜索，避免频繁搜索
                searchTimeout = setTimeout(() => {
                    this.searchMembers();
                }, 300);
            });
        }

        // 新增会员模态框关闭
        const closeAddModal = document.getElementById('closeAddModal');
        const cancelAddBtn = document.getElementById('cancelAddBtn');
        if (closeAddModal) {
            closeAddModal.addEventListener('click', () => {
                this.hideAddMemberModal();
            });
        }
        if (cancelAddBtn) {
            cancelAddBtn.addEventListener('click', () => {
                this.hideAddMemberModal();
            });
        }

        // 编辑会员模态框关闭
        const closeEditModal = document.getElementById('closeEditModal');
        const cancelEditBtn = document.getElementById('cancelEditBtn');
        if (closeEditModal) {
            closeEditModal.addEventListener('click', () => {
                this.hideEditMemberModal();
            });
        }
        if (cancelEditBtn) {
            cancelEditBtn.addEventListener('click', () => {
                this.hideEditMemberModal();
            });
        }

        // 新增会员表单提交
        const addMemberForm = document.getElementById('addMemberForm');
        if (addMemberForm) {
            addMemberForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleAddMember();
            });
        }

        // 编辑会员表单提交
        const editMemberForm = document.getElementById('editMemberForm');
        if (editMemberForm) {
            editMemberForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleEditMember();
            });
        }

        // 上级会员选择变化事件 - 添加会员
        const parentMemberSelect = document.getElementById('parentMember');
        if (parentMemberSelect) {
            parentMemberSelect.addEventListener('change', (e) => {
                const selectedParentId = e.target.value ? parseInt(e.target.value) : null;
                this.showHierarchyPreview(selectedParentId, 'addMemberHierarchyPreview');
            });
        }

        // 上级会员选择变化事件 - 编辑会员
        const editParentMemberSelect = document.getElementById('editParentMember');
        if (editParentMemberSelect) {
            editParentMemberSelect.addEventListener('change', (e) => {
                const selectedParentId = e.target.value ? parseInt(e.target.value) : null;
                this.showHierarchyPreview(selectedParentId, 'editMemberHierarchyPreview');
            });
        }

        // 层级关系树按钮事件
        const toggleCurrentBtn = document.getElementById('toggleCurrentBtn');
        const expandAllBtn = document.getElementById('expandAllBtn');
        const collapseAllBtn = document.getElementById('collapseAllBtn');
        const refreshTreeBtn = document.getElementById('refreshTreeBtn');

        if (toggleCurrentBtn) {
            toggleCurrentBtn.addEventListener('click', () => {
                this.toggleCurrentTreeNode();
            });
        }

        if (expandAllBtn) {
            expandAllBtn.addEventListener('click', () => {
                this.expandAllTreeNodes();
            });
        }

        if (collapseAllBtn) {
            collapseAllBtn.addEventListener('click', () => {
                this.collapseAllTreeNodes();
            });
        }

        if (refreshTreeBtn) {
            refreshTreeBtn.addEventListener('click', () => {
                this.buildMemberTree();
            });
        }

        // 层级关系搜索功能
        const hierarchySearchInput = document.getElementById('hierarchySearchInput');
        const searchHierarchyBtn = document.getElementById('searchHierarchyBtn');
        const clearSearchBtn = document.getElementById('clearSearchBtn');

        if (hierarchySearchInput) {
            // 实时搜索
            hierarchySearchInput.addEventListener('input', (e) => {
                const searchTerm = e.target.value.trim();
                if (searchTerm) {
                    this.searchInHierarchy(searchTerm);
                } else {
                    // 搜索框为空时，收起所有节点
                    this.clearHierarchySearch();
                }
            });

            // 回车搜索
            hierarchySearchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    const searchTerm = e.target.value.trim();
                    if (searchTerm) {
                        this.searchInHierarchy(searchTerm);
                    } else {
                        this.clearHierarchySearch();
                    }
                }
            });
        }

        if (searchHierarchyBtn) {
            searchHierarchyBtn.addEventListener('click', () => {
                if (hierarchySearchInput) {
                    const searchTerm = hierarchySearchInput.value.trim();
                    this.searchInHierarchy(searchTerm);
                }
            });
        }

        if (clearSearchBtn) {
            clearSearchBtn.addEventListener('click', () => {
                if (hierarchySearchInput) {
                    hierarchySearchInput.value = '';
                    this.clearHierarchySearch();
                }
            });
        }

        // 右键菜单事件
        this.bindContextMenuEvents();

        // 模态框背景点击关闭
        const addMemberModal = document.getElementById('addMemberModal');
        if (addMemberModal) {
            addMemberModal.addEventListener('click', (e) => {
                if (e.target === addMemberModal) {
                    this.hideAddMemberModal();
                }
            });
        }

        const editMemberModal = document.getElementById('editMemberModal');
        if (editMemberModal) {
            editMemberModal.addEventListener('click', (e) => {
                if (e.target === editMemberModal) {
                    this.hideEditMemberModal();
                }
            });
        }

        // 删除确认对话框事件
        const closeDeleteModal = document.getElementById('closeDeleteModal');
        const cancelDeleteBtn = document.getElementById('cancelDeleteBtn');
        const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');

        if (closeDeleteModal) {
            closeDeleteModal.addEventListener('click', () => {
                this.hideDeleteConfirmModal();
            });
        }

        if (cancelDeleteBtn) {
            cancelDeleteBtn.addEventListener('click', () => {
                this.hideDeleteConfirmModal();
            });
        }

        if (confirmDeleteBtn) {
            confirmDeleteBtn.addEventListener('click', () => {
                this.confirmDeleteMember();
            });
        }

        const deleteConfirmModal = document.getElementById('deleteConfirmModal');
        if (deleteConfirmModal) {
            deleteConfirmModal.addEventListener('click', (e) => {
                if (e.target === deleteConfirmModal) {
                    this.hideDeleteConfirmModal();
                }
            });
        }

        // 修改密码相关事件
        const closePasswordModal = document.getElementById('closePasswordModal');
        const cancelPasswordBtn = document.getElementById('cancelPasswordBtn');
        const changePasswordForm = document.getElementById('changePasswordForm');

        if (closePasswordModal) {
            closePasswordModal.addEventListener('click', () => {
                this.hideChangePasswordModal();
            });
        }

        if (cancelPasswordBtn) {
            cancelPasswordBtn.addEventListener('click', () => {
                this.hideChangePasswordModal();
            });
        }

        if (changePasswordForm) {
            changePasswordForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleChangePassword();
            });
        }

        // 密码显示/隐藏切换事件
        const toggleCurrentPassword = document.getElementById('toggleCurrentPassword');
        const toggleNewPassword = document.getElementById('toggleNewPassword');
        const toggleConfirmPassword = document.getElementById('toggleConfirmPassword');

        if (toggleCurrentPassword) {
            toggleCurrentPassword.addEventListener('click', () => {
                this.togglePasswordVisibility('currentPassword', 'toggleCurrentPassword');
            });
        }

        if (toggleNewPassword) {
            toggleNewPassword.addEventListener('click', () => {
                this.togglePasswordVisibility('newPassword', 'toggleNewPassword');
            });
        }

        if (toggleConfirmPassword) {
            toggleConfirmPassword.addEventListener('click', () => {
                this.togglePasswordVisibility('confirmPassword', 'toggleConfirmPassword');
            });
        }

        const changePasswordModal = document.getElementById('changePasswordModal');
        if (changePasswordModal) {
            changePasswordModal.addEventListener('click', (e) => {
                if (e.target === changePasswordModal) {
                    this.hideChangePasswordModal();
                }
            });
        }



        // 充值弹窗事件
        this.bindRechargeModalEvents();

        // 手动调整弹窗事件
        this.bindAdjustModalEvents();

        // 分成统计事件
        this.bindCommissionStatsEvents();
    }

    initializeTab() {
        // 检查URL hash来确定初始标签页
        const hash = window.location.hash.substring(1); // 移除 # 符号

        // 如果是设置页面，显示修改密码弹窗
        if (hash === 'settings') {
            this.showTab('members'); // 默认显示会员管理标签
            // 延迟显示密码修改弹窗，确保页面已加载完成
            setTimeout(() => {
                this.showChangePasswordModal();
            }, 100);
        } else {
            const validTabs = ['members', 'points', 'hierarchy', 'points-config', 'commission-stats', 'records', 'admin-manage'];
            const initialTab = validTabs.includes(hash) ? hash : 'members';
            this.showTab(initialTab);
        }

        // 清除hash，避免页面刷新时重复处理
        if (hash) {
            window.history.replaceState(null, null, window.location.pathname);
        }
    }

    async handleLogout() {
        try {
            // 清除登录状态，但保留记住密码的凭据
            localStorage.removeItem('isLoggedIn');
            localStorage.removeItem('currentAdmin');
            // 注意：不删除 loginCredentials，保留"记住密码"功能

            // 调用退出登录API
            await window.electronAPI.appLogout();

            // 跳转到登录页面
            window.location.href = 'login.html';
        } catch (error) {
            console.error('退出登录失败:', error);
            // 即使API调用失败，也要跳转到登录页面
            window.location.href = 'login.html';
        }
    }

    showTab(tabName) {
        // 更新当前标签
        this.currentTab = tabName;

        // 更新标签按钮状态
        const navTabs = document.querySelectorAll('.nav-tab');
        navTabs.forEach(tab => {
            tab.classList.remove('active');
            if (tab.dataset.tab === tabName) {
                tab.classList.add('active');
            }
        });

        // 更新标签内容显示
        const tabContents = document.querySelectorAll('.tab-content');
        tabContents.forEach(content => {
            content.classList.remove('active');
            content.style.display = 'none'; // 确保隐藏
        });

        const activeTabContent = document.getElementById(`${tabName}-tab`);
        if (activeTabContent) {
            activeTabContent.classList.add('active');
            activeTabContent.style.display = 'block'; // 强制显示
        }



        // 如果切换到积分配置标签页，加载积分配置数据
        if (tabName === 'points-config') {
            this.loadPointsConfig();
        }

        // 如果切换到操作记录标签页，加载操作记录
        if (tabName === 'records') {
            this.loadOperationLogs({}, 1);
        }

        // 如果切换到层级关系标签页，构建会员树
        if (tabName === 'hierarchy') {
            // 延迟构建，确保标签页已显示
            setTimeout(() => {
                this.buildMemberTree();
            }, 100);
        }

        // 如果切换到分成统计标签页，加载分成统计数据
        if (tabName === 'commission-stats') {
            // 延迟加载，确保标签页已显示
            setTimeout(() => {
                this.loadCommissionStats();
            }, 100);
        }

        // 如果切换到管理员管理标签页，加载管理员数据
        if (tabName === 'admin-manage') {
            // 延迟加载，确保标签页已显示
            setTimeout(() => {
                this.loadAdmins();
            }, 100);
        }
    }

    async loadMembers(page = 1) {
        try {
            this.showLoading(true);
            this.currentPage = page;
            this.isSearching = false;

            // 获取所有会员数据用于分页
            const result = await window.electronAPI.memberGetAll();
            if (result.success) {
                this.allMembers = result.members || [];
                this.originalAllMembers = [...this.allMembers]; // 保存完整数据副本
                this.totalMembersCount = this.allMembers.length;

                // 计算分页数据
                const startIndex = (page - 1) * this.pageSize;
                const endIndex = startIndex + this.pageSize;
                this.members = this.allMembers.slice(startIndex, endIndex);

                this.renderMembersTable();
                this.updateMembersCount();
                this.renderMembersPagination();
            } else {
                this.showToast('error', result.message || '加载会员列表失败');
            }
        } catch (error) {
            console.error('加载会员列表失败:', error);
            this.showToast('error', '加载会员列表失败');
        } finally {
            this.showLoading(false);
        }
    }

    renderMembersTable() {
        const tableBody = document.getElementById('membersTableBody');
        if (!tableBody) return;

        if (this.members.length === 0) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="9" style="text-align: center; padding: 40px; color: #999;">
                        暂无会员数据
                    </td>
                </tr>
            `;
            return;
        }

        tableBody.innerHTML = this.members.map(member => `
            <tr>
                <td>${member.id}</td>
                <td>${member.name}</td>
                <td class="level-column">${this.getMemberLevelDisplay(member.id)}</td>
                <td>${this.formatPoints(member.member_points)}</td>
                <td>${this.formatPoints(member.sales_points)}</td>
                <td>${this.getEffectiveDateDisplay(member.effective_date)}</td>
                <td>${this.getMemberStatusDisplay(member)}</td>
                <td>${this.formatDateWithTimezone(member.created_at)}</td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-recharge" onclick="memberManagement.showRechargeModal(${member.id})" title="会员充值">
                            会员充值
                        </button>
                        <button class="btn btn-member-points" onclick="memberManagement.showMemberPointsAdjustModal(${member.id})" title="会员积分调整">
                            会员积分
                        </button>
                        <button class="btn btn-sales-points" onclick="memberManagement.showSalesPointsAdjustModal(${member.id})" title="销售积分调整">
                            销售积分
                        </button>
                        <button class="btn btn-edit" onclick="memberManagement.editMember(${member.id})" title="编辑会员">
                            编辑
                        </button>
                        <button class="btn btn-delete" onclick="memberManagement.deleteMember(${member.id})" title="删除会员">
                            删除
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    updateMembersCount() {
        const totalMembersElement = document.getElementById('totalMembers');
        if (totalMembersElement) {
            totalMembersElement.textContent = this.totalMembersCount;
        }
    }

    renderMembersPagination() {
        const paginationContainer = document.getElementById('membersPagination');
        if (!paginationContainer) return;

        const totalPages = Math.ceil((this.totalMembersCount || 0) / this.pageSize);
        const currentPage = this.currentPage || 1;

        if (totalPages <= 1) {
            paginationContainer.innerHTML = '';
            return;
        }

        let paginationHTML = '';

        // 上一页按钮
        if (currentPage > 1) {
            paginationHTML += `<button onclick="memberManagement.goToMembersPage(${currentPage - 1})">上一页</button>`;
        }

        // 页码按钮
        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, currentPage + 2);

        if (startPage > 1) {
            paginationHTML += `<button onclick="memberManagement.goToMembersPage(1)">1</button>`;
            if (startPage > 2) {
                paginationHTML += `<span>...</span>`;
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            const activeClass = i === currentPage ? 'active' : '';
            paginationHTML += `<button class="${activeClass}" onclick="memberManagement.goToMembersPage(${i})">${i}</button>`;
        }

        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                paginationHTML += `<span>...</span>`;
            }
            paginationHTML += `<button onclick="memberManagement.goToMembersPage(${totalPages})">${totalPages}</button>`;
        }

        // 下一页按钮
        if (currentPage < totalPages) {
            paginationHTML += `<button onclick="memberManagement.goToMembersPage(${currentPage + 1})">下一页</button>`;
        }

        paginationContainer.innerHTML = paginationHTML;
    }

    // 处理会员分页跳转
    goToMembersPage(page) {
        this.currentPage = page;

        // 计算分页数据
        const startIndex = (page - 1) * this.pageSize;
        const endIndex = startIndex + this.pageSize;
        this.members = this.allMembers.slice(startIndex, endIndex);

        this.renderMembersTable();
        this.renderMembersPagination();
    }

    // 获取会员状态显示（HTML格式）
    getMemberStatusDisplay(member) {
        if (!member) {
            return '<span class="member-status unknown">未知状态</span>';
        }

        // 如果没有到期日期，显示普通会员
        if (!member.expire_date) {
            return '<span class="member-status ordinary">普通会员</span>';
        }

        const statusInfo = this.getMemberStatusInfo(member);

        // 到期日期列只显示日期，不显示状态
        switch (statusInfo.status) {
            case 'active':
                return `<span class="member-status active">${this.formatDateWithTimezone(member.expire_date)}</span>`;
            case 'expired':
                return `<span class="member-status expired custom-tooltip" style="color: red;" data-tooltip="已过期">${this.formatDateWithTimezone(member.expire_date)}</span>`;
            case 'not_effective':
                // 未生效状态也直接显示到期日期，不显示"未生效"
                return `<span class="member-status pending">${this.formatDateWithTimezone(member.expire_date)}</span>`;
            case 'not_purchased':
                return '<span class="member-status ordinary">普通会员</span>';
            case 'invalid_date':
                return '<span class="member-status error">数据异常</span>';
            default:
                return '<span class="member-status unknown">未知状态</span>';
        }
    }

    // 获取会员状态文本（纯文本格式）
    getMemberStatusText(expireDate) {
        if (!expireDate) {
            return '普通会员';
        }

        const expire = new Date(expireDate);
        const now = new Date();

        if (expire <= now) {
            return '已过期';
        } else {
            return `到期: ${this.formatDateWithTimezone(expireDate)}`;
        }
    }

    // 获取生效日期显示
    getEffectiveDateDisplay(effectiveDate) {
        if (!effectiveDate) {
            return '<span class="effective-status none">未充值</span>';
        }

        const effective = new Date(effectiveDate);
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const effectiveDay = new Date(effective.getFullYear(), effective.getMonth(), effective.getDate());

        if (effectiveDay > today) {
            return `<span class="effective-status pending">${this.formatDateWithTimezone(effectiveDate)}</span>`;
        } else {
            return `<span class="effective-status active">${this.formatDateWithTimezone(effectiveDate)}</span>`;
        }
    }

    // 格式化日期时间（保持向后兼容，使用东八区时间）
    formatDateTime(dateTimeString) {
        return this.formatDateTimeWithTimezone(dateTimeString);
    }

    // 计算会员层级
    getMemberLevel(memberId) {
        // 使用完整的会员数据计算层级，不受搜索和分页影响
        const membersData = this.originalAllMembers.length > 0 ? this.originalAllMembers : this.allMembers;

        if (!membersData || membersData.length === 0) {
            return 1;
        }

        const member = membersData.find(m => m.id === memberId);
        if (!member || !member.parent_id) {
            return 1; // 顶级会员
        }

        // 递归计算层级
        let level = 1;
        let currentParentId = member.parent_id;

        while (currentParentId) {
            level++;
            const parent = membersData.find(m => m.id === currentParentId);
            if (!parent) break;
            currentParentId = parent.parent_id;

            // 防止无限循环
            if (level > 20) break;
        }

        return level;
    }

    // 增强的层级显示
    getMemberLevelDisplay(memberId) {
        const level = this.getMemberLevel(memberId);

        // 层级样式类 - 扩展到10级，超过10级使用level-high
        let levelClass = 'level-display';
        if (level === 1) levelClass += ' level-1';
        else if (level === 2) levelClass += ' level-2';
        else if (level === 3) levelClass += ' level-3';
        else if (level === 4) levelClass += ' level-4';
        else if (level === 5) levelClass += ' level-5';
        else if (level === 6) levelClass += ' level-6';
        else if (level === 7) levelClass += ' level-7';
        else if (level === 8) levelClass += ' level-8';
        else if (level === 9) levelClass += ' level-9';
        else if (level === 10) levelClass += ' level-10';
        else levelClass += ' level-high';

        return `
            <div class="${levelClass}">
                <span class="level-text">${level}</span>
            </div>
        `;
    }

    // 显示层级预览
    showHierarchyPreview(parentId, containerId) {
        const container = document.getElementById(containerId);
        if (!container) return;

        if (!parentId) {
            container.innerHTML = `
                <div class="hierarchy-preview">
                    <span class="parent-level-info">将成为顶级会员</span>
                    <span class="arrow">→</span>
                    <span class="new-level">1层</span>
                </div>
                <div class="level-hint">顶级会员可以发展下级会员</div>
            `;
            return;
        }

        const parentLevel = this.getMemberLevel(parentId);
        const newMemberLevel = parentLevel + 1;
        const membersData = this.originalAllMembers.length > 0 ? this.originalAllMembers : this.allMembers;
        const parentMember = membersData.find(m => m.id === parentId);

        if (parentMember) {
            container.innerHTML = `
                <div class="hierarchy-preview">
                    <span class="parent-level-info">上级：${parentMember.name} (${parentLevel}层)</span>
                    <span class="arrow">→</span>
                    <span class="new-level">新会员：${newMemberLevel}层</span>
                </div>
                <div class="level-hint">
                    ${newMemberLevel <= 3 ? '前3层享有较高分成比例' : '第4层及以上分成比例较低'}
                </div>
            `;
        }
    }

    // 清空层级预览
    clearHierarchyPreview(containerId) {
        const container = document.getElementById(containerId);
        if (container) {
            container.innerHTML = '';
        }
    }

    // 显示会员积分调整弹窗
    async showMemberPointsAdjustModal(memberId) {
        try {
            // 获取会员信息
            const result = await window.electronAPI.memberGetById(memberId);
            if (!result.success) {
                this.showToast('error', '获取会员信息失败');
                return;
            }

            const member = result.member;

            // 显示会员积分调整弹窗
            this.showAdjustPointsModalForMember(member, 'member');

            // 显示提示信息
            this.showToast('info', `正在为会员"${member.name}"调整会员积分`);

        } catch (error) {
            console.error('显示会员积分调整弹窗失败:', error);
            this.showToast('error', '显示会员积分调整弹窗失败');
        }
    }

    // 显示销售积分调整弹窗
    async showSalesPointsAdjustModal(memberId) {
        try {
            // 获取会员信息
            const result = await window.electronAPI.memberGetById(memberId);
            if (!result.success) {
                this.showToast('error', '获取会员信息失败');
                return;
            }

            const member = result.member;

            // 显示销售积分调整弹窗
            this.showAdjustPointsModalForMember(member, 'sales');

            // 显示提示信息
            this.showToast('info', `正在为会员"${member.name}"调整销售积分`);

        } catch (error) {
            console.error('显示销售积分调整弹窗失败:', error);
            this.showToast('error', '显示销售积分调整弹窗失败');
        }
    }

    // 直接显示积分调整弹窗（保留兼容性）
    async showPointsAdjustModal(memberId) {
        // 默认显示会员积分调整
        await this.showMemberPointsAdjustModal(memberId);
    }

    async searchMembers() {
        const searchInput = document.getElementById('memberSearch');
        const keyword = searchInput ? searchInput.value.trim() : '';

        if (!keyword) {
            await this.loadMembers(1);
            return;
        }

        try {
            this.showLoading(true);
            this.isSearching = true;

            const result = await window.electronAPI.memberSearch(keyword);
            if (result.success) {
                this.allMembers = result.members || [];
                this.totalMembersCount = this.allMembers.length;
                this.currentPage = 1;

                // 计算分页数据
                const startIndex = 0;
                const endIndex = this.pageSize;
                this.members = this.allMembers.slice(startIndex, endIndex);

                this.renderMembersTable();
                this.updateMembersCount();
                this.renderMembersPagination();
            } else {
                this.showToast('error', result.message || '搜索失败');
            }
        } catch (error) {
            console.error('搜索失败:', error);
            this.showToast('error', '搜索失败');
        } finally {
            this.showLoading(false);
        }
    }

    showAddMemberModal(defaultParentId = null) {
        const modal = document.getElementById('addMemberModal');
        if (modal) {
            modal.classList.add('show');

            // 清空表单
            const form = document.getElementById('addMemberForm');
            if (form) {
                form.reset();
            }

            // 清空搜索框
            const searchInput = document.getElementById('parentMemberSearch');
            if (searchInput) {
                searchInput.value = '';
            }

            // 清空层级预览
            this.clearHierarchyPreview('addMemberHierarchyPreview');

            // 加载上级会员选项
            this.loadParentMemberOptions();

            // 如果指定了默认父会员ID，设置它
            if (defaultParentId) {
                setTimeout(() => {
                    const parentSelect = document.getElementById('parentMember');
                    if (parentSelect) {
                        parentSelect.value = defaultParentId;
                        // 触发change事件以更新层级预览
                        parentSelect.dispatchEvent(new Event('change'));
                        console.log('设置默认上级会员ID为:', defaultParentId);
                    }
                }, 50); // 较短的延迟，确保选项已加载
            } else {
                // 显示默认层级预览（顶级会员）
                this.showHierarchyPreview(null, 'addMemberHierarchyPreview');
            }
        }
    }

    hideAddMemberModal() {
        const modal = document.getElementById('addMemberModal');
        if (modal) {
            modal.classList.remove('show');
        }
    }

    showEditMemberModal(member) {
        const modal = document.getElementById('editMemberModal');
        if (modal) {
            modal.classList.add('show');

            // 填充表单数据
            document.getElementById('editMemberId').value = member.id;
            document.getElementById('editMemberName').value = member.name;

            // 调试：打印会员数据
            console.log('编辑会员数据:', member);
            console.log('生效日期原始值:', member.effective_date);
            console.log('到期日期原始值:', member.expire_date);

            // 填充日期字段
            const effectiveDateInput = document.getElementById('editEffectiveDate');
            const expiryDateInput = document.getElementById('editExpiryDate');

            // 先清空日期字段
            if (effectiveDateInput) {
                effectiveDateInput.value = '';
            }
            if (expiryDateInput) {
                expiryDateInput.value = '';
            }

            // 填充生效日期
            if (effectiveDateInput && member.effective_date) {
                console.log('处理生效日期:', member.effective_date);

                // 尝试不同的日期解析方式
                let effectiveDate;
                if (member.effective_date.includes('T')) {
                    // 如果已经包含时间部分
                    effectiveDate = new Date(member.effective_date);
                } else {
                    // 如果只是日期字符串，添加时间部分避免时区问题
                    effectiveDate = new Date(member.effective_date + 'T00:00:00');
                }

                console.log('解析后的生效日期:', effectiveDate);

                if (!isNaN(effectiveDate.getTime())) {
                    const year = effectiveDate.getFullYear();
                    const month = String(effectiveDate.getMonth() + 1).padStart(2, '0');
                    const day = String(effectiveDate.getDate()).padStart(2, '0');
                    const dateString = `${year}-${month}-${day}`;
                    console.log('设置生效日期值:', dateString);
                    effectiveDateInput.value = dateString;
                }
            }

            // 填充到期日期
            if (expiryDateInput && member.expire_date) {
                console.log('处理到期日期:', member.expire_date);

                let expiryDate;
                if (member.expire_date.includes('T')) {
                    expiryDate = new Date(member.expire_date);
                } else {
                    expiryDate = new Date(member.expire_date + 'T00:00:00');
                }

                console.log('解析后的到期日期:', expiryDate);

                if (!isNaN(expiryDate.getTime())) {
                    const year = expiryDate.getFullYear();
                    const month = String(expiryDate.getMonth() + 1).padStart(2, '0');
                    const day = String(expiryDate.getDate()).padStart(2, '0');
                    const dateString = `${year}-${month}-${day}`;
                    console.log('设置到期日期值:', dateString);
                    expiryDateInput.value = dateString;
                }
            }

            // 清空搜索框
            const searchInput = document.getElementById('editParentMemberSearch');
            if (searchInput) {
                searchInput.value = '';
            }

            // 清空层级预览
            this.clearHierarchyPreview('editMemberHierarchyPreview');

            // 加载上级会员选项
            this.loadEditParentMemberOptions(member.parent_id);

            // 显示当前层级预览
            this.showHierarchyPreview(member.parent_id, 'editMemberHierarchyPreview');
        }
    }

    hideEditMemberModal() {
        const modal = document.getElementById('editMemberModal');
        if (modal) {
            modal.classList.remove('show');
        }
    }

    loadParentMemberOptions() {
        const parentSelect = document.getElementById('parentMember');
        const searchInput = document.getElementById('parentMemberSearch');
        if (!parentSelect) return;

        // 保存所有会员数据用于搜索，使用完整数据而不是分页数据
        const membersData = this.originalAllMembers.length > 0 ? this.originalAllMembers : this.allMembers;
        this.allParentMembers = [...membersData];

        // 初始化显示所有选项
        this.renderParentMemberOptions(this.allParentMembers);

        // 绑定搜索事件
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.filterParentMembers(e.target.value);
            });
        }
    }

    renderParentMemberOptions(members) {
        const parentSelect = document.getElementById('parentMember');
        if (!parentSelect) return;

        // 清空现有选项
        parentSelect.innerHTML = '<option value="" selected>无上级（顶级会员）</option>';

        // 添加会员选项
        members.forEach(member => {
            const option = document.createElement('option');
            option.value = member.id;
            option.textContent = `${member.name} (编号: ${member.id})`;
            parentSelect.appendChild(option);
        });
    }

    filterParentMembers(keyword) {
        if (!keyword.trim()) {
            this.renderParentMemberOptions(this.allParentMembers);
            return;
        }

        const filteredMembers = this.allParentMembers.filter(member =>
            member.name.toLowerCase().includes(keyword.toLowerCase()) ||
            member.id.toString().includes(keyword)
        );

        this.renderParentMemberOptions(filteredMembers);
    }

    loadEditParentMemberOptions(currentParentId) {
        const parentSelect = document.getElementById('editParentMember');
        const searchInput = document.getElementById('editParentMemberSearch');
        if (!parentSelect) return;

        // 获取当前编辑的会员ID
        const currentMemberId = parseInt(document.getElementById('editMemberId').value);

        // 获取当前会员的所有下级会员ID
        const subordinateIds = this.getAllSubordinates(currentMemberId);

        console.log(`编辑会员 ${currentMemberId}，排除的下级会员ID:`, subordinateIds);

        // 保存所有可选的会员数据（排除自己和所有下级会员），使用完整数据
        const membersData = this.originalAllMembers.length > 0 ? this.originalAllMembers : this.allMembers;
        this.allEditParentMembers = membersData.filter(member =>
            member.id !== currentMemberId && !subordinateIds.includes(member.id)
        );
        this.currentParentId = currentParentId;

        // 初始化显示所有选项
        this.renderEditParentMemberOptions(this.allEditParentMembers, currentParentId);

        // 绑定搜索事件
        if (searchInput) {
            // 移除之前的事件监听器
            searchInput.removeEventListener('input', this.editParentSearchHandler);

            // 创建新的事件处理器
            this.editParentSearchHandler = (e) => {
                this.filterEditParentMembers(e.target.value);
            };

            searchInput.addEventListener('input', this.editParentSearchHandler);
        }
    }

    renderEditParentMemberOptions(members, selectedParentId) {
        const parentSelect = document.getElementById('editParentMember');
        if (!parentSelect) return;

        // 清空现有选项，如果没有选中的上级，则默认选中"无上级"
        const noParentSelected = !selectedParentId;
        parentSelect.innerHTML = `<option value=""${noParentSelected ? ' selected' : ''}>无上级（顶级会员）</option>`;

        // 添加会员选项
        members.forEach(member => {
            const option = document.createElement('option');
            option.value = member.id;
            option.textContent = `${member.name} (编号: ${member.id})`;

            // 设置当前上级为选中状态
            if (member.id === selectedParentId) {
                option.selected = true;
            }

            parentSelect.appendChild(option);
        });
    }

    filterEditParentMembers(keyword) {
        if (!keyword.trim()) {
            this.renderEditParentMemberOptions(this.allEditParentMembers, this.currentParentId);
            return;
        }

        const filteredMembers = this.allEditParentMembers.filter(member =>
            member.name.toLowerCase().includes(keyword.toLowerCase()) ||
            member.id.toString().includes(keyword)
        );

        this.renderEditParentMemberOptions(filteredMembers, this.currentParentId);
    }

    // 获取某个会员的所有下级会员（递归获取所有层级）
    getAllSubordinates(memberId, allMembers = null) {
        // 使用完整的会员数据，不受搜索和分页影响
        const membersData = allMembers || (this.originalAllMembers.length > 0 ? this.originalAllMembers : this.allMembers);
        const subordinates = [];

        // 找到直接下级
        const directSubordinates = membersData.filter(member => member.parent_id === memberId);

        for (const subordinate of directSubordinates) {
            subordinates.push(subordinate.id);
            // 递归获取下级的下级
            const subSubordinates = this.getAllSubordinates(subordinate.id, membersData);
            subordinates.push(...subSubordinates);
        }

        return subordinates;
    }

    async handleAddMember() {
        const memberName = document.getElementById('memberName').value.trim();
        const parentMember = document.getElementById('parentMember').value;

        if (!memberName) {
            this.showToast('error', '请输入会员姓名');
            return;
        }

        // 检查管理员信息
        if (!this.currentAdmin || !this.currentAdmin.id) {
            console.error('当前管理员信息无效:', this.currentAdmin);
            this.showToast('error', '管理员信息无效，请重新登录');
            return;
        }

        console.log('当前管理员信息:', this.currentAdmin);

        try {
            this.showLoading(true);

            const result = await window.electronAPI.memberCreate({
                name: memberName,
                parentId: parentMember || null,
                adminId: this.currentAdmin.id,
                adminName: this.currentAdmin.name || this.currentAdmin.username
            });
            
            if (result.success) {
                this.showToast('success', '会员创建成功');
                this.hideAddMemberModal();
                await this.loadMembers(this.currentPage);
                // 自动刷新层级关系图
                this.autoRefreshHierarchyTree();
            } else {
                this.showToast('error', result.message || '创建会员失败');
            }
        } catch (error) {
            console.error('创建会员失败:', error);
            this.showToast('error', '创建会员失败');
        } finally {
            this.showLoading(false);
        }
    }

    async handleEditMember() {
        const memberId = document.getElementById('editMemberId').value;
        const memberName = document.getElementById('editMemberName').value.trim();
        const parentMember = document.getElementById('editParentMember').value;
        const effectiveDate = document.getElementById('editEffectiveDate').value;
        const expiryDate = document.getElementById('editExpiryDate').value;

        if (!memberName) {
            this.showToast('error', '请输入会员姓名');
            return;
        }

        // 验证日期逻辑
        if (effectiveDate && expiryDate) {
            const effective = new Date(effectiveDate);
            const expiry = new Date(expiryDate);

            if (effective >= expiry) {
                this.showToast('error', '生效日期必须早于到期日期');
                return;
            }
        }

        try {
            this.showLoading(true);

            const result = await window.electronAPI.memberUpdate({
                memberId: parseInt(memberId),
                name: memberName,
                parentId: parentMember || null,
                effectiveDate: effectiveDate || null,
                expiryDate: expiryDate || null,
                adminId: this.currentAdmin.id,
                adminName: this.currentAdmin.name
            });

            if (result.success) {
                this.showToast('success', '会员信息更新成功');
                this.hideEditMemberModal();
                await this.loadMembers(this.currentPage);
                // 自动刷新层级关系图
                this.autoRefreshHierarchyTree();
            } else {
                this.showToast('error', result.message || '更新会员信息失败');
            }
        } catch (error) {
            console.error('更新会员信息失败:', error);
            this.showToast('error', '更新会员信息失败');
        } finally {
            this.showLoading(false);
        }
    }

    async editMember(memberId) {
        try {
            // 获取会员信息
            const result = await window.electronAPI.memberGetById(memberId);
            if (!result.success) {
                this.showToast('error', result.message || '获取会员信息失败');
                return;
            }

            const member = result.member;

            // 显示编辑模态框
            this.showEditMemberModal(member);
        } catch (error) {
            console.error('获取会员信息失败:', error);
            this.showToast('error', '获取会员信息失败');
        }
    }

    async deleteMember(memberId) {
        // 获取会员信息用于显示确认对话框
        const member = this.members.find(m => m.id === memberId);
        if (!member) {
            this.showToast('error', '会员不存在');
            return;
        }

        // 显示自定义确认对话框
        this.showDeleteConfirmModal(member);
    }

    showDeleteConfirmModal(member) {
        const modal = document.getElementById('deleteConfirmModal');
        const memberNameSpan = document.getElementById('deleteMemberName');

        if (modal && memberNameSpan) {
            memberNameSpan.textContent = member.name;
            modal.classList.add('show');

            // 保存要删除的会员ID
            this.memberToDelete = member.id;
        }
    }

    hideDeleteConfirmModal() {
        const modal = document.getElementById('deleteConfirmModal');
        if (modal) {
            modal.classList.remove('show');
            this.memberToDelete = null;
        }
    }

    async confirmDeleteMember() {
        if (!this.memberToDelete) {
            return;
        }

        // 保存要删除的会员ID，避免在hideDeleteConfirmModal中被清空
        const memberIdToDelete = this.memberToDelete;

        try {
            this.showLoading(true);
            this.hideDeleteConfirmModal();

            const result = await window.electronAPI.memberDelete({
                memberId: memberIdToDelete,
                adminId: this.currentAdmin.id,
                adminName: this.currentAdmin.name
            });
            if (result.success) {
                this.showToast('success', result.message || '会员删除成功');
                // 删除后检查当前页是否还有数据，如果没有则回到上一页
                const totalPages = Math.ceil((this.totalMembersCount - 1) / this.pageSize);
                const targetPage = this.currentPage > totalPages ? Math.max(1, totalPages) : this.currentPage;
                await this.loadMembers(targetPage);
            } else {
                this.showToast('error', result.message || '删除会员失败');
            }
        } catch (error) {
            console.error('删除会员失败:', error);
            this.showToast('error', '删除会员失败');
        } finally {
            this.showLoading(false);
        }
    }

    showLoading(show) {
        const loadingOverlay = document.getElementById('loadingOverlay');
        if (loadingOverlay) {
            if (show) {
                loadingOverlay.classList.add('show');
            } else {
                loadingOverlay.classList.remove('show');
            }
        }
    }

    showChangePasswordModal() {
        const modal = document.getElementById('changePasswordModal');
        if (modal) {
            modal.classList.add('show');

            // 清空表单
            const form = document.getElementById('changePasswordForm');
            if (form) {
                form.reset();
            }
        }
    }

    hideChangePasswordModal() {
        const modal = document.getElementById('changePasswordModal');
        if (modal) {
            modal.classList.remove('show');
        }
    }

    async handleChangePassword() {
        const currentPassword = document.getElementById('currentPassword').value.trim();
        const newPassword = document.getElementById('newPassword').value.trim();
        const confirmPassword = document.getElementById('confirmPassword').value.trim();

        // 表单验证
        if (!currentPassword) {
            this.showToast('error', '请输入当前密码');
            return;
        }

        if (!newPassword) {
            this.showToast('error', '请输入新密码');
            return;
        }

        if (newPassword.length < 6) {
            this.showToast('error', '新密码长度至少6位');
            return;
        }

        if (newPassword !== confirmPassword) {
            this.showToast('error', '两次输入的新密码不一致');
            return;
        }

        if (currentPassword === newPassword) {
            this.showToast('error', '新密码不能与当前密码相同');
            return;
        }

        try {
            this.showLoading(true);

            const result = await window.electronAPI.adminChangePassword(
                this.currentAdmin.id,
                currentPassword,
                newPassword,
                this.currentAdmin.id
            );

            if (result.success) {
                this.showToast('success', '密码修改成功');
                this.hideChangePasswordModal();
            } else {
                this.showToast('error', result.message || '密码修改失败');
            }
        } catch (error) {
            console.error('修改密码失败:', error);
            this.showToast('error', '密码修改失败');
        } finally {
            this.showLoading(false);
        }
    }

    // ==================== 充值功能 ====================

    async showRechargeModal(memberId = null) {
        try {
            console.log('=== 打开充值弹窗 ===');
            
            // 如果没有指定会员ID，则不能打开充值弹窗
            if (!memberId) {
                this.showToast('error', '请先选择要充值的会员');
                return;
            }
            
            // 保存最初点击的会员信息（用于积分扣除）
            const result = await window.electronAPI.memberGetById(parseInt(memberId));
            if (!result.success) {
                this.showToast('error', '获取会员信息失败');
                return;
            }
            this.originalRechargeMember = result.member;
            
            // 加载可充值的会员列表（基于指定的会员ID）
            await this.loadRechargeableMembers(memberId);
            
            // 预选该会员
            const memberSelect = document.getElementById('rechargeTargetMember');
            if (memberSelect) {
                memberSelect.value = memberId;
                await this.onRechargeTargetMemberChange();
            }

            // 显示弹窗
            const modal = document.getElementById('rechargeModal');
            if (modal) {
                modal.classList.add('show');
            }
        } catch (error) {
            console.error('显示充值弹窗失败:', error);
            this.showToast('error', '打开充值弹窗失败');
        }
    }

    // 加载可充值的会员列表
    async loadRechargeableMembers(targetMemberId) {
        try {
            // 获取所有会员
            const result = await window.electronAPI.memberGetAll();
            if (!result.success) {
                this.showToast('error', '获取会员列表失败');
                return;
            }

            const allMembers = result.members;
            
            // 筛选可充值的会员：目标会员自己 + 所有下级会员
            const rechargeableMembers = this.getRechargeableMembers(allMembers, targetMemberId);
            
            // 渲染会员选择列表
            this.renderRechargeableMemberOptions(rechargeableMembers);
            
        } catch (error) {
            console.error('加载可充值会员列表失败:', error);
            this.showToast('error', '加载会员列表失败');
        }
    }

    // 获取可充值的会员列表（自己 + 下级）
    getRechargeableMembers(allMembers, targetMemberId) {
        const rechargeableMembers = [];
        
        // 添加目标会员自己
        const targetMember = allMembers.find(m => m.id === targetMemberId);
        if (targetMember) {
            rechargeableMembers.push(targetMember);
        }
        
        // 添加目标会员的所有下级会员
        const subordinateIds = this.getAllSubordinates(targetMemberId, allMembers);
        subordinateIds.forEach(subordinateId => {
            const subordinateMember = allMembers.find(m => m.id === subordinateId);
            if (subordinateMember) {
                rechargeableMembers.push(subordinateMember);
            }
        });
        
        return rechargeableMembers;
    }

    // 渲染可充值会员选项
    renderRechargeableMemberOptions(members) {
        const select = document.getElementById('rechargeTargetMember');
        if (!select) return;
        
        // 清空现有选项（保留第一个空选项）
        while (select.children.length > 1) {
            select.removeChild(select.lastChild);
        }
        
        // 添加会员选项
        members.forEach(member => {
            const option = document.createElement('option');
            option.value = member.id;
            option.textContent = `${member.name} (编号: ${member.id})`;
            select.appendChild(option);
        });
    }

    // 重置充值弹窗
    resetRechargeModal() {
        // 重置会员选择
        const memberSelect = document.getElementById('rechargeTargetMember');
        if (memberSelect) {
            memberSelect.value = '';
        }
        
        // 隐藏会员信息
        const memberInfo = document.getElementById('selectedMemberInfo');
        if (memberInfo) {
            memberInfo.style.display = 'none';
        }
        
        // 重置表单
        const form = document.getElementById('rechargeForm');
        if (form) {
            form.reset();
        }
        
        // 重置套餐选择
        this.resetPackageSelection();
        
        // 隐藏充值摘要
        const summary = document.getElementById('rechargeSummary');
        if (summary) {
            summary.style.display = 'none';
        }
        
        this.currentRechargeMember = null;
        this.originalRechargeMember = null;
    }

    // 重置套餐选择
    resetPackageSelection() {
        const packageSelect = document.getElementById('rechargePackage');
        if (packageSelect) {
            packageSelect.setAttribute('autocomplete', 'off');
            packageSelect.value = '';
            packageSelect.selectedIndex = 0;
            
            // 清除所有选项的选中状态
            for (let i = 0; i < packageSelect.options.length; i++) {
                packageSelect.options[i].selected = false;
                packageSelect.options[i].removeAttribute('selected');
            }
            
            // 设置第一个选项为选中
            if (packageSelect.options.length > 0) {
                packageSelect.options[0].selected = true;
                packageSelect.options[0].setAttribute('selected', 'selected');
            }
        }
    }

    // 处理会员选择变化
    async onRechargeTargetMemberChange() {
        const memberSelect = document.getElementById('rechargeTargetMember');
        const memberInfo = document.getElementById('selectedMemberInfo');
        
        if (!memberSelect || !memberInfo) return;
        
        const selectedMemberId = memberSelect.value;
        
        if (!selectedMemberId) {
            // 没有选择会员，隐藏信息
            memberInfo.style.display = 'none';
            this.currentRechargeMember = null;
            this.resetPackageSelection();
            document.getElementById('rechargeSummary').style.display = 'none';
            return;
        }
        
        try {
            // 获取选中会员的详细信息
            const result = await window.electronAPI.memberGetById(parseInt(selectedMemberId));
            if (!result.success) {
                this.showToast('error', result.message || '获取会员信息失败');
                return;
            }
            
            const member = result.member;
            this.currentRechargeMember = member;
            
            console.log('选中充值会员:', member.name, '(ID:', member.id, ')');
            
            // 填充会员信息
            document.getElementById('rechargeMemberName').textContent = member.name;
            document.getElementById('rechargeMemberId').textContent = member.id;
            document.getElementById('currentMemberPoints').textContent = this.formatPoints(member.member_points);
            document.getElementById('currentSalesPoints').textContent = this.formatPoints(member.sales_points);
            document.getElementById('currentExpireDate').textContent = this.getMemberStatusText(member.expire_date);
            document.getElementById('rechargeTargetMemberId').value = member.id;
            
            // 检查积分来源会员的状态（用于积分抵扣）
            const pointsSourceMember = this.originalRechargeMember || member;
            const statusInfo = this.getMemberStatusInfo(pointsSourceMember);

            // 显示可用积分和状态
            const availablePointsElement = document.getElementById('availablePoints');
            const pointsToUseInput = document.getElementById('pointsToUse');
            const useMaxPointsBtn = document.getElementById('useMaxPointsBtn');

            if (statusInfo.canUsePoints) {
                // 会员在有效期内，可以使用积分
                availablePointsElement.textContent = this.formatPoints(pointsSourceMember.member_points);
                availablePointsElement.style.color = '';
                pointsToUseInput.disabled = false;
                useMaxPointsBtn.disabled = false;
                pointsToUseInput.placeholder = '输入要使用的积分数量（支持小数）';
            } else {
                // 会员不在有效期内，禁用积分功能
                availablePointsElement.textContent = `${this.formatPoints(pointsSourceMember.member_points)} (不可用)`;
                availablePointsElement.style.color = '#999';
                pointsToUseInput.disabled = true;
                useMaxPointsBtn.disabled = true;
                pointsToUseInput.placeholder = `积分不可用：${statusInfo.message}`;
                pointsToUseInput.value = '';
            }

            // 显示会员信息
            memberInfo.style.display = 'block';

            // 重置套餐选择和积分抵扣
            this.resetPackageSelection();
            if (statusInfo.canUsePoints) {
                document.getElementById('pointsToUse').value = '';
            }
            this.updateRechargeSummary();
            
        } catch (error) {
            console.error('加载会员信息失败:', error);
            this.showToast('error', '获取会员信息失败');
        }
    }



    hideRechargeModal() {
        const modal = document.getElementById('rechargeModal');
        if (modal) {
            modal.classList.remove('show');
            
            // 重置充值弹窗状态
            this.resetRechargeModal();
            
            // 清空搜索框
            const searchInput = document.getElementById('rechargeTargetMemberSearch');
            if (searchInput) {
                searchInput.value = '';
            }
            
            console.log('充值弹窗已关闭并重置');
        }
    }

    showToast(type, message) {
        const toast = document.getElementById('toast');
        const toastIcon = document.getElementById('toastIcon');
        const toastMessage = document.getElementById('toastMessage');

        if (!toast || !toastIcon || !toastMessage) return;

        // 设置图标和消息
        const icons = {
            success: '✅',
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️'
        };

        toastIcon.textContent = icons[type] || icons.info;
        toastMessage.textContent = message;

        // 显示提示
        toast.classList.add('show');

        // 3秒后自动隐藏
        setTimeout(() => {
            toast.classList.remove('show');
        }, 3000);
    }

    bindRechargeModalEvents() {
        // 充值弹窗关闭事件
        const closeRechargeModal = document.getElementById('closeRechargeModal');
        const cancelRechargeBtn = document.getElementById('cancelRechargeBtn');
        const rechargeForm = document.getElementById('rechargeForm');
        const rechargePackage = document.getElementById('rechargePackage');

        if (closeRechargeModal) {
            closeRechargeModal.addEventListener('click', () => {
                this.hideRechargeModal();
            });
        }

        if (cancelRechargeBtn) {
            cancelRechargeBtn.addEventListener('click', () => {
                this.hideRechargeModal();
            });
        }

        if (rechargeForm) {
            rechargeForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleRecharge();
            });
        }

        if (rechargePackage) {
            rechargePackage.addEventListener('change', () => {
                this.updateRechargeSummary();
            });
        }

        const pointsToUseInput = document.getElementById('pointsToUse');
        if (pointsToUseInput) {
            pointsToUseInput.addEventListener('input', () => {
                this.updateRechargeSummary();
            });
        }

        // 最大积分按钮事件
        const useMaxPointsBtn = document.getElementById('useMaxPointsBtn');
        if (useMaxPointsBtn) {
            useMaxPointsBtn.addEventListener('click', () => {
                this.useMaxPoints();
            });
        }

        // 会员选择变化事件
        const rechargeTargetMember = document.getElementById('rechargeTargetMember');
        if (rechargeTargetMember) {
            rechargeTargetMember.addEventListener('change', () => {
                this.onRechargeTargetMemberChange();
            });
        }

        // 会员搜索事件
        const rechargeTargetMemberSearch = document.getElementById('rechargeTargetMemberSearch');
        if (rechargeTargetMemberSearch) {
            rechargeTargetMemberSearch.addEventListener('input', (e) => {
                this.filterRechargeableMembers(e.target.value);
            });
        }

        const rechargeModal = document.getElementById('rechargeModal');
        if (rechargeModal) {
            rechargeModal.addEventListener('click', (e) => {
                if (e.target === rechargeModal) {
                    this.hideRechargeModal();
                }
            });
        }
    }

    // 过滤可充值会员列表
    filterRechargeableMembers(searchTerm) {
        const select = document.getElementById('rechargeTargetMember');
        if (!select) return;

        const options = select.querySelectorAll('option');
        const lowerSearchTerm = searchTerm.toLowerCase();

        options.forEach((option, index) => {
            if (index === 0) {
                // 保持第一个空选项始终可见
                option.style.display = '';
                return;
            }

            const text = option.textContent.toLowerCase();
            if (text.includes(lowerSearchTerm)) {
                option.style.display = '';
            } else {
                option.style.display = 'none';
            }
        });
    }

    bindAdjustModalEvents() {
        // 手动调整弹窗事件
        const closeAdjustModal = document.getElementById('closeAdjustModal');
        const cancelAdjustBtn = document.getElementById('cancelAdjustBtn');
        const adjustPointsForm = document.getElementById('adjustPointsForm');

        if (closeAdjustModal) {
            closeAdjustModal.addEventListener('click', () => {
                this.hideAdjustPointsModal();
            });
        }

        if (cancelAdjustBtn) {
            cancelAdjustBtn.addEventListener('click', () => {
                this.hideAdjustPointsModal();
            });
        }

        if (adjustPointsForm) {
            adjustPointsForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleAdjustPoints();
            });
        }

        // 清零按钮事件
        const clearMemberPointsBtn = document.getElementById('clearMemberPointsBtn');
        const clearSalesPointsBtn = document.getElementById('clearSalesPointsBtn');

        if (clearMemberPointsBtn) {
            clearMemberPointsBtn.addEventListener('click', () => {
                this.handleClearPoints('member');
            });
        }

        if (clearSalesPointsBtn) {
            clearSalesPointsBtn.addEventListener('click', () => {
                this.handleClearPoints('sales');
            });
        }

        // 积分清零确认对话框事件
        const closeClearConfirmModal = document.getElementById('closeClearConfirmModal');
        const cancelClearBtn = document.getElementById('cancelClearBtn');
        const confirmClearBtn = document.getElementById('confirmClearBtn');

        if (closeClearConfirmModal) {
            closeClearConfirmModal.addEventListener('click', () => {
                this.hideClearConfirmModal();
            });
        }

        if (cancelClearBtn) {
            cancelClearBtn.addEventListener('click', () => {
                this.hideClearConfirmModal();
            });
        }

        if (confirmClearBtn) {
            confirmClearBtn.addEventListener('click', () => {
                this.executeClearPoints();
            });
        }

        // 限制积分调整输入框只能输入数字（支持小数）
        const adjustAmountInput = document.getElementById('adjustAmount');
        if (adjustAmountInput) {
            adjustAmountInput.addEventListener('input', (e) => {
                // 只允许数字、负号、小数点和空字符
                let value = e.target.value;

                // 移除所有非数字、非负号、非小数点的字符
                value = value.replace(/[^0-9.-]/g, '');

                // 确保负号只能在开头
                if (value.indexOf('-') > 0) {
                    value = value.replace(/-/g, '');
                }

                // 确保只有一个负号
                const negativeCount = (value.match(/-/g) || []).length;
                if (negativeCount > 1) {
                    value = value.replace(/-/g, '');
                    if (value.length > 0) {
                        value = '-' + value;
                    }
                }

                // 确保只有一个小数点
                const dotCount = (value.match(/\./g) || []).length;
                if (dotCount > 1) {
                    const parts = value.split('.');
                    value = parts[0] + '.' + parts.slice(1).join('');
                }

                // 限制小数位数为1位
                if (value.includes('.')) {
                    const parts = value.split('.');
                    if (parts[1] && parts[1].length > 1) {
                        parts[1] = parts[1].substring(0, 1);
                        value = parts.join('.');
                    }
                }

                e.target.value = value;
            });

            // 优化粘贴处理
            adjustAmountInput.addEventListener('paste', (e) => {
                e.preventDefault();
                const paste = (e.clipboardData || window.clipboardData).getData('text');

                // 检查粘贴内容是否为有效数字（支持小数）
                if (/^-?[0-9]+(\.[0-9]?)?$/.test(paste.trim())) {
                    e.target.value = paste.trim();
                    // 触发input事件以确保验证
                    e.target.dispatchEvent(new Event('input'));
                }
            });

            // 使用更兼容的keydown事件处理，支持数字小键盘
            adjustAmountInput.addEventListener('keydown', (e) => {
                // 允许的控制键和功能键
                const allowedKeys = [
                    'Backspace', 'Delete', 'Tab', 'Escape', 'Enter',
                    'Home', 'End', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown',
                    'F1', 'F2', 'F3', 'F4', 'F5', 'F6', 'F7', 'F8', 'F9', 'F10', 'F11', 'F12'
                ];

                // 允许Ctrl/Cmd/Alt组合键（如Ctrl+A, Ctrl+C, Ctrl+V等）
                if (e.ctrlKey || e.metaKey || e.altKey) {
                    return;
                }

                // 允许控制键
                if (allowedKeys.includes(e.key)) {
                    return;
                }

                // 允许数字键（主键盘和数字小键盘）
                if ((e.key >= '0' && e.key <= '9') ||
                    (e.code >= 'Digit0' && e.code <= 'Digit9') ||
                    (e.code >= 'Numpad0' && e.code <= 'Numpad9')) {
                    return;
                }

                // 允许负号，但只能在开头且只能有一个
                if (e.key === '-' || e.key === 'Minus' || e.code === 'Minus' || e.code === 'NumpadSubtract') {
                    const currentValue = e.target.value;
                    const cursorPosition = e.target.selectionStart;

                    // 只允许在开头输入负号，且当前没有负号
                    if (cursorPosition === 0 && !currentValue.includes('-')) {
                        return;
                    }
                }

                // 允许小数点，但只能有一个
                if (e.key === '.' || e.key === 'Period' || e.code === 'Period' || e.code === 'NumpadDecimal') {
                    const currentValue = e.target.value;

                    // 只允许输入一个小数点
                    if (!currentValue.includes('.')) {
                        return;
                    }
                }

                // 阻止其他所有按键
                e.preventDefault();
            });

            // 输入框获得焦点时自动选中现有内容
            adjustAmountInput.addEventListener('focus', (e) => {
                e.target.select();
            });
        }

        const adjustPointsModal = document.getElementById('adjustPointsModal');
        if (adjustPointsModal) {
            adjustPointsModal.addEventListener('click', (e) => {
                // 只有直接点击弹窗背景（不是内容区域）时才关闭
                if (e.target === adjustPointsModal && e.type === 'click') {
                    // 确保不是文本选择等其他操作
                    const selection = window.getSelection();
                    if (!selection || selection.toString().length === 0) {
                        this.hideAdjustPointsModal();
                    }
                }
            });
        }

        // 积分类型选择器事件监听器
        const pointsTypeSelect = document.getElementById('pointsType');
        if (pointsTypeSelect) {
            pointsTypeSelect.addEventListener('change', (e) => {
                const selectedType = e.target.value;
                const commissionGroup = document.querySelector('.form-group:has(label[for="enableCommission"])');
                
                if (commissionGroup) {
                    commissionGroup.style.display = 'block';
                    const enableCommissionYes = document.getElementById('enableCommissionYes');
                    const enableCommissionNo = document.getElementById('enableCommissionNo');
                    
                    if (selectedType === 'sales') {
                        // 销售积分默认启用分成
                        if (enableCommissionYes) {
                            enableCommissionYes.checked = true;
                        }
                        if (enableCommissionNo) {
                            enableCommissionNo.checked = false;
                        }
                        // 更新标签文字
                        const yesLabel = enableCommissionYes?.nextElementSibling;
                        const noLabel = enableCommissionNo?.nextElementSibling;
                        if (yesLabel) yesLabel.textContent = '启用分成（默认）';
                        if (noLabel) noLabel.textContent = '不启用分成';
                    } else if (selectedType === 'member') {
                        // 会员积分默认不启用分成
                        if (enableCommissionYes) {
                            enableCommissionYes.checked = false;
                        }
                        if (enableCommissionNo) {
                            enableCommissionNo.checked = true;
                        }
                        // 更新标签文字
                        const yesLabel = enableCommissionYes?.nextElementSibling;
                        const noLabel = enableCommissionNo?.nextElementSibling;
                        if (yesLabel) yesLabel.textContent = '启用分成';
                        if (noLabel) noLabel.textContent = '不启用分成（默认）';
                    } else {
                        // 未选择积分类型时保持HTML默认状态（不启用分成）
                        const yesLabel = enableCommissionYes?.nextElementSibling;
                        const noLabel = enableCommissionNo?.nextElementSibling;
                        if (yesLabel) yesLabel.textContent = '启用分成';
                        if (noLabel) noLabel.textContent = '不启用分成（默认）';
                    }
                }
            });
        }
    }

    useMaxPoints() {
        const packageSelect = document.getElementById('rechargePackage');
        const pointsToUseInput = document.getElementById('pointsToUse');

        if (!packageSelect.value) {
            this.showToast('warning', '请先选择会员套餐');
            return;
        }

        // 检查积分来源会员状态
        const pointsSourceMember = this.originalRechargeMember || this.currentRechargeMember;
        if (!pointsSourceMember) {
            this.showToast('error', '无法获取积分来源会员信息');
            return;
        }

        const statusInfo = this.getMemberStatusInfo(pointsSourceMember);
        if (!statusInfo.canUsePoints) {
            this.showToast('warning', `积分不可用：${statusInfo.message}`);
            return;
        }
        
        // 获取选中的套餐信息
        const selectedIndex = packageSelect.selectedIndex;
        const selectedOption = packageSelect.options[selectedIndex];
        
        if (!selectedOption || selectedIndex === 0 || !selectedOption.dataset.amount) {
            this.showToast('warning', '请选择有效的会员套餐');
            return;
        }
        
        const totalAmount = parseFloat(selectedOption.dataset.amount);
        const availablePoints = parseFloat(document.getElementById('availablePoints').textContent) || 0;
        
        // 计算最大可抵扣积分（10积分=1元，且不能超过套餐总金额）
        const maxUsablePoints = Math.min(availablePoints, totalAmount * 10);

        // 四舍五入到一位小数并填入输入框
        pointsToUseInput.value = Math.round(maxUsablePoints * 10) / 10;
        
        // 更新充值摘要
        this.updateRechargeSummary();
        
        // 显示提示
        if (maxUsablePoints > 0) {
            const deductionAmount = (maxUsablePoints / 10).toFixed(2);
            this.showToast('success', `已设置最大抵扣：${maxUsablePoints}积分 (¥${deductionAmount})`);
        } else {
            this.showToast('info', '当前无可用积分');
        }
    }

    updateRechargeSummary() {
        const packageSelect = document.getElementById('rechargePackage');
        const summaryDiv = document.getElementById('rechargeSummary');

        if (!packageSelect.value) {
            summaryDiv.style.display = 'none';
            return;
        }

        // 更可靠地获取选中的选项
        const selectedIndex = packageSelect.selectedIndex;
        const selectedOption = packageSelect.options[selectedIndex];
        
        // 验证选中的选项是否有效
        if (!selectedOption || selectedIndex === 0 || !selectedOption.dataset.amount) {
            summaryDiv.style.display = 'none';
            return;
        }
        
        const totalAmount = parseFloat(selectedOption.dataset.amount);
        const days = parseInt(selectedOption.dataset.days);
        
        console.log('updateRechargeSummary - 选中选项:', {
            selectedIndex,
            value: selectedOption.value,
            text: selectedOption.textContent,
            amount: selectedOption.dataset.amount,
            days: selectedOption.dataset.days
        });

        // 获取积分抵扣
        const pointsToUseInput = document.getElementById('pointsToUse');
        let pointsToUse = parseFloat(pointsToUseInput.value) || 0;
        const availablePoints = parseFloat(document.getElementById('availablePoints').textContent) || 0;

        // 四舍五入积分到一位小数
        pointsToUse = Math.round(pointsToUse * 10) / 10;

        // 计算积分抵扣金额（10积分=1元）
        // 限制积分使用不能超过可用积分，但允许完全抵扣套餐价格
        const maxUsablePoints = Math.min(pointsToUse, availablePoints);
        const pointsDeduction = Math.round((maxUsablePoints / 10) * 100) / 100; // 四舍五入到分
        const cashAmount = Math.max(0, totalAmount - pointsDeduction);

        // 更新最大可抵扣显示（最大可抵扣金额为可用积分对应的金额或套餐价格，取较小值）
        const maxDeductionAmount = Math.min(availablePoints / 10, totalAmount);
        document.getElementById('maxDeduction').textContent = `¥${maxDeductionAmount.toFixed(2)}`;

        // 如果输入的积分超过可用积分，自动调整
        if (pointsToUse > availablePoints) {
            const adjustedPoints = Math.round(availablePoints * 10) / 10;
            pointsToUseInput.value = adjustedPoints;
            pointsToUse = adjustedPoints;
        }

        // 如果积分抵扣金额超过总金额，自动调整为最大可抵扣积分
        if (pointsDeduction > totalAmount) {
            const adjustedPoints = Math.round((totalAmount * 10) * 10) / 10;
            pointsToUseInput.value = adjustedPoints;
            pointsToUse = adjustedPoints;
        }

        // 计算生效日期和到期日期
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);

        const currentExpireDate = this.currentRechargeMember?.expire_date;
        const currentExpire = currentExpireDate ? new Date(currentExpireDate) : null;
        const currentEffectiveDate = this.currentRechargeMember?.effective_date;
        const currentEffective = currentEffectiveDate ? new Date(currentEffectiveDate) : null;

        let startDate, newExpireDate, needT1 = false;

        // 判断是否需要T+1生效（与后端逻辑保持一致）
        if (!currentExpire) {
            // 情况1：首次充值（从未充值过）
            needT1 = true;
            startDate = tomorrow;
            newExpireDate = new Date(startDate);
            newExpireDate.setDate(newExpireDate.getDate() + days);
        } else if (currentExpire <= today) {
            // 情况2：会员已过期
            if (currentEffective && currentEffective > today) {
                // 如果有生效日期且生效日期在今天之后，保持原生效日期
                needT1 = false;
                startDate = currentEffective;
                newExpireDate = new Date(tomorrow);
                newExpireDate.setDate(newExpireDate.getDate() + days);
            } else {
                // 等同于新充值，生效日期设为第二天
                needT1 = true;
                startDate = tomorrow;
                newExpireDate = new Date(startDate);
                newExpireDate.setDate(newExpireDate.getDate() + days);
            }
        } else {
            // 情况3：会员未过期（续费充值）
            if (currentEffective) {
                // 如果已有生效日期，保持原生效日期不变，只叠加到期日期
                needT1 = false;
                startDate = currentEffective; // 保持原生效日期
                newExpireDate = new Date(currentExpire);
                newExpireDate.setDate(newExpireDate.getDate() + days);
            } else {
                // 如果没有生效日期，设置T+1生效
                needT1 = true;
                startDate = tomorrow;
                newExpireDate = new Date(currentExpire);
                newExpireDate.setDate(newExpireDate.getDate() + days);
            }
        }

        // 更新显示
        document.getElementById('summaryTotalAmount').textContent = `¥${totalAmount}`;
        document.getElementById('summaryPointsDeduction').textContent = `¥${pointsDeduction.toFixed(2)} (${this.formatPoints(maxUsablePoints)}积分)`;
        document.getElementById('summaryCashAmount').textContent = `¥${cashAmount.toFixed(2)}`;
        document.getElementById('summaryDays').textContent = `${days}天`;
        // 直接传递 Date 对象，让 formatDateWithTimezone 处理时区转换
        document.getElementById('summaryStartDate').textContent = this.formatDateWithTimezone(startDate);
        document.getElementById('summaryExpireDate').textContent = this.formatDateWithTimezone(newExpireDate);

        // 更新生效提示
        const noteElement = document.querySelector('.summary-note p');
        if (noteElement) {
            if (needT1) {
                noteElement.innerHTML = '💡 管理员操作：确认会员已付费，权益将于T+1（明天）开始生效';
            } else {
                noteElement.innerHTML = '💡 管理员操作：续费充值，保持原生效日期，延长到期时间';
            }
        }

        summaryDiv.style.display = 'block';
    }

    async handleRecharge() {
        // 重新获取DOM元素，避免缓存问题
        const packageSelect = document.getElementById('rechargePackage');
        const memberId = document.getElementById('rechargeTargetMemberId').value;

        console.log('=== 充值处理开始 ===');
        console.log('DOM元素重新获取完成');
        console.log('套餐选择元素:', packageSelect);
        console.log('当前套餐值:', packageSelect.value);
        console.log('selectedIndex:', packageSelect.selectedIndex);
        console.log('所有选项:', Array.from(packageSelect.options).map(opt => ({value: opt.value, text: opt.textContent, selected: opt.selected})));
        console.log('目标会员ID:', memberId);

        if (!packageSelect.value) {
            console.log('套餐未选择，中止充值');
            this.showToast('error', '请选择会员套餐');
            return;
        }

        try {
            this.showLoading(true);

            // 检查API是否存在
            if (!window.electronAPI || !window.electronAPI.membershipRecharge) {
                console.error('API方法不存在: window.electronAPI.membershipRecharge');
                this.showToast('error', 'API方法不存在，请重启应用程序');
                return;
            }

            // 使用多种方式获取选中的选项，避免缓存问题
            const selectedIndex = packageSelect.selectedIndex;
            const selectedOption = packageSelect.options[selectedIndex];
            const selectedOption2 = packageSelect.selectedOptions[0];

            console.log('选项获取对比:');
            console.log('- selectedIndex:', selectedIndex);
            console.log('- options[selectedIndex]:', selectedOption);
            console.log('- selectedOptions[0]:', selectedOption2);
            console.log('- 两者是否相同:', selectedOption === selectedOption2);

            if (!selectedOption || selectedIndex === 0) {
                console.error('没有选中有效的套餐选项, selectedIndex:', selectedIndex);
                this.showToast('error', '请选择有效的会员套餐');
                return;
            }

            // 直接从选项的dataset获取金额
            const amount = parseFloat(selectedOption.dataset.amount);
            const packageType = selectedOption.value;

            if (isNaN(amount) || amount <= 0) {
                console.error('套餐金额异常:', selectedOption.dataset.amount);
                this.showToast('error', '套餐金额异常，请重新选择');
                return;
            }

            console.log('=== 充值操作详细信息 ===');
            console.log('目标会员ID:', memberId);
            console.log('套餐选择元素值:', packageSelect.value);
            console.log('选中的选项:', selectedOption);
            console.log('选项文本:', selectedOption.textContent);
            console.log('选项data-amount:', selectedOption.dataset.amount);
            console.log('解析后的金额:', amount);

            // 验证套餐类型和金额的对应关系
            const expectedAmounts = {
                'half_month': 398,
                'one_month': 698,
                'two_months': 1298
            };

            if (expectedAmounts[packageSelect.value] !== amount) {
                console.error('套餐类型与金额不匹配!', {
                    packageType: packageSelect.value,
                    expectedAmount: expectedAmounts[packageSelect.value],
                    actualAmount: amount
                });
                this.showToast('error', '套餐配置异常，请刷新页面重试');
                return;
            }

            // 获取积分使用情况
            const pointsToUseInput = document.getElementById('pointsToUse');
            let pointsToUse = parseFloat(pointsToUseInput.value) || 0;
            const availablePoints = parseFloat(document.getElementById('availablePoints').textContent) || 0;

            // 四舍五入积分到一位小数
            pointsToUse = Math.round(pointsToUse * 10) / 10;

            // 计算积分抵扣金额（10积分=1元）
            const maxUsablePoints = Math.min(pointsToUse, availablePoints);
            const pointsDeductionAmount = Math.round((maxUsablePoints / 10) * 100) / 100; // 四舍五入到分
            
            console.log('=== 前端积分使用调试 ===');
            console.log('积分输入框元素:', pointsToUseInput);
            console.log('积分输入框原始值:', pointsToUseInput ? pointsToUseInput.value : 'null');
            console.log('解析后的积分值:', pointsToUse);
            console.log('可用积分:', availablePoints);
            console.log('实际使用积分:', maxUsablePoints);
            console.log('积分抵扣金额:', pointsDeductionAmount);
            console.log('=== 前端调试结束 ===');
            
            // 验证积分使用 - 使用最初点击的会员的积分进行验证
            if (pointsToUse > 0) {
                const originalMemberPoints = this.originalRechargeMember ? this.originalRechargeMember.member_points : 0;
                if (pointsToUse > originalMemberPoints) {
                    this.showToast('error', '使用积分不能超过可用积分');
                    return;
                }
            }
            
            console.log('开始充值操作:', {
                memberId: parseInt(memberId),
                packageType: packageSelect.value,
                amount: amount,
                adminId: this.currentAdmin.id,
                pointsToUse: pointsToUse,
                pointsDeductionAmount: pointsDeductionAmount
            });

            // 确定积分来源会员ID
            const pointsSourceMemberId = this.originalRechargeMember ? this.originalRechargeMember.id : null;
            
            console.log('充值参数:', {
                memberId: parseInt(memberId),
                packageType: packageSelect.value,
                amount: amount,
                adminId: this.currentAdmin.id,
                pointsToUse: pointsToUse,
                pointsDeductionAmount: pointsDeductionAmount,
                pointsSourceMemberId: pointsSourceMemberId
            });

            // 管理员操作，直接执行充值（传递积分抵扣金额而不是积分数量）
            const result = await window.electronAPI.membershipRecharge(
                parseInt(memberId),
                packageSelect.value,
                amount,
                this.currentAdmin.id,
                pointsDeductionAmount,  // 传递积分抵扣金额而不是积分数量
                pointsSourceMemberId
            );

            console.log('充值结果:', result);

            if (result && result.success) {
                this.showToast('success', '会员费用充值成功，权益将于明天生效');
                this.hideRechargeModal();
                
                // 重新加载会员列表以更新状态
                await this.loadMembers(this.currentPage);
                
                // 如果使用了积分，更新最初点击的会员的积分显示
                if (pointsToUse > 0 && this.originalRechargeMember) {
                    // 重新获取最初点击的会员信息并更新积分显示
                    const updatedMemberResult = await window.electronAPI.memberGetById(parseInt(this.originalRechargeMember.id));
                    if (updatedMemberResult.success) {
                        // 更新originalRechargeMember的积分信息
                        this.originalRechargeMember.member_points = updatedMemberResult.member.member_points;
                        // 更新弹窗中的可用积分显示
                        document.getElementById('availablePoints').textContent = this.formatPoints(updatedMemberResult.member.member_points);
                    }
                }
                
                // 如果返回了受影响的会员列表，更新这些会员的积分显示
                if (result.affectedMembers && result.affectedMembers.length > 0) {
                    console.log('更新受影响会员的积分显示:', result.affectedMembers);
                    for (const affectedMember of result.affectedMembers) {
                        await this.updateMemberPointsDisplay(affectedMember.id);
                    }
                } else {
                    // 如果没有返回受影响会员列表，至少更新充值会员的显示
                    await this.updateMemberPointsDisplay(parseInt(memberId));
                }
            } else {
                console.error('充值失败:', result);
                this.showToast('error', (result && result.message) || '充值失败');
            }
        } catch (error) {
            console.error('充值异常:', error);
            console.error('错误堆栈:', error.stack);
            this.showToast('error', `系统错误: ${error.message || error}`);
        } finally {
            this.showLoading(false);
        }
    }

    showAdjustPointsModal() {
        const modal = document.getElementById('adjustPointsModal');
        if (modal) {
            modal.classList.add('show');

            // 清空表单
            const form = document.getElementById('adjustPointsForm');
            if (form) {
                form.reset();
            }

            // 清空搜索框
            const searchInput = document.getElementById('adjustMemberSearch');
            if (searchInput) {
                searchInput.value = '';
            }

            // 加载会员选项
            this.loadAdjustMemberOptions();
        }
    }

    // 为特定会员显示调整积分弹窗
    showAdjustPointsModalForMember(member, pointsType = null) {
        const modal = document.getElementById('adjustPointsModal');
        if (modal) {
            modal.classList.add('show');

            // 清空表单
            const form = document.getElementById('adjustPointsForm');
            if (form) {
                form.reset();
            }

            // 直接设置选中的会员信息
            const memberIdDisplay = document.getElementById('displayMemberId');
            const memberNameDisplay = document.getElementById('displayMemberName');
            const hiddenMemberInput = document.getElementById('adjustMember');

            if (memberIdDisplay) {
                memberIdDisplay.textContent = member.id;
            }
            if (memberNameDisplay) {
                memberNameDisplay.textContent = member.name;
            }
            if (hiddenMemberInput) {
                hiddenMemberInput.value = member.id;
            }

            // 如果指定了积分类型，预设并禁用选择
            const pointsTypeSelect = document.getElementById('pointsType');
            if (pointsType && pointsTypeSelect) {
                pointsTypeSelect.value = pointsType;
                pointsTypeSelect.disabled = true;

                // 更新弹窗标题
                const modalTitle = modal.querySelector('.modal-header h3');
                if (modalTitle) {
                    const typeText = pointsType === 'member' ? '会员积分' : '销售积分';
                    modalTitle.textContent = `手动调整${typeText}`;
                }
            } else if (pointsTypeSelect) {
                pointsTypeSelect.disabled = false;
                // 恢复默认标题
                const modalTitle = modal.querySelector('.modal-header h3');
                if (modalTitle) {
                    modalTitle.textContent = '手动调整积分';
                }
            }

            // 控制分成设置的显示/隐藏
            const commissionGroup = document.querySelector('.form-group:has(label[for="enableCommission"])');
            if (commissionGroup) {
                commissionGroup.style.display = 'block';
                const enableCommissionYes = document.getElementById('enableCommissionYes');
                const enableCommissionNo = document.getElementById('enableCommissionNo');
                
                if (pointsType === 'sales') {
                    // 销售积分默认启用分成
                    if (enableCommissionYes) {
                        enableCommissionYes.checked = true;
                    }
                    if (enableCommissionNo) {
                        enableCommissionNo.checked = false;
                    }
                    // 更新标签文字
                    const yesLabel = enableCommissionYes?.nextElementSibling;
                    const noLabel = enableCommissionNo?.nextElementSibling;
                    if (yesLabel) yesLabel.textContent = '启用分成（默认）';
                    if (noLabel) noLabel.textContent = '不启用分成';
                } else if (pointsType === 'member') {
                    // 会员积分默认不启用分成
                    if (enableCommissionYes) {
                        enableCommissionYes.checked = false;
                    }
                    if (enableCommissionNo) {
                        enableCommissionNo.checked = true;
                    }
                    // 更新标签文字
                    const yesLabel = enableCommissionYes?.nextElementSibling;
                    const noLabel = enableCommissionNo?.nextElementSibling;
                    if (yesLabel) yesLabel.textContent = '启用分成';
                    if (noLabel) noLabel.textContent = '不启用分成（默认）';
                } else {
                    // 未指定积分类型时保持HTML默认状态（不启用分成）
                    const yesLabel = enableCommissionYes?.nextElementSibling;
                    const noLabel = enableCommissionNo?.nextElementSibling;
                    if (yesLabel) yesLabel.textContent = '启用分成';
                    if (noLabel) noLabel.textContent = '不启用分成（默认）';
                }
            }

            console.log('积分调整弹窗已为会员设置:', {
                id: member.id,
                name: member.name,
                pointsType: pointsType || '未指定'
            });
        }
    }

    hideAdjustPointsModal() {
        const modal = document.getElementById('adjustPointsModal');
        if (modal) {
            modal.classList.remove('show');

            // 重置积分类型选择框状态
            const pointsTypeSelect = document.getElementById('pointsType');
            if (pointsTypeSelect) {
                pointsTypeSelect.disabled = false;
                pointsTypeSelect.value = '';
            }

            // 恢复默认标题
            const modalTitle = modal.querySelector('.modal-header h3');
            if (modalTitle) {
                modalTitle.textContent = '手动调整积分';
            }
        }
    }

    loadAdjustMemberOptions() {
        const memberSelect = document.getElementById('adjustMember');
        const searchInput = document.getElementById('adjustMemberSearch');
        if (!memberSelect) return;

        // 保存所有会员数据用于搜索
        this.allAdjustMembers = [...this.members];

        // 初始化显示所有选项
        this.renderAdjustMemberOptions(this.allAdjustMembers);

        // 绑定搜索事件
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.filterAdjustMembers(e.target.value);
            });
        }
    }

    renderAdjustMemberOptions(members) {
        const memberSelect = document.getElementById('adjustMember');
        if (!memberSelect) return;

        // 清空现有选项
        memberSelect.innerHTML = '<option value="">请选择要调整积分的会员</option>';

        // 添加会员选项
        members.forEach(member => {
            const option = document.createElement('option');
            option.value = member.id;
            option.textContent = `${member.name} (编号: ${member.id}) - 会员积分: ${this.formatPoints(member.member_points)}, 销售积分: ${this.formatPoints(member.sales_points)}`;
            memberSelect.appendChild(option);
        });
    }

    filterAdjustMembers(keyword) {
        if (!keyword.trim()) {
            this.renderAdjustMemberOptions(this.allAdjustMembers);
            return;
        }

        const filteredMembers = this.allAdjustMembers.filter(member =>
            member.name.toLowerCase().includes(keyword.toLowerCase()) ||
            member.id.toString().includes(keyword)
        );

        this.renderAdjustMemberOptions(filteredMembers);
    }

    async handleAdjustPoints() {
        const memberId = document.getElementById('adjustMember').value;
        const pointsType = document.getElementById('pointsType').value;
        let amount = parseFloat(document.getElementById('adjustAmount').value);
        const reason = document.getElementById('adjustReason') ? document.getElementById('adjustReason').value.trim() : '管理员调整';
        
        // 获取分成设置
        const enableCommissionRadio = document.querySelector('input[name="enableCommission"]:checked');
        const enableCommission = enableCommissionRadio ? enableCommissionRadio.value === 'true' : true;

        if (!memberId) {
            this.showToast('error', '请选择要调整积分的会员');
            return;
        }

        if (!pointsType) {
            this.showToast('error', '请选择积分类型');
            return;
        }

        // 检查是否为有效数字并四舍五入到一位小数
        if (isNaN(amount) || amount === 0) {
            this.showToast('error', '请输入有效的调整数量');
            return;
        }

        // 四舍五入到一位小数
        amount = Math.round(amount * 10) / 10;

        // 根据分成设置选择不同的API方法
        const apiMethod = enableCommission ? 'pointsAdjustWithCommission' : 'pointsAdjust';
        
        // 检查API是否存在
        if (!window.electronAPI || !window.electronAPI[apiMethod]) {
            console.error(`API方法不存在: window.electronAPI.${apiMethod}`);
            this.showToast('error', 'API方法不存在，请重启应用程序');
            return;
        }

        try {
            this.showLoading(true);

            console.log('开始积分调整:', {
                memberId: parseInt(memberId),
                pointsType: pointsType,
                amount: amount,
                reason: reason,
                adminId: this.currentAdmin.id,
                enableCommission: enableCommission,
                apiMethod: apiMethod
            });

            const result = await window.electronAPI[apiMethod](
                parseInt(memberId),
                pointsType,
                amount,
                reason,
                this.currentAdmin.id
            );

            console.log('积分调整结果:', result);

            if (result && result.success) {
                console.log('=== 积分调整成功，开始后续处理 ===');

                // 根据是否触发分成显示不同的消息
                if (enableCommission && result.commissionTriggered && result.affectedMembers && result.affectedMembers.length > 0) {
                    this.showToast('success', `积分调整成功！已触发分成机制，影响${result.affectedMembers.length}个上级用户`);
                    console.log('分成详情:', result.affectedMembers);
                } else if (enableCommission && amount > 0) {
                    this.showToast('success', '积分调整成功！未找到有效期内的上级用户，未触发分成');
                } else {
                    this.showToast('success', '积分调整成功');
                }

                this.hideAdjustPointsModal();

                // 重新加载会员数据（这会自动显示最新的积分）
                await this.loadMembers(this.currentPage);
            } else {
                console.error('积分调整失败:', result);
                this.showToast('error', (result && result.message) || '积分调整失败');
            }
        } catch (error) {
            console.error('积分调整异常:', error);
            console.error('错误堆栈:', error.stack);
            this.showToast('error', `积分调整失败: ${error.message || error}`);
        } finally {
            this.showLoading(false);
        }
    }

    async handleClearPoints(pointsType) {
        const memberId = document.getElementById('adjustMember').value;

        if (!memberId) {
            this.showToast('error', '请先选择要清零积分的会员');
            return;
        }

        const pointsTypeName = pointsType === 'member' ? '会员积分' : '销售积分';

        // 显示自定义确认对话框
        this.showClearConfirmModal(pointsTypeName, pointsType, memberId);
    }

    showClearConfirmModal(pointsTypeName, pointsType, memberId) {
        const modal = document.getElementById('clearPointsConfirmModal');
        const messageElement = document.getElementById('clearConfirmMessage');

        if (modal && messageElement) {
            messageElement.textContent = `确定要将该会员的${pointsTypeName}清零吗？`;
            modal.classList.add('show');

            // 保存清零参数
            this.clearPointsParams = {
                pointsType: pointsType,
                pointsTypeName: pointsTypeName,
                memberId: memberId
            };
        }
    }

    hideClearConfirmModal() {
        const modal = document.getElementById('clearPointsConfirmModal');
        if (modal) {
            modal.classList.remove('show');
            this.clearPointsParams = null;
        }
    }

    async executeClearPoints() {
        if (!this.clearPointsParams) {
            return;
        }

        const { pointsType, pointsTypeName, memberId } = this.clearPointsParams;

        // 隐藏确认对话框
        this.hideClearConfirmModal();

        // 检查API是否存在
        if (!window.electronAPI || !window.electronAPI.pointsAdjust) {
            console.error('API方法不存在: window.electronAPI.pointsAdjust');
            this.showToast('error', 'API方法不存在，请重启应用程序');
            return;
        }

        try {
            this.showLoading(true);

            // 获取当前会员的积分信息
            const member = await window.electronAPI.memberGetById(parseInt(memberId));
            if (!member || !member.success) {
                this.showToast('error', '获取会员信息失败');
                return;
            }

            const currentPoints = pointsType === 'member' ? member.member.member_points : member.member.sales_points;

            // 如果积分已经是0，不需要操作
            if (currentPoints === 0) {
                this.showToast('info', `该会员的${pointsTypeName}已经是0，无需清零`);
                return;
            }

            // 计算清零需要的调整数量（负数）
            const adjustAmount = -currentPoints;
            const reason = `${pointsTypeName}清零操作`;

            console.log('开始积分清零:', {
                memberId: parseInt(memberId),
                pointsType: pointsType,
                currentPoints: currentPoints,
                adjustAmount: adjustAmount,
                reason: reason,
                adminId: this.currentAdmin.id
            });

            const result = await window.electronAPI.pointsAdjust(
                parseInt(memberId),
                pointsType,
                adjustAmount,
                reason,
                this.currentAdmin.id
            );

            console.log('积分清零结果:', result);

            if (result && result.success) {
                console.log('=== 积分清零成功，开始后续处理 ===');
                this.showToast('success', `${pointsTypeName}清零成功`);

                // 重新加载会员数据（这会自动显示最新的积分）
                await this.loadMembers(this.currentPage);
            } else {
                console.error('积分清零失败:', result);
                this.showToast('error', (result && result.message) || `${pointsTypeName}清零失败`);
            }
        } catch (error) {
            console.error('积分清零异常:', error);
            console.error('错误堆栈:', error.stack);
            this.showToast('error', `${pointsTypeName}清零失败: ${error.message || error}`);
        } finally {
            this.showLoading(false);
        }
    }



    // ==================== 操作记录相关方法 ====================

    async loadOperationLogs(filters = {}, page = 1) {
        try {
            this.showLoading(true);
            this.currentLogsPage = page;
            this.currentLogsFilters = filters;

            const offset = (page - 1) * this.pageSize;
            const result = await window.electronAPI.operationLogsGet(filters, this.pageSize, offset, this.currentAdmin?.id);

            if (result.success) {
                this.operationLogs = result.logs || [];
                this.renderOperationLogs();

                // 获取总数用于分页
                await this.loadOperationLogsCount(filters);
                this.renderLogsPagination();
            } else {
                console.error('获取操作记录失败:', result.message);
                this.showToast('error', '获取操作记录失败');
            }
        } catch (error) {
            console.error('获取操作记录异常:', error);
            this.showToast('error', '获取操作记录失败');
        } finally {
            this.showLoading(false);
        }
    }

    async loadOperationLogsCount(filters = {}) {
        try {
            const result = await window.electronAPI.operationLogsGetCount(filters, this.currentAdmin?.id);
            if (result.success) {
                this.totalLogsCount = result.count || 0;
                console.log('操作记录总数:', this.totalLogsCount, '每页记录数:', this.pageSize, '总页数:', Math.ceil(this.totalLogsCount / this.pageSize));
                this.updateLogsCount();
            }
        } catch (error) {
            console.error('获取操作记录总数失败:', error);
            this.totalLogsCount = 0;
        }
    }

    renderOperationLogs() {
        const tbody = document.getElementById('operationLogsTableBody');
        if (!tbody) return;

        if (this.operationLogs.length === 0) {
            tbody.innerHTML = '<tr><td colspan="5" class="no-data">暂无操作记录</td></tr>';
            return;
        }

        tbody.innerHTML = this.operationLogs.map(log => `
            <tr>
                <td>${this.formatDateTimeWithTimezone(log.created_at)}</td>
                <td>${log.operator_name || log.admin_name || '未知'}</td>
                <td>${this.getOperationTypeText(log.operation_type)}</td>
                <td>${this.getTargetText(log.target_type, log.target_name, log.target_id)}</td>
                <td title="${log.operation_details}">${this.truncateText(log.operation_details, 50)}</td>
            </tr>
        `).join('');
    }

    getOperationTypeText(type) {
        const typeMap = {
            'create': '新增会员',
            'update': '编辑会员',
            'delete': '删除会员',
            'recharge': '会员充值',
            'points_adjust': '积分调整'
        };
        return typeMap[type] || type;
    }

    getTargetText(type, name, id) {
        if (type === 'member') {
            return `${name || '未知会员'} (编号: ${id})`;
        }
        return `${name || id || '未知'}`;
    }

    truncateText(text, maxLength) {
        if (!text) return '';
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength) + '...';
    }

    updateLogsCount() {
        const totalElement = document.getElementById('totalLogs');
        if (totalElement) {
            totalElement.textContent = this.totalLogsCount || 0;
        }
    }

    renderLogsPagination() {
        const paginationContainer = document.getElementById('logsPagination');
        if (!paginationContainer) return;

        const totalPages = Math.ceil((this.totalLogsCount || 0) / this.pageSize);
        const currentPage = this.currentLogsPage || 1;

        if (totalPages <= 1) {
            paginationContainer.innerHTML = '';
            return;
        }

        let paginationHTML = '';

        // 上一页按钮
        if (currentPage > 1) {
            paginationHTML += `<button onclick="memberManagement.loadOperationLogs(memberManagement.currentLogsFilters, ${currentPage - 1})">上一页</button>`;
        }

        // 页码按钮
        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, currentPage + 2);

        if (startPage > 1) {
            paginationHTML += `<button onclick="memberManagement.loadOperationLogs(memberManagement.currentLogsFilters, 1)">1</button>`;
            if (startPage > 2) {
                paginationHTML += `<span>...</span>`;
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            const activeClass = i === currentPage ? 'active' : '';
            paginationHTML += `<button class="${activeClass}" onclick="memberManagement.loadOperationLogs(memberManagement.currentLogsFilters, ${i})">${i}</button>`;
        }

        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                paginationHTML += `<span>...</span>`;
            }
            paginationHTML += `<button onclick="memberManagement.loadOperationLogs(memberManagement.currentLogsFilters, ${totalPages})">${totalPages}</button>`;
        }

        // 下一页按钮
        if (currentPage < totalPages) {
            paginationHTML += `<button onclick="memberManagement.loadOperationLogs(memberManagement.currentLogsFilters, ${currentPage + 1})">下一页</button>`;
        }

        paginationContainer.innerHTML = paginationHTML;
    }

    // 绑定操作记录相关事件
    bindOperationLogsEvents() {
        const searchBtn = document.getElementById('searchLogsBtn');
        const clearFilterBtn = document.getElementById('clearFilterBtn');
        const targetFilter = document.getElementById('targetFilter');

        if (searchBtn) {
            searchBtn.addEventListener('click', () => {
                this.filterOperationLogs();
            });
        }

        if (clearFilterBtn) {
            clearFilterBtn.addEventListener('click', () => {
                this.clearLogsFilter();
            });
        }

        // 目标对象搜索框实时搜索
        if (targetFilter) {
            let searchTimeout;
            targetFilter.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    this.filterOperationLogs();
                }, 300); // 300ms延迟，避免频繁搜索
            });

            // 回车键搜索
            targetFilter.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.filterOperationLogs();
                }
            });
        }

        // 操作类型下拉框变化时立即搜索
        const operationTypeFilter = document.getElementById('operationTypeFilter');
        if (operationTypeFilter) {
            operationTypeFilter.addEventListener('change', () => {
                this.filterOperationLogs();
            });
        }

        // 日期筛选变化时立即搜索
        const startDateFilter = document.getElementById('startDateFilter');
        const endDateFilter = document.getElementById('endDateFilter');

        if (startDateFilter) {
            startDateFilter.addEventListener('change', () => {
                this.filterOperationLogs();
            });
        }

        if (endDateFilter) {
            endDateFilter.addEventListener('change', () => {
                this.filterOperationLogs();
            });
        }
    }

    filterOperationLogs() {
        const operationType = document.getElementById('operationTypeFilter').value;
        const targetFilter = document.getElementById('targetFilter').value.trim();
        const startDate = document.getElementById('startDateFilter').value;
        const endDate = document.getElementById('endDateFilter').value;

        const filters = {};
        if (operationType) filters.operationType = operationType;
        if (targetFilter) filters.keyword = targetFilter;
        if (startDate) filters.startDate = startDate + ' 00:00:00';
        if (endDate) filters.endDate = endDate + ' 23:59:59';

        this.loadOperationLogs(filters, 1);
    }

    clearLogsFilter() {
        document.getElementById('operationTypeFilter').value = '';
        document.getElementById('targetFilter').value = '';
        document.getElementById('startDateFilter').value = '';
        document.getElementById('endDateFilter').value = '';
        this.loadOperationLogs({}, 1);
    }

    // 更新页面中指定会员的积分显示（包括搜索结果和主列表）
    async updateMemberPointsDisplay(memberId) {
        try {
            console.log('开始更新页面中会员ID为', memberId, '的积分显示');

            // 获取最新的会员信息
            const result = await window.electronAPI.memberGetById(memberId);
            if (!result.success) {
                console.log('获取会员信息失败，无法更新积分显示');
                return;
            }

            const member = result.member;
            console.log('获取到最新会员信息:', member);

            // 1. 更新搜索结果中的积分显示
            await this.updateSearchResultPoints(memberId, member);

            // 2. 更新主会员列表中的积分显示
            await this.updateMainListPoints(memberId, member);

        } catch (error) {
            console.error('更新积分显示失败:', error);
        }
    }

    // 更新搜索结果中指定会员的积分显示
    async updateSearchResultPoints(memberId, memberInfo = null) {
        try {
            console.log('开始更新搜索结果中会员ID为', memberId, '的积分显示');

            // 检查搜索结果容器是否存在
            const searchResults = document.getElementById('rechargeSearchResults');
            if (!searchResults) {
                console.log('未找到搜索结果容器，可能没有进行搜索');
                return;
            }

            // 检查是否有搜索结果
            const memberItems = searchResults.querySelectorAll('.member-search-item');
            console.log('找到搜索结果项数量:', memberItems.length);

            if (memberItems.length === 0) {
                console.log('搜索结果为空，无需更新');
                return;
            }

            // 获取会员信息（如果没有传入的话）
            let member = memberInfo;
            if (!member) {
                const result = await window.electronAPI.memberGetById(memberId);
                if (!result.success) {
                    console.log('获取会员信息失败，无法更新搜索结果');
                    return;
                }
                member = result.member;
            }
            console.log('获取到最新会员信息:', member);

            // 查找包含该会员ID的搜索项
            let found = false;
            for (const item of memberItems) {
                // 检查这个项目是否是目标会员（通过onclick属性中的ID判断）
                const onclickAttr = item.getAttribute('onclick');
                console.log('检查搜索项onclick属性:', onclickAttr);

                if (onclickAttr && onclickAttr.includes(`memberManagement.showRechargeModal(${memberId})`)) {
                    console.log('找到匹配的会员项，开始更新积分显示');

                    // 找到了对应的会员项，更新积分显示
                    const memberDetails = item.querySelector('.member-details');
                    if (memberDetails) {
                        const spans = memberDetails.querySelectorAll('span');
                        spans.forEach(span => {
                            const originalText = span.textContent;
                            if (span.textContent.includes('会员积分:')) {
                                span.textContent = `会员积分: ${this.formatPoints(member.member_points)}`;
                                console.log('更新会员积分:', originalText, '->', span.textContent);
                            } else if (span.textContent.includes('销售积分:')) {
                                span.textContent = `销售积分: ${this.formatPoints(member.sales_points)}`;
                                console.log('更新销售积分:', originalText, '->', span.textContent);
                            }
                        });
                    }
                    console.log(`已更新搜索结果中会员${member.name}的积分显示`);
                    found = true;
                    break;
                }
            }

            if (!found) {
                console.log('在搜索结果中未找到会员ID为', memberId, '的项目');
            }
        } catch (error) {
            console.error('更新搜索结果积分显示失败:', error);
        }
    }

    // 更新主会员列表中指定会员的积分显示
    async updateMainListPoints(memberId, member) {
        try {
            console.log('开始更新主列表中会员ID为', memberId, '的积分显示');

            // 查找主会员列表表格
            const tableBody = document.getElementById('membersTableBody');
            if (!tableBody) {
                console.log('未找到主会员列表表格');
                return;
            }

            // 查找对应的会员行
            const rows = tableBody.querySelectorAll('tr');
            console.log('主列表中找到行数:', rows.length);

            let found = false;
            for (const row of rows) {
                const cells = row.querySelectorAll('td');
                if (cells.length > 0) {
                    // 第一列是会员ID
                    const idCell = cells[0];
                    if (idCell && idCell.textContent.trim() === memberId.toString()) {
                        console.log('找到匹配的会员行，开始更新积分');

                        // 第4列是会员积分，第5列是销售积分，第6列是生效日期，第7列是到期日期（基于0的索引：ID(0)、名称(1)、层级(2)、会员积分(3)、销售积分(4)、生效日期(5)、到期日期(6)...）
                        if (cells[3]) {
                            const oldMemberPoints = cells[3].textContent;
                            cells[3].textContent = this.formatPoints(member.member_points);
                            console.log('更新会员积分:', oldMemberPoints, '->', cells[3].textContent);
                        }

                        if (cells[4]) {
                            const oldSalesPoints = cells[4].textContent;
                            cells[4].textContent = this.formatPoints(member.sales_points);
                            console.log('更新销售积分:', oldSalesPoints, '->', cells[4].textContent);
                        }

                        // 更新生效日期显示
                        if (cells[5]) {
                            const oldEffectiveDate = cells[5].innerHTML;
                            cells[5].innerHTML = this.getEffectiveDateDisplay(member.effective_date);
                            console.log('更新生效日期:', oldEffectiveDate, '->', cells[5].innerHTML);
                        }

                        // 更新到期日期显示
                        if (cells[6]) {
                            const oldExpireDate = cells[6].innerHTML;
                            cells[6].innerHTML = this.getMemberStatusDisplay(member);
                            console.log('更新到期日期:', oldExpireDate, '->', cells[6].innerHTML);
                        }

                        console.log(`已更新主列表中会员${member.name}的积分显示`);
                        found = true;
                        break;
                    }
                }
            }

            if (!found) {
                console.log('在主列表中未找到会员ID为', memberId, '的行');
            }
        } catch (error) {
            console.error('更新主列表积分显示失败:', error);
        }
    }

    // ==================== 积分配置相关方法 ====================

    async loadPointsConfig() {
        try {
            // 检查权限
            if (!this.currentAdmin) {
                this.showToast('error', '管理员信息不存在');
                return;
            }

            if (!this.currentAdmin.permissions) {
                this.showToast('error', '权限信息不存在');
                return;
            }

            if (!this.currentAdmin.permissions.canViewPointsConfig) {
                this.showToast('error', '没有访问积分配置的权限');
                return;
            }

            this.showLoading(true);

            const result = await window.electronAPI.levelRatiosGetAll(this.currentAdmin.id);

            if (result.success) {
                this.renderPointsConfigTable(result.ratios || []);
                this.bindPointsConfigEvents();
            } else {
                this.showToast('error', result.message || '加载积分配置失败');
            }
        } catch (error) {
            console.error('加载积分配置失败:', error);
            this.showToast('error', '加载积分配置失败');
        } finally {
            this.showLoading(false);
        }
    }

    renderPointsConfigTable(ratios) {
        const tableBody = document.getElementById('pointsConfigTableBody');
        if (!tableBody) return;

        if (ratios.length === 0) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="5" style="text-align: center; padding: 40px; color: #999;">
                        暂无配置数据
                    </td>
                </tr>
            `;
            return;
        }

        tableBody.innerHTML = ratios.map(ratio => `
            <tr data-level="${ratio.level}">
                <td>
                    <div class="level-badge level-${ratio.level}">${ratio.level}</div>
                </td>
                <td>${ratio.description || `第${ratio.level}层`}</td>
                <td>
                    <span class="ratio-display member-ratio" data-level="${ratio.level}" data-type="member">
                        ${ratio.member_ratio}%
                    </span>
                </td>
                <td>
                    <span class="ratio-display sales-ratio" data-level="${ratio.level}" data-type="sales">
                        ${ratio.sales_ratio}%
                    </span>
                </td>

            </tr>
        `).join('');
    }

    bindPointsConfigEvents() {
        // 保存配置按钮
        const saveBtn = document.getElementById('saveBtn');
        if (saveBtn) {
            saveBtn.addEventListener('click', () => {
                this.savePointsConfig();
            });
        }

        // 重置为默认按钮
        const resetBtn = document.getElementById('resetBtn');
        if (resetBtn) {
            resetBtn.addEventListener('click', () => {
                this.resetAllPointsConfig();
            });
        }

        // 输入框变化事件
        const ratioInputs = document.querySelectorAll('.ratio-input');
        ratioInputs.forEach(input => {
            input.addEventListener('input', (e) => {
                this.validateRatioInput(e.target);
            });
        });
    }

    validateRatioInput(input) {
        let value = parseFloat(input.value);
        if (isNaN(value) || value < 0) {
            input.value = 0;
        } else if (value > 100) {
            input.value = 100;
        }
    }

    async savePointsConfig() {
        try {
            // 检查权限
            if (!this.currentAdmin || !this.currentAdmin.permissions.canViewPointsConfig) {
                this.showToast('error', '没有修改积分配置的权限');
                return;
            }

            this.showLoading(true);

            // 收集所有配置数据
            const ratios = [];
            const rows = document.querySelectorAll('#pointsConfigTableBody tr[data-level]');

            rows.forEach(row => {
                const level = parseInt(row.dataset.level);
                const memberRatio = parseFloat(row.querySelector('.member-ratio').value) || 0;
                const salesRatio = parseFloat(row.querySelector('.sales-ratio').value) || 0;
                const description = row.querySelector('td:nth-child(2)').textContent.trim();

                ratios.push({
                    level: level,
                    salesRatio: salesRatio,
                    memberRatio: memberRatio,
                    description: description
                });
            });

            const result = await window.electronAPI.levelRatiosBatchUpdate(ratios, this.currentAdmin.id);
            if (result.success) {
                this.showToast('success', '积分配置保存成功');
            } else {
                this.showToast('error', result.message || '保存配置失败');
            }
        } catch (error) {
            console.error('保存积分配置失败:', error);
            this.showToast('error', '保存配置失败');
        } finally {
            this.showLoading(false);
        }
    }

    async resetAllPointsConfig() {
        const confirmed = await this.showAdminActionConfirm(
            '重置积分配置',
            '确定要重置所有配置为默认值吗？',
            true
        );
        
        if (!confirmed) {
            return;
        }

        try {
            this.showLoading(true);

            const result = await window.electronAPI.levelRatiosReset();
            if (result.success) {
                this.showToast('success', '重置为默认配置成功');
                // 重新加载配置数据
                await this.loadPointsConfig();
            } else {
                this.showToast('error', result.message || '重置配置失败');
            }
        } catch (error) {
            console.error('重置积分配置失败:', error);
            this.showToast('error', '重置配置失败');
        } finally {
            this.showLoading(false);
        }
    }

    async resetLevelRatio(level) {
        // 获取默认值
        const defaultRatios = {
            1: { sales: 100.0, member: 10.0 },
            2: { sales: 100.0, member: 8.0 },
            3: { sales: 50.0, member: 6.0 },
            4: { sales: 10.0, member: 4.0 },
            5: { sales: 10.0, member: 3.0 },
            6: { sales: 10.0, member: 2.5 },
            7: { sales: 10.0, member: 2.0 },
            8: { sales: 10.0, member: 1.5 },
            9: { sales: 10.0, member: 1.0 },
            10: { sales: 10.0, member: 0.8 },
            11: { sales: 10.0, member: 0.5 },
            12: { sales: 10.0, member: 0.3 }
        };

        const defaultValue = defaultRatios[level];
        if (!defaultValue) return;

        // 更新输入框
        const row = document.querySelector(`tr[data-level="${level}"]`);
        if (row) {
            row.querySelector('.member-ratio').value = defaultValue.member;
            row.querySelector('.sales-ratio').value = defaultValue.sales;
        }
    }

    // 构建会员层级树
    buildMemberTree() {
        const treeContainer = document.getElementById('memberTree');
        if (!treeContainer) return;

        try {
            treeContainer.innerHTML = '<div class="tree-loading">正在加载层级关系...</div>';

            // 构建树形结构
            const treeData = this.buildTreeData();

            if (treeData.length === 0) {
                treeContainer.innerHTML = '<div class="tree-empty">暂无会员数据</div>';
                return;
            }

            // 渲染树形结构
            const treeHTML = this.renderTreeNodes(treeData);
            treeContainer.innerHTML = treeHTML;

            // 绑定树节点事件
            this.bindTreeNodeEvents();

            // 更新按钮状态
            this.updateTreeActionButtons();

        } catch (error) {
            console.error('构建会员树失败:', error);
            treeContainer.innerHTML = '<div class="tree-error">加载层级关系失败，请刷新重试</div>';
        }
    }

    // 构建树形数据结构
    buildTreeData() {
        // 使用完整的会员数据构建层级关系图，不受搜索和分页影响
        const membersData = this.originalAllMembers.length > 0 ? this.originalAllMembers : this.allMembers;

        if (!membersData || membersData.length === 0) {
            return [];
        }

        // 创建会员映射
        const memberMap = new Map();
        membersData.forEach(member => {
            memberMap.set(member.id, {
                ...member,
                children: []
            });
        });

        // 构建父子关系
        const rootNodes = [];
        membersData.forEach(member => {
            const memberNode = memberMap.get(member.id);

            if (member.parent_id && memberMap.has(member.parent_id)) {
                // 有上级，添加到上级的children中
                const parent = memberMap.get(member.parent_id);
                parent.children.push(memberNode);
            } else {
                // 无上级，是根节点
                rootNodes.push(memberNode);
            }
        });

        return rootNodes;
    }

    // 渲染树节点
    renderTreeNodes(nodes, level = 1) {
        if (!nodes || nodes.length === 0) {
            return '';
        }

        return nodes.map(node => {
            const hasChildren = node.children && node.children.length > 0;
            const levelClass = level === 1 ? 'level-1' :
                              level === 2 ? 'level-2' :
                              level === 3 ? 'level-3' :
                              level === 4 ? 'level-4' :
                              level === 5 ? 'level-5' :
                              level === 6 ? 'level-6' :
                              level === 7 ? 'level-7' :
                              level === 8 ? 'level-8' :
                              level === 9 ? 'level-9' :
                              level === 10 ? 'level-10' : 'level-high';

            const childrenHTML = hasChildren ?
                `<div class="tree-children collapsed" data-member-id="${node.id}">
                    ${this.renderTreeNodes(node.children, level + 1)}
                </div>` : '';

            return `
                <div class="tree-node" data-member-id="${node.id}">
                    <div class="tree-node-content" data-member-id="${node.id}" data-level="${level}">
                        <button class="tree-toggle ${hasChildren ? '' : 'no-children'}"
                                data-member-id="${node.id}">
                            ${hasChildren ? '+' : '•'}
                        </button>
                        <div class="tree-member-info">
                            <span class="tree-member-name">${node.name}</span>
                            <span class="tree-member-level ${levelClass}">${level}</span>
                            <div class="tree-member-stats">
                                <span>下级: ${node.children ? node.children.length : 0}</span>
                                <span>编号: ${node.id}</span>
                            </div>
                        </div>
                    </div>
                    ${childrenHTML}
                </div>
            `;
        }).join('');
    }

    // 绑定树节点事件
    bindTreeNodeEvents() {
        const toggleButtons = document.querySelectorAll('.tree-toggle');

        toggleButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.stopPropagation();

                if (button.classList.contains('no-children')) {
                    return;
                }

                const memberId = button.dataset.memberId;
                this.toggleTreeNode(memberId);
            });
        });

        // 节点内容点击事件
        const nodeContents = document.querySelectorAll('.tree-node-content');
        nodeContents.forEach(content => {
            // 单击选择节点
            content.addEventListener('click', (e) => {
                if (e.target.classList.contains('tree-toggle')) {
                    return;
                }

                const memberId = parseInt(content.dataset.memberId);
                this.selectTreeNode(memberId);
            });

            // 双击展开/收起节点
            content.addEventListener('dblclick', (e) => {
                if (e.target.classList.contains('tree-toggle')) {
                    return;
                }

                const memberId = parseInt(content.dataset.memberId);
                this.toggleTreeNode(memberId);
            });

            // 添加右键点击事件
            content.addEventListener('contextmenu', (e) => {
                const memberId = parseInt(content.dataset.memberId);
                if (memberId) {
                    this.showContextMenu(e, memberId);
                }
            });
        });
    }

    // 切换树节点展开/收起状态
    toggleTreeNode(memberId) {
        const childrenContainer = document.querySelector(`.tree-children[data-member-id="${memberId}"]`);
        const toggleButton = document.querySelector(`.tree-toggle[data-member-id="${memberId}"]`);
        const nodeContent = document.querySelector(`.tree-node-content[data-member-id="${memberId}"]`);

        if (!childrenContainer || !toggleButton) return;

        const isCollapsed = childrenContainer.classList.contains('collapsed');

        if (isCollapsed) {
            // 展开
            childrenContainer.classList.remove('collapsed');
            toggleButton.textContent = '−';
            nodeContent.classList.add('expanded');
        } else {
            // 收起
            childrenContainer.classList.add('collapsed');
            toggleButton.textContent = '+';
            nodeContent.classList.remove('expanded');
        }

        // 如果这是当前选中的节点，更新按钮状态
        if (this.selectedTreeNodeId === memberId) {
            this.updateTreeActionButtons();
        }
    }

    // 展开所有节点
    expandAllTreeNodes() {
        const childrenContainers = document.querySelectorAll('.tree-children');
        const toggleButtons = document.querySelectorAll('.tree-toggle:not(.no-children)');
        const nodeContents = document.querySelectorAll('.tree-node-content');

        childrenContainers.forEach(container => {
            container.classList.remove('collapsed');
            container.style.display = ''; // 重置内联样式
        });

        toggleButtons.forEach(button => {
            button.textContent = '−';
            button.classList.remove('collapsed');
            button.classList.add('expanded');
        });

        nodeContents.forEach(content => {
            content.classList.add('expanded');
        });
    }

    // 收起所有节点
    collapseAllTreeNodes() {
        const childrenContainers = document.querySelectorAll('.tree-children');
        const toggleButtons = document.querySelectorAll('.tree-toggle:not(.no-children)');
        const nodeContents = document.querySelectorAll('.tree-node-content');

        childrenContainers.forEach(container => {
            container.classList.add('collapsed');
            container.style.display = 'none'; // 确保隐藏
        });

        toggleButtons.forEach(button => {
            button.textContent = '+';
            button.classList.remove('expanded');
            button.classList.add('collapsed');
        });

        nodeContents.forEach(content => {
            content.classList.remove('expanded');
        });
    }

    // 绑定右键菜单事件
    bindContextMenuEvents() {
        // 如果已经绑定过，直接返回
        if (this.contextMenuEventsBound) {
            return;
        }

        const contextMenu = document.getElementById('treeContextMenu');
        const addSubMemberItem = document.getElementById('addSubMemberItem');
        const editMemberItem = document.getElementById('editMemberItem');

        if (!contextMenu || !addSubMemberItem || !editMemberItem) {
            console.log('右键菜单元素未找到');
            return;
        }

        // 存储当前右键点击的会员ID
        this.contextMenuMemberId = null;

        // 绑定添加下级会员事件
        addSubMemberItem.addEventListener('click', (e) => {
            e.stopPropagation();
            const memberId = this.contextMenuMemberId;
            this.hideContextMenu();

            if (memberId) {
                this.showAddMemberModalWithParent(memberId);
            }
        });

        // 绑定编辑会员事件
        editMemberItem.addEventListener('click', (e) => {
            e.stopPropagation();
            const memberId = this.contextMenuMemberId;
            this.hideContextMenu();

            if (memberId) {
                this.editMember(memberId);
            }
        });

        // 点击其他地方隐藏菜单
        document.addEventListener('click', (e) => {
            if (!contextMenu.contains(e.target)) {
                this.hideContextMenu();
            }
        });

        // 阻止右键菜单的默认行为
        contextMenu.addEventListener('contextmenu', (e) => {
            e.preventDefault();
        });

        // 标记已绑定
        this.contextMenuEventsBound = true;
    }

    // 显示右键菜单
    showContextMenu(e, memberId) {
        e.preventDefault();
        e.stopPropagation();



        const contextMenu = document.getElementById('treeContextMenu');
        if (!contextMenu) {
            console.log('右键菜单元素未找到');
            return;
        }

        this.contextMenuMemberId = memberId;

        // 设置菜单位置
        const x = e.clientX;
        const y = e.clientY;

        // 确保菜单不会超出视窗
        const menuWidth = 160;
        const menuHeight = 80;
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

        let menuX = x;
        let menuY = y;

        if (x + menuWidth > viewportWidth) {
            menuX = x - menuWidth;
        }

        if (y + menuHeight > viewportHeight) {
            menuY = y - menuHeight;
        }

        contextMenu.style.left = `${menuX}px`;
        contextMenu.style.top = `${menuY}px`;
        contextMenu.style.display = 'block';
    }

    // 隐藏右键菜单
    hideContextMenu() {
        const contextMenu = document.getElementById('treeContextMenu');
        if (contextMenu) {
            contextMenu.style.display = 'none';
        }
        this.contextMenuMemberId = null;
    }

    // 显示添加会员弹窗并设置上级会员
    showAddMemberModalWithParent(parentId) {
        console.log('=== 进入 showAddMemberModalWithParent 方法，上级会员ID:', parentId);

        // 显示添加会员弹窗，并传入默认父会员ID
        this.showAddMemberModal(parentId);
    }

    // 选择树节点
    selectTreeNode(memberId) {
        // 移除之前选中节点的样式
        const previousSelected = document.querySelector('.tree-node-content.selected');
        if (previousSelected) {
            previousSelected.classList.remove('selected');
        }

        // 添加当前选中节点的样式
        const currentNode = document.querySelector(`.tree-node-content[data-member-id="${memberId}"]`);
        if (currentNode) {
            currentNode.classList.add('selected');
            this.selectedTreeNodeId = memberId;
        }

        // 更新按钮状态
        this.updateTreeActionButtons();
    }

    // 更新树操作按钮状态
    updateTreeActionButtons() {
        const toggleCurrentBtn = document.getElementById('toggleCurrentBtn');

        if (this.selectedTreeNodeId) {
            // 检查选中节点是否有子节点
            const hasChildren = this.hasChildrenInTree(this.selectedTreeNodeId);

            if (toggleCurrentBtn) {
                toggleCurrentBtn.disabled = !hasChildren;

                if (hasChildren) {
                    // 检查当前节点是否展开
                    const isExpanded = this.isTreeNodeExpanded(this.selectedTreeNodeId);
                    toggleCurrentBtn.textContent = isExpanded ? '收起当前' : '展开当前';
                }
            }
        } else {
            // 没有选中节点时禁用按钮
            if (toggleCurrentBtn) {
                toggleCurrentBtn.disabled = true;
                toggleCurrentBtn.textContent = '展开当前';
            }
        }
    }

    // 检查节点是否有子节点
    hasChildrenInTree(memberId) {
        const childrenContainer = document.querySelector(`.tree-children[data-member-id="${memberId}"]`);
        return childrenContainer && childrenContainer.children.length > 0;
    }

    // 检查节点是否展开
    isTreeNodeExpanded(memberId) {
        const childrenContainer = document.querySelector(`.tree-children[data-member-id="${memberId}"]`);
        return childrenContainer && !childrenContainer.classList.contains('collapsed');
    }

    // 切换当前选中节点的展开/收起状态
    toggleCurrentTreeNode() {
        if (!this.selectedTreeNodeId) {
            this.showToast('warning', '请先选择一个会员节点');
            return;
        }

        const isExpanded = this.isTreeNodeExpanded(this.selectedTreeNodeId);

        if (isExpanded) {
            // 当前是展开状态，执行收起
            this.collapseTreeNodeRecursively(this.selectedTreeNodeId);
        } else {
            // 当前是收起状态，执行展开
            this.expandTreeNodeRecursively(this.selectedTreeNodeId);
        }

        // 更新按钮状态
        this.updateTreeActionButtons();
    }

    // 递归展开节点及其所有子节点
    expandTreeNodeRecursively(memberId) {
        const childrenContainer = document.querySelector(`.tree-children[data-member-id="${memberId}"]`);
        const toggleButton = document.querySelector(`.tree-toggle[data-member-id="${memberId}"]`);
        const nodeContent = document.querySelector(`.tree-node-content[data-member-id="${memberId}"]`);

        if (childrenContainer && toggleButton) {
            // 展开当前节点
            childrenContainer.classList.remove('collapsed');
            childrenContainer.style.display = ''; // 重置内联样式
            toggleButton.textContent = '−';
            toggleButton.classList.remove('collapsed');
            toggleButton.classList.add('expanded');
            if (nodeContent) {
                nodeContent.classList.add('expanded');
            }

            // 递归展开所有子节点
            const childNodes = childrenContainer.querySelectorAll('.tree-node[data-member-id]');
            childNodes.forEach(childNode => {
                const childMemberId = parseInt(childNode.dataset.memberId);
                if (childMemberId) {
                    this.expandTreeNodeRecursively(childMemberId);
                }
            });
        }
    }

    // 递归收起节点及其所有子节点
    collapseTreeNodeRecursively(memberId) {
        const childrenContainer = document.querySelector(`.tree-children[data-member-id="${memberId}"]`);
        const toggleButton = document.querySelector(`.tree-toggle[data-member-id="${memberId}"]`);
        const nodeContent = document.querySelector(`.tree-node-content[data-member-id="${memberId}"]`);

        if (childrenContainer && toggleButton) {
            // 先递归收起所有子节点
            const childNodes = childrenContainer.querySelectorAll('.tree-node[data-member-id]');
            childNodes.forEach(childNode => {
                const childMemberId = parseInt(childNode.dataset.memberId);
                if (childMemberId) {
                    this.collapseTreeNodeRecursively(childMemberId);
                }
            });

            // 收起当前节点
            childrenContainer.classList.add('collapsed');
            childrenContainer.style.display = 'none'; // 确保隐藏
            toggleButton.textContent = '+';
            toggleButton.classList.remove('expanded');
            toggleButton.classList.add('collapsed');
            if (nodeContent) {
                nodeContent.classList.remove('expanded');
            }
        }
    }

    // 自动刷新层级关系图
    autoRefreshHierarchyTree() {
        // 检查当前是否在层级关系标签页
        const hierarchyTab = document.getElementById('hierarchy-tab');
        if (hierarchyTab && hierarchyTab.classList.contains('active')) {
            // 延迟刷新，确保数据已更新
            setTimeout(() => {
                this.buildMemberTree();
            }, 200);
        }
    }

    // ==================== 分成统计相关方法 ====================

    bindCommissionStatsEvents() {
        // 筛选按钮
        const filterCommissionBtn = document.getElementById('filterCommissionBtn');
        if (filterCommissionBtn) {
            filterCommissionBtn.addEventListener('click', () => {
                this.filterCommissionStats();
            });
        }

        // 重置筛选按钮
        const resetCommissionFilterBtn = document.getElementById('resetCommissionFilterBtn');
        if (resetCommissionFilterBtn) {
            resetCommissionFilterBtn.addEventListener('click', () => {
                this.resetCommissionFilter();
            });
        }

        // 积分类型筛选下拉框
        const pointsTypeFilter = document.getElementById('pointsTypeFilter');
        if (pointsTypeFilter) {
            pointsTypeFilter.addEventListener('change', () => {
                this.filterCommissionStats();
            });
        }

        // 分成统计Tab切换事件
        const commissionTabBtns = document.querySelectorAll('.commission-tab-btn');
        commissionTabBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const tabName = e.currentTarget.dataset.commissionTab;
                this.switchCommissionTab(tabName);
            });
        });

        // 分成记录分页控制将通过动态生成的按钮处理
    }

    switchCommissionTab(tabName) {
        // 移除所有tab按钮的active状态
        document.querySelectorAll('.commission-tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });

        // 隐藏所有tab内容
        document.querySelectorAll('.commission-tab-content').forEach(content => {
            content.classList.remove('active');
        });

        // 激活当前tab按钮
        const activeBtn = document.querySelector(`[data-commission-tab="${tabName}"]`);
        if (activeBtn) {
            activeBtn.classList.add('active');
        }

        // 显示对应的tab内容
        const activeContent = document.getElementById(`commission-${tabName}-tab`);
        if (activeContent) {
            activeContent.classList.add('active');
        }

        // 根据tab类型加载对应数据
        if (tabName === 'ranking') {
            this.loadCommissionRanking();
        } else if (tabName === 'details') {
            this.loadCommissionDetails();
        }
    }

    async loadCommissionStats() {
        try {
            this.showLoading(true);

            // 加载系统分成统计
            await this.loadSystemCommissionStats();

            // 加载分成排行榜
            await this.loadCommissionRanking();

            // 加载分成记录详情
            await this.loadCommissionDetails();

            // 默认显示第一个tab（分成记录详情）
            this.switchCommissionTab('details');

        } catch (error) {
            console.error('加载分成统计失败:', error);
            this.showToast('error', '加载分成统计失败');
        } finally {
            this.showLoading(false);
        }
    }

    async loadSystemCommissionStats() {
        try {
            const result = await window.electronAPI.commissionGetSystemStats(this.commissionFilters);
            if (result.success) {
                const stats = result.stats;

                // 更新系统统计显示
                document.getElementById('totalMemberCommission').textContent = stats.totalMemberCommission || 0;
                document.getElementById('totalSalesCommission').textContent = stats.totalSalesCommission || 0;
                document.getElementById('totalCommissionRecords').textContent = stats.totalRecords || 0;
            }
        } catch (error) {
            console.error('加载系统分成统计失败:', error);
        }
    }

    async loadCommissionRanking() {
        try {
            const rankingBody = document.getElementById('commissionRankingBody');
            if (!rankingBody) return;

            // 计算偏移量
            const offset = (this.rankingCurrentPage - 1) * this.rankingPageSize;

            // 获取排行榜数据
            const filters = {
                ...this.commissionFilters
            };

            // 获取排行榜数据和总数
            const [rankingResult, countResult] = await Promise.all([
                window.electronAPI.commissionGetMemberRanking(filters, this.rankingPageSize, offset),
                window.electronAPI.commissionGetMemberRankingCount(filters)
            ]);
            
            if (rankingResult.success) {
                // 更新总数
                this.rankingTotalCount = countResult.success ? (countResult.count || 0) : 0;
                
                if (rankingResult.ranking && rankingResult.ranking.length > 0) {
                    rankingBody.innerHTML = rankingResult.ranking.map((member, index) => {
                        const globalRank = offset + index + 1;
                        return `
                            <tr>
                                <td>${globalRank}</td>
                                <td>${member.member_name}</td>
                                <td><span class="commission-amount">${member.total_commission || 0}</span></td>
                                <td>${member.commission_count || 0}</td>
                                <td><span class="commission-amount">${member.avg_commission || 0}</span></td>
                                <td>
                                    <button class="btn btn-sm btn-primary" onclick="memberManagement.viewMemberCommissionDetails('${member.member_id}')">
                                        查看详情
                                    </button>
                                </td>
                            </tr>
                        `;
                    }).join('');
                } else {
                    const pointsTypeText = filters.pointsType === 'member' ? '会员积分' : 
                                          filters.pointsType === 'sales' ? '销售积分' : '';
                    rankingBody.innerHTML = `
                        <tr>
                            <td colspan="6" style="text-align: center; padding: 20px; color: #666;">
                                暂无${pointsTypeText}分成排行榜数据
                            </td>
                        </tr>
                    `;
                }
                
                // 更新分页
                this.updateRankingPagination();
            }
        } catch (error) {
            console.error('加载分成排行榜失败:', error);
            const rankingBody = document.getElementById('commissionRankingBody');
            if (rankingBody) {
                rankingBody.innerHTML = `
                    <tr>
                        <td colspan="6" style="text-align: center; padding: 20px; color: #dc3545;">
                            加载排行榜数据失败
                        </td>
                    </tr>
                `;
            }
        }
    }

    async loadCommissionDetails() {
        try {
            const offset = (this.commissionCurrentPage - 1) * this.commissionPageSize;

            // 获取筛选条件
            const filters = {
                ...this.commissionFilters
            };

            // 获取分成记录详情
            const [recordsResult, countResult] = await Promise.all([
                window.electronAPI.commissionGetAllRecords(
                    filters,
                    this.commissionPageSize,
                    offset
                ),
                window.electronAPI.commissionGetAllRecordsCount(filters)
            ]);

            if (recordsResult.success && countResult.success) {
                this.commissionTotalCount = countResult.count || 0;
                this.renderCommissionDetails(recordsResult.records || []);
                this.updateCommissionPagination();
            }
        } catch (error) {
            console.error('加载分成记录详情失败:', error);
        }
    }

    renderCommissionDetails(records) {
        const tbody = document.getElementById('commissionDetailsBody');
        if (!tbody) return;

        if (records.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="9" style="text-align: center; padding: 20px; color: #666;">
                        暂无分成记录
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = records.map(record => `
            <tr>
                <td>${this.formatDateTime(record.created_at)}</td>
                <td>${record.source_member_name}</td>
                <td>${record.beneficiary_member_name}</td>
                <td><span class="level-badge level-${record.level}">${record.level}</span></td>
                <td><span class="points-type-badge ${record.points_type}">${record.points_type === 'member' ? '会员积分' : '销售积分'}</span></td>
                <td>${record.base_amount}</td>
                <td>${record.commission_rate.toFixed(1)}%</td>
                <td><span class="commission-amount">${record.commission_amount}</span></td>
                <td><span class="trigger-type-badge ${record.trigger_type}">${record.trigger_type === 'recharge' ? '充值' : '手动调整'}</span></td>
            </tr>
        `).join('');
    }

    updateCommissionPagination() {
        const totalPages = Math.ceil(this.commissionTotalCount / this.commissionPageSize);
        const currentPage = this.commissionCurrentPage;

        // 更新分页信息
        document.getElementById('commissionTotalCount').textContent = this.commissionTotalCount;
        document.getElementById('commissionPageSize').textContent = this.commissionPageSize;

        const paginationContainer = document.getElementById('commissionPagination');
        if (!paginationContainer) return;

        if (totalPages <= 1) {
            paginationContainer.innerHTML = '';
            return;
        }

        let paginationHTML = '';

        // 首页按钮
        if (currentPage > 1) {
            paginationHTML += `<button onclick="memberManagement.goToCommissionPage(1)">首页</button>`;
        }

        // 上一页按钮
        if (currentPage > 1) {
            paginationHTML += `<button onclick="memberManagement.goToCommissionPage(${currentPage - 1})">上一页</button>`;
        }

        // 页码按钮逻辑
        const maxVisiblePages = 5;
        let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
        let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

        // 调整起始页
        if (endPage - startPage + 1 < maxVisiblePages) {
            startPage = Math.max(1, endPage - maxVisiblePages + 1);
        }

        // 第一页
        if (startPage > 1) {
            paginationHTML += `<button onclick="memberManagement.goToCommissionPage(1)">1</button>`;
            if (startPage > 2) {
                paginationHTML += `<span>...</span>`;
            }
        }

        // 中间页码
        for (let i = startPage; i <= endPage; i++) {
            const activeClass = i === currentPage ? 'active' : '';
            paginationHTML += `<button class="${activeClass}" onclick="memberManagement.goToCommissionPage(${i})">${i}</button>`;
        }

        // 最后一页
        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                paginationHTML += `<span>...</span>`;
            }
            paginationHTML += `<button onclick="memberManagement.goToCommissionPage(${totalPages})">${totalPages}</button>`;
        }

        // 下一页按钮
        if (currentPage < totalPages) {
            paginationHTML += `<button onclick="memberManagement.goToCommissionPage(${currentPage + 1})">下一页</button>`;
        }

        // 末页按钮
        if (currentPage < totalPages) {
            paginationHTML += `<button onclick="memberManagement.goToCommissionPage(${totalPages})">末页</button>`;
        }

        paginationContainer.innerHTML = paginationHTML;
    }

    // 处理分成记录分页跳转
    goToCommissionPage(page) {
        this.commissionCurrentPage = page;
        this.loadCommissionDetails();
    }

    // 更新排行榜分页
    updateRankingPagination() {
        const totalPages = Math.ceil(this.rankingTotalCount / this.rankingPageSize);
        const currentPage = this.rankingCurrentPage;

        // 更新分页信息
        document.getElementById('rankingTotalCount').textContent = this.rankingTotalCount;
        document.getElementById('rankingPageSize').textContent = this.rankingPageSize;

        const paginationContainer = document.getElementById('rankingPagination');
        if (!paginationContainer) return;

        if (totalPages <= 1) {
            paginationContainer.innerHTML = '';
            return;
        }

        let paginationHTML = '';

        // 首页按钮
        if (currentPage > 1) {
            paginationHTML += `<button onclick="memberManagement.goToRankingPage(1)">首页</button>`;
        }

        // 上一页按钮
        if (currentPage > 1) {
            paginationHTML += `<button onclick="memberManagement.goToRankingPage(${currentPage - 1})">上一页</button>`;
        }

        // 页码按钮逻辑
        const maxVisiblePages = 5;
        let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
        let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

        // 调整起始页
        if (endPage - startPage + 1 < maxVisiblePages) {
            startPage = Math.max(1, endPage - maxVisiblePages + 1);
        }

        // 第一页
        if (startPage > 1) {
            paginationHTML += `<button onclick="memberManagement.goToRankingPage(1)">1</button>`;
            if (startPage > 2) {
                paginationHTML += `<span>...</span>`;
            }
        }

        // 中间页码
        for (let i = startPage; i <= endPage; i++) {
            const activeClass = i === currentPage ? 'active' : '';
            paginationHTML += `<button class="${activeClass}" onclick="memberManagement.goToRankingPage(${i})">${i}</button>`;
        }

        // 最后一页
        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                paginationHTML += `<span>...</span>`;
            }
            paginationHTML += `<button onclick="memberManagement.goToRankingPage(${totalPages})">${totalPages}</button>`;
        }

        // 下一页按钮
        if (currentPage < totalPages) {
            paginationHTML += `<button onclick="memberManagement.goToRankingPage(${currentPage + 1})">下一页</button>`;
        }

        // 末页按钮
        if (currentPage < totalPages) {
            paginationHTML += `<button onclick="memberManagement.goToRankingPage(${totalPages})">末页</button>`;
        }

        paginationContainer.innerHTML = paginationHTML;
    }

    // 处理排行榜分页跳转
    goToRankingPage(page) {
        this.rankingCurrentPage = page;
        this.loadCommissionRanking();
    }

    filterCommissionStats() {
        const startDate = document.getElementById('commissionStartDate').value;
        const endDate = document.getElementById('commissionEndDate').value;
        const pointsType = document.getElementById('pointsTypeFilter').value;

        this.commissionFilters = {};
        if (startDate) this.commissionFilters.startDate = startDate;
        if (endDate) this.commissionFilters.endDate = endDate;
        if (pointsType) this.commissionFilters.pointsType = pointsType;

        this.commissionCurrentPage = 1;
        this.rankingCurrentPage = 1;
        this.loadCommissionStats();
    }

    resetCommissionFilter() {
        document.getElementById('commissionStartDate').value = '';
        document.getElementById('commissionEndDate').value = '';
        document.getElementById('pointsTypeFilter').value = '';

        this.commissionFilters = {};
        this.commissionCurrentPage = 1;
        this.rankingCurrentPage = 1;
        this.loadCommissionStats();
    }



    // 查看会员分成详情
    viewMemberCommissionDetails(memberId) {
        // 设置筛选条件为特定会员
        this.commissionFilters.memberId = memberId;
        this.commissionCurrentPage = 1;
        
        // 重新加载分成记录详情
        this.loadCommissionDetails();
        
        // 滚动到分成记录详情区域
        const detailsSection = document.querySelector('.commission-details');
        if (detailsSection) {
            detailsSection.scrollIntoView({ behavior: 'smooth' });
        }
        
        this.showToast('success', '已筛选该会员的分成记录');
    }

    // ==================== 层级关系搜索功能 ====================

    // 在层级关系中搜索会员
    searchInHierarchy(searchTerm) {
        if (!searchTerm) {
            this.clearHierarchySearch();
            return;
        }

        // 获取所有会员数据
        const membersData = this.originalAllMembers.length > 0 ? this.originalAllMembers : this.allMembers;

        // 搜索匹配的会员（按姓名或ID）
        const matchedMembers = membersData.filter(member => {
            const nameMatch = member.name.toLowerCase().includes(searchTerm.toLowerCase());
            const idMatch = member.id.toString().includes(searchTerm);
            return nameMatch || idMatch;
        });

        if (matchedMembers.length === 0) {
            this.showToast('info', '未找到匹配的会员');
            return;
        }

        // 如果找到多个匹配，使用第一个
        const targetMember = matchedMembers[0];

        // 展开到目标会员并高亮显示
        this.expandToMemberAndHighlight(targetMember);

        // 显示搜索结果提示
        if (matchedMembers.length > 1) {
            this.showToast('info', `找到 ${matchedMembers.length} 个匹配会员，已定位到：${targetMember.name}`);
        } else {
            this.showToast('success', `已定位到会员：${targetMember.name}`);
        }
    }

    // 展开到指定会员并高亮显示
    expandToMemberAndHighlight(targetMember) {
        // 首先收起所有节点
        this.collapseAllTreeNodes();

        // 获取目标会员的完整路径（从根到目标会员）
        const pathToMember = this.getPathToMember(targetMember.id);

        // 找到根用户（路径中的第一个用户）
        const rootMemberId = pathToMember[0];

        // 展开整个根用户分支（包括所有子用户）
        this.expandEntireBranch(rootMemberId);

        // 高亮显示目标会员
        setTimeout(() => {
            this.highlightMember(targetMember.id);
        }, 300); // 延迟确保展开动画完成
    }

    // 获取到指定会员的完整路径
    getPathToMember(memberId) {
        const membersData = this.originalAllMembers.length > 0 ? this.originalAllMembers : this.allMembers;
        const path = [];
        let currentMember = membersData.find(m => m.id === memberId);

        // 从目标会员向上追溯到根会员
        while (currentMember) {
            path.unshift(currentMember.id);
            if (currentMember.parent_id) {
                currentMember = membersData.find(m => m.id === currentMember.parent_id);
            } else {
                break;
            }
        }

        return path;
    }

    // 展开路径上的所有节点
    expandPathNodes(path) {
        path.forEach(memberId => {
            const memberNode = document.querySelector(`[data-member-id="${memberId}"]`);
            if (memberNode) {
                const toggleBtn = memberNode.querySelector('.tree-toggle');
                const childrenContainer = memberNode.querySelector('.tree-children');

                if (toggleBtn && childrenContainer) {
                    // 展开节点
                    toggleBtn.classList.remove('collapsed');
                    toggleBtn.classList.add('expanded');
                    childrenContainer.style.display = 'block';

                    // 更新按钮文本
                    toggleBtn.textContent = '−';
                }
            }
        });
    }

    // 展开整个分支（根用户及其所有子用户）
    expandEntireBranch(rootMemberId) {
        const rootNode = document.querySelector(`[data-member-id="${rootMemberId}"]`);
        if (!rootNode) return;

        // 递归展开所有子节点
        this.expandNodeAndChildren(rootNode);
    }

    // 递归展开节点及其所有子节点
    expandNodeAndChildren(node) {
        const toggleBtn = node.querySelector('.tree-toggle');
        const childrenContainer = node.querySelector('.tree-children');

        if (toggleBtn && childrenContainer) {
            // 展开当前节点
            toggleBtn.classList.remove('collapsed');
            toggleBtn.classList.add('expanded');
            childrenContainer.style.display = 'block';
            toggleBtn.textContent = '−';

            // 递归展开所有子节点
            const childNodes = childrenContainer.querySelectorAll(':scope > .tree-node');
            childNodes.forEach(childNode => {
                this.expandNodeAndChildren(childNode);
            });
        }
    }

    // 高亮显示指定会员
    highlightMember(memberId) {
        // 清除之前的高亮
        const previousHighlight = document.querySelector('.member-node.search-highlight');
        if (previousHighlight) {
            previousHighlight.classList.remove('search-highlight');
        }

        // 高亮目标会员
        const targetNode = document.querySelector(`[data-member-id="${memberId}"] .member-node`);
        if (targetNode) {
            targetNode.classList.add('search-highlight');

            // 滚动到目标位置
            targetNode.scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });

            // 3秒后移除高亮
            setTimeout(() => {
                targetNode.classList.remove('search-highlight');
            }, 3000);
        }
    }

    // 清除搜索状态
    clearHierarchySearch() {
        // 清除高亮
        const highlighted = document.querySelector('.tree-node-content.search-highlight');
        if (highlighted) {
            highlighted.classList.remove('search-highlight');
        }

        // 收起所有节点，恢复到默认的收起状态
        this.collapseAllTreeNodes();
    }

    // ==================== Excel导出功能 ====================

    // 导出会员数据到Excel
    async exportMembersToExcel() {
        try {
            // 检查是否有数据
            if (!this.members || this.members.length === 0) {
                this.showToast('warning', '暂无数据可导出');
                return;
            }

            // 显示加载提示
            this.showToast('info', '正在生成Excel文件，请稍候...');

            // 准备导出数据
            const exportData = this.prepareExportData();

            // 创建工作簿
            const workbook = XLSX.utils.book_new();

            // 创建工作表
            const worksheet = XLSX.utils.json_to_sheet(exportData);

            // 设置列宽
            const columnWidths = [
                { wch: 8 },   // 会员ID
                { wch: 15 },  // 会员姓名
                { wch: 15 },  // 上级会员
                { wch: 8 },   // 层级
                { wch: 12 },  // 会员积分
                { wch: 12 },  // 销售积分
                { wch: 12 },  // 生效日期
                { wch: 15 },  // 会员状态
                { wch: 20 }   // 创建时间
            ];
            worksheet['!cols'] = columnWidths;

            // 添加工作表到工作簿
            XLSX.utils.book_append_sheet(workbook, worksheet, '会员数据');

            // 生成文件名
            const fileName = this.generateExportFileName();

            // 导出文件
            XLSX.writeFile(workbook, fileName);

            // 显示成功提示
            this.showToast('success', `Excel文件已导出：${fileName}`);

        } catch (error) {
            console.error('导出Excel失败:', error);
            this.showToast('error', '导出Excel失败，请重试');
        }
    }

    // 准备导出数据
    prepareExportData() {
        return this.members.map(member => {
            const statusInfo = this.getMemberStatusInfo(member);

            return {
                '会员ID': member.id,
                '会员姓名': member.name,
                '上级会员': member.parent_name || '无',
                '层级': this.getMemberLevel(member.id),
                '会员积分': this.formatPoints(member.member_points),
                '销售积分': this.formatPoints(member.sales_points),
                '生效日期': member.effective_date ? this.formatDateWithTimezone(member.effective_date) : '未设置',
                '会员状态': this.getStatusText(statusInfo, member),
                '创建时间': this.formatDateTimeWithTimezone(member.created_at)
            };
        });
    }

    // 获取状态文本（纯文本，不包含HTML）
    getStatusText(statusInfo, member) {
        switch (statusInfo.status) {
            case 'active':
                return `有效期至 ${this.formatDateWithTimezone(member.expire_date)}`;
            case 'expired':
                return '已过期';
            case 'not_effective':
                return '未生效';
            case 'not_purchased':
                return '普通会员';
            case 'invalid_date':
                return '数据异常';
            default:
                return '未知状态';
        }
    }

    // 获取会员层级
    getMemberLevel(memberId) {
        // 计算会员在层级关系中的层级
        let level = 1;
        let currentMember = this.allMembers.find(m => m.id === memberId);

        while (currentMember && currentMember.parent_id) {
            level++;
            currentMember = this.allMembers.find(m => m.id === currentMember.parent_id);

            // 防止无限循环
            if (level > 20) break;
        }

        return level;
    }

    // 生成导出文件名
    generateExportFileName() {
        const now = new Date();
        const timestamp = now.toLocaleString('zh-CN', {
            timeZone: 'Asia/Shanghai',
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        }).replace(/[\/\s:]/g, match => {
            if (match === '/') return '-';
            if (match === ' ') return '_';
            if (match === ':') return '-';
            return match;
        });

        return `会员数据_${timestamp}.xlsx`;
    }

    // ==================== 管理员管理相关方法 ====================

    // 绑定管理员管理事件
    bindAdminManageEvents() {
        // 新增管理员按钮
        const addAdminBtn = document.getElementById('addAdminBtn');
        if (addAdminBtn) {
            addAdminBtn.addEventListener('click', () => {
                this.showAddAdminModal();
            });
        }

        // 新增管理员弹窗相关事件
        const addAdminModal = document.getElementById('addAdminModal');
        const closeAddAdminModal = document.getElementById('closeAddAdminModal');
        const cancelAddAdminBtn = document.getElementById('cancelAddAdminBtn');
        const addAdminForm = document.getElementById('addAdminForm');

        if (closeAddAdminModal) {
            closeAddAdminModal.addEventListener('click', () => {
                this.hideAddAdminModal();
            });
        }

        if (cancelAddAdminBtn) {
            cancelAddAdminBtn.addEventListener('click', () => {
                this.hideAddAdminModal();
            });
        }

        if (addAdminModal) {
            addAdminModal.addEventListener('click', (e) => {
                if (e.target === addAdminModal) {
                    this.hideAddAdminModal();
                }
            });
        }

        if (addAdminForm) {
            addAdminForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleAddAdmin();
            });
        }

        // 重置密码弹窗相关事件
        const resetPasswordModal = document.getElementById('resetPasswordModal');
        const closeResetPasswordModal = document.getElementById('closeResetPasswordModal');
        const cancelResetPasswordBtn = document.getElementById('cancelResetPasswordBtn');
        const resetPasswordForm = document.getElementById('resetPasswordForm');

        if (closeResetPasswordModal) {
            closeResetPasswordModal.addEventListener('click', () => {
                this.hideResetPasswordModal();
            });
        }

        if (cancelResetPasswordBtn) {
            cancelResetPasswordBtn.addEventListener('click', () => {
                this.hideResetPasswordModal();
            });
        }

        if (resetPasswordModal) {
            resetPasswordModal.addEventListener('click', (e) => {
                if (e.target === resetPasswordModal) {
                    this.hideResetPasswordModal();
                }
            });
        }

        if (resetPasswordForm) {
            resetPasswordForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleResetPassword();
            });
        }

        // 重置密码弹窗中的密码显示/隐藏切换
        const toggleNewAdminPassword = document.getElementById('toggleNewAdminPassword');
        const toggleConfirmAdminPassword = document.getElementById('toggleConfirmAdminPassword');

        if (toggleNewAdminPassword) {
            toggleNewAdminPassword.addEventListener('click', () => {
                this.togglePasswordVisibility('newAdminPassword', 'toggleNewAdminPassword');
            });
        }

        if (toggleConfirmAdminPassword) {
            toggleConfirmAdminPassword.addEventListener('click', () => {
                this.togglePasswordVisibility('confirmAdminPassword', 'toggleConfirmAdminPassword');
            });
        }
    }

    // 加载管理员列表
    async loadAdmins() {
        try {
            // 检查权限
            if (!this.currentAdmin) {
                this.showToast('error', '管理员信息不存在');
                return;
            }

            if (!this.currentAdmin.permissions) {
                this.showToast('error', '权限信息不存在');
                return;
            }

            if (!this.currentAdmin.permissions.canManageAdmins) {
                this.showToast('error', '没有查看管理员列表的权限');
                return;
            }

            this.showLoading(true);

            const result = await window.electronAPI.adminGetAll(this.currentAdmin.id);

            if (result.success) {
                this.renderAdminTable(result.admins || []);
                const totalElement = document.getElementById('totalAdmins');
                if (totalElement) {
                    totalElement.textContent = result.admins.length;
                }
            } else {
                this.showToast('error', result.message || '加载管理员列表失败');
            }
        } catch (error) {
            console.error('加载管理员列表失败:', error);
            this.showToast('error', '加载管理员列表失败');
        } finally {
            this.showLoading(false);
        }
    }

    // 渲染管理员表格
    renderAdminTable(admins) {
        const tbody = document.getElementById('adminTableBody');

        if (!tbody) {
            console.error('未找到 adminTableBody 元素');
            return;
        }

        tbody.innerHTML = '';

        if (admins.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="9" class="no-data">暂无管理员数据</td>
                </tr>
            `;
            return;
        }

        admins.forEach((admin) => {
            const row = document.createElement('tr');
            const canManage = this.canManageAdmin(admin);

            row.innerHTML = `
                <td>${admin.id}</td>
                <td>${admin.username}</td>
                <td>${admin.name}</td>
                <td>
                    <span class="role-badge role-${admin.role_level}">
                        ${admin.roleName || this.getRoleName(admin.role_level)}
                    </span>
                </td>
                <td>${admin.creator_name || '-'}</td>
                <td>${this.formatDateWithTimezone(admin.created_at)}</td>
                <td>${admin.last_login_at ? this.formatDateWithTimezone(admin.last_login_at) : '从未登录'}</td>
                <td>
                    <span class="status-badge ${admin.is_active ? 'status-active' : 'status-inactive'}">
                        ${admin.is_active ? '启用' : '禁用'}
                    </span>
                </td>
                <td>
                    <div class="action-buttons">
                        ${canManage ? `
                            ${this.currentAdmin.roleLevel === 0 && admin.id !== this.currentAdmin.id ? `
                                <button class="btn btn-sm btn-info"
                                        onclick="memberManagement.showResetPasswordModal(${admin.id}, '${admin.name}')"
                                        title="重置密码">
                                    重置密码
                                </button>
                            ` : ''}
                            <button class="btn btn-sm ${admin.is_active ? 'btn-warning' : 'btn-success'}"
                                    onclick="memberManagement.toggleAdminStatus(${admin.id}, ${!admin.is_active})"
                                    title="${admin.is_active ? '禁用管理员' : '启用管理员'}">
                                ${admin.is_active ? '禁用' : '启用'}
                            </button>
                            ${admin.role_level === 2 ? `
                                <button class="btn btn-sm btn-danger"
                                        onclick="memberManagement.deleteAdmin(${admin.id}, '${admin.name}')"
                                        title="删除管理员">
                                    删除
                                </button>
                            ` : ''}
                        ` : ''}
                    </div>
                </td>
            `;

            tbody.appendChild(row);
        });
    }

    // 检查是否可以管理某个管理员
    canManageAdmin(targetAdmin) {
        if (!this.currentAdmin || !this.currentAdmin.permissions.canManageAdmins) {
            return false;
        }

        // 不能管理超超管理员
        if (targetAdmin.role_level === 0) {
            return false;
        }

        // 超超管理员可以管理所有人（除了自己）
        if (this.currentAdmin.roleLevel === 0) {
            if (this.currentAdmin.id === targetAdmin.id) {
                return false;
            }
            return true;
        }

        // 超级管理员可以管理普通管理员，但不能管理其他超级管理员
        if (this.currentAdmin.roleLevel === 1) {
            if (this.currentAdmin.id === targetAdmin.id) {
                return false;
            }
            if (targetAdmin.role_level > 1) {
                return true;
            } else {
                return false;
            }
        }

        // 普通管理员不能管理任何人
        return false;
    }

    // 显示新增管理员弹窗
    showAddAdminModal() {
        // 检查权限
        if (!this.currentAdmin || !this.currentAdmin.permissions.canCreateAdmin) {
            this.showToast('error', '没有创建管理员的权限');
            return;
        }

        // 清空表单
        document.getElementById('addAdminForm').reset();

        // 设置默认角色为普通管理员
        const roleInput = document.getElementById('adminRole');
        if (roleInput) {
            roleInput.value = '2';
        }

        // 显示弹窗
        document.getElementById('addAdminModal').style.display = 'flex';
    }

    // 隐藏新增管理员弹窗
    hideAddAdminModal() {
        document.getElementById('addAdminModal').style.display = 'none';
        document.getElementById('addAdminForm').reset();
    }

    // 处理新增管理员
    async handleAddAdmin() {
        try {
            const form = document.getElementById('addAdminForm');
            const formData = new FormData(form);

            const adminData = {
                username: formData.get('adminUsername').trim(),
                password: formData.get('adminPassword').trim(),
                name: formData.get('adminName').trim(),
                roleLevel: 2, // 固定为普通管理员
                creatorId: this.currentAdmin.id
            };

            // 基本验证
            if (!adminData.username || !adminData.password || !adminData.name) {
                this.showToast('error', '请填写所有必填字段');
                return;
            }

            if (adminData.username.length < 3) {
                this.showToast('error', '用户名至少需要3个字符');
                return;
            }

            if (adminData.password.length < 6) {
                this.showToast('error', '密码至少需要6个字符');
                return;
            }

            // 用户名格式验证
            if (!/^[a-zA-Z0-9_]+$/.test(adminData.username)) {
                this.showToast('error', '用户名只能包含字母、数字和下划线');
                return;
            }

            this.showLoading(true);

            const result = await window.electronAPI.adminCreate(adminData);
            if (result.success) {
                this.showToast('success', '普通管理员创建成功');
                this.hideAddAdminModal();
                this.loadAdmins(); // 重新加载管理员列表
            } else {
                this.showToast('error', result.message || '创建管理员失败');
            }
        } catch (error) {
            console.error('创建管理员失败:', error);
            this.showToast('error', '创建管理员失败');
        } finally {
            this.showLoading(false);
        }
    }

    // 切换管理员状态
    async toggleAdminStatus(adminId, isActive) {
        try {
            const action = isActive ? '启用' : '禁用';
            
            // 使用自定义确认框
            const confirmed = await this.showAdminActionConfirm(
                `${action}管理员`,
                `确定要${action}该管理员吗？`,
                false
            );
            
            if (!confirmed) {
                return;
            }

            this.showLoading(true);

            const result = await window.electronAPI.adminToggleStatus({
                adminId,
                isActive,
                operatorId: this.currentAdmin.id
            });

            if (result.success) {
                this.showToast('success', `管理员${action}成功`);
                this.loadAdmins(); // 重新加载管理员列表
            } else {
                this.showToast('error', result.message || `${action}管理员失败`);
            }
        } catch (error) {
            console.error('切换管理员状态失败:', error);
            this.showToast('error', '操作失败');
        } finally {
            this.showLoading(false);
        }
    }

    // 删除管理员
    async deleteAdmin(adminId, adminName) {
        try {
            if (!this.currentAdmin || !this.currentAdmin.permissions.canManageAdmins) {
                this.showToast('error', '没有管理管理员的权限');
                return;
            }

            // 使用自定义确认框进行二次确认
            const firstConfirmed = await this.showAdminActionConfirm(
                '删除管理员',
                `确定要删除管理员"${adminName}"吗？`,
                true
            );
            
            if (!firstConfirmed) return;

            // 再次确认
            const doubleConfirmed = await this.showAdminActionConfirm(
                '再次确认删除',
                `请再次确认：真的要删除管理员"${adminName}"吗？\n\n删除后该管理员将无法登录系统！`,
                true
            );
            
            if (!doubleConfirmed) return;

            this.showLoading(true);

            const result = await window.electronAPI.adminDelete({
                adminId,
                operatorId: this.currentAdmin.id
            });

            if (result.success) {
                this.showToast('success', '管理员删除成功');
                this.loadAdmins(); // 重新加载管理员列表
            } else {
                this.showToast('error', result.message || '删除管理员失败');
            }
        } catch (error) {
            console.error('删除管理员失败:', error);
            this.showToast('error', '删除失败');
        } finally {
            this.showLoading(false);
        }
    }

    // 显示管理员操作确认框
    showAdminActionConfirm(title, message, showWarning = false) {
        return new Promise((resolve) => {
            const modal = document.getElementById('adminActionConfirmModal');
            const titleElement = document.getElementById('adminActionTitle');
            const messageElement = document.getElementById('adminActionMessage');
            const warningElement = document.getElementById('adminActionWarning');
            const confirmBtn = document.getElementById('confirmAdminActionBtn');
            const cancelBtn = document.getElementById('cancelAdminActionBtn');
            const closeBtn = document.getElementById('closeAdminActionModal');

            // 设置内容
            titleElement.textContent = title;
            messageElement.textContent = message;
            
            if (showWarning) {
                warningElement.style.display = 'block';
                warningElement.textContent = '此操作不可撤销，请谨慎操作。';
            } else {
                warningElement.style.display = 'none';
            }

            // 清除之前的事件监听器
            const newConfirmBtn = confirmBtn.cloneNode(true);
            const newCancelBtn = cancelBtn.cloneNode(true);
            const newCloseBtn = closeBtn.cloneNode(true);
            
            confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);
            cancelBtn.parentNode.replaceChild(newCancelBtn, cancelBtn);
            closeBtn.parentNode.replaceChild(newCloseBtn, closeBtn);

            // 添加事件监听器
            newConfirmBtn.addEventListener('click', () => {
                modal.style.display = 'none';
                resolve(true);
            });

            newCancelBtn.addEventListener('click', () => {
                modal.style.display = 'none';
                resolve(false);
            });

            newCloseBtn.addEventListener('click', () => {
                modal.style.display = 'none';
                resolve(false);
            });

            // 点击背景关闭
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.style.display = 'none';
                    resolve(false);
                }
            });

            // 显示弹窗
            modal.style.display = 'flex';
        });
    }

    // 显示重置密码弹窗
    showResetPasswordModal(adminId, adminName) {
        // 检查权限 - 只有超超管理员可以重置其他管理员密码
        if (!this.currentAdmin || this.currentAdmin.roleLevel !== 0) {
            this.showToast('error', '没有重置密码的权限');
            return;
        }

        // 不能重置自己的密码
        if (this.currentAdmin.id === adminId) {
            this.showToast('error', '不能重置自己的密码');
            return;
        }

        // 保存要重置密码的管理员信息
        this.resetPasswordAdminId = adminId;
        this.resetPasswordAdminName = adminName;

        // 清空表单
        const form = document.getElementById('resetPasswordForm');
        if (form) {
            form.reset();
        }

        // 设置管理员信息
        const adminNameElement = document.getElementById('resetPasswordAdminName');
        if (adminNameElement) {
            adminNameElement.textContent = adminName;
        }

        // 显示弹窗
        const modal = document.getElementById('resetPasswordModal');
        if (modal) {
            modal.style.display = 'flex';
        }
    }

    // 隐藏重置密码弹窗
    hideResetPasswordModal() {
        const modal = document.getElementById('resetPasswordModal');
        if (modal) {
            modal.style.display = 'none';
        }

        // 清理数据
        this.resetPasswordAdminId = null;
        this.resetPasswordAdminName = null;

        // 清空表单
        const form = document.getElementById('resetPasswordForm');
        if (form) {
            form.reset();
        }
    }

    // 处理重置密码
    async handleResetPassword() {
        if (!this.resetPasswordAdminId) {
            this.showToast('error', '重置密码参数错误');
            return;
        }

        const newPassword = document.getElementById('newAdminPassword').value.trim();
        const confirmPassword = document.getElementById('confirmAdminPassword').value.trim();

        // 表单验证
        if (!newPassword) {
            this.showToast('error', '请输入新密码');
            return;
        }

        if (newPassword.length < 6) {
            this.showToast('error', '新密码长度至少6位');
            return;
        }

        if (newPassword !== confirmPassword) {
            this.showToast('error', '两次输入的密码不一致');
            return;
        }

        try {
            this.showLoading(true);

            const result = await window.electronAPI.adminResetPassword({
                targetAdminId: this.resetPasswordAdminId,
                newPassword: newPassword,
                operatorId: this.currentAdmin.id
            });

            if (result.success) {
                this.showToast('success', '管理员密码重置成功');
                this.hideResetPasswordModal();
            } else {
                this.showToast('error', result.message || '重置密码失败');
            }
        } catch (error) {
            console.error('重置密码失败:', error);
            this.showToast('error', '重置密码失败');
        } finally {
            this.showLoading(false);
        }
    }

    // 密码显示/隐藏切换功能
    togglePasswordVisibility(inputId, toggleButtonId) {
        const passwordInput = document.getElementById(inputId);
        const toggleButton = document.getElementById(toggleButtonId);
        
        if (!passwordInput || !toggleButton) {
            return;
        }
        
        const isPassword = passwordInput.type === 'password';
        
        // 切换输入框类型
        passwordInput.type = isPassword ? 'text' : 'password';
        
        // 切换按钮图标
        toggleButton.textContent = isPassword ? '🙈' : '👁️';
    }
}

// 全局实例
let memberManagement;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    memberManagement = new MemberManagement();
});
