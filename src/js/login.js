// 登录页面逻辑
class LoginManager {
    constructor() {
        this.loginForm = document.getElementById('loginForm');
        this.usernameInput = document.getElementById('username');
        this.passwordInput = document.getElementById('password');
        this.loginBtn = document.getElementById('loginBtn');
        this.errorMessage = document.getElementById('errorMessage');
        this.togglePasswordBtn = document.getElementById('togglePassword');
        
        this.init();
    }

    init() {
        // 绑定事件
        this.bindEvents();
        
        // 设置默认焦点
        this.usernameInput.focus();
    }

    bindEvents() {
        // 表单提交事件
        this.loginForm.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleLogin();
        });

        // 密码显示/隐藏切换
        this.togglePasswordBtn.addEventListener('click', () => {
            this.togglePasswordVisibility();
        });

        // 输入框回车事件
        this.usernameInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.passwordInput.focus();
            }
        });

        this.passwordInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.handleLogin();
            }
        });

        // 输入时清除错误信息
        this.usernameInput.addEventListener('input', () => {
            this.hideError();
        });

        this.passwordInput.addEventListener('input', () => {
            this.hideError();
        });
    }

    async handleLogin() {
        const username = this.usernameInput.value.trim();
        const password = this.passwordInput.value;

        // 基本验证
        if (!username) {
            this.showError('请输入管理员账号');
            this.usernameInput.focus();
            return;
        }

        if (!password) {
            this.showError('请输入密码');
            this.passwordInput.focus();
            return;
        }

        // 显示加载状态
        this.setLoading(true);

        try {
            // 调用登录API
            const result = await window.electronAPI.adminLogin(username, password);

            if (result.success) {
                // 登录成功
                this.handleLoginSuccess(result.admin);
            } else {
                // 登录失败
                this.showError(result.message || '登录失败，请检查账号和密码');
                this.setLoading(false);
            }
        } catch (error) {
            console.error('登录错误:', error);
            this.showError('登录过程中发生错误，请重试');
            this.setLoading(false);
        }
    }

    handleLoginSuccess(admin) {
        // 保存当前登录的管理员信息到本地存储
        localStorage.setItem('currentAdmin', JSON.stringify(admin));
        localStorage.setItem('isLoggedIn', 'true');

        // 显示成功消息
        this.showSuccess('登录成功，正在跳转...');

        // 延迟跳转到主界面
        setTimeout(() => {
            window.location.href = 'member-management.html';
        }, 1000);
    }

    togglePasswordVisibility() {
        const isPassword = this.passwordInput.type === 'password';
        this.passwordInput.type = isPassword ? 'text' : 'password';
        
        const eyeIcon = this.togglePasswordBtn.querySelector('.eye-icon');
        eyeIcon.textContent = isPassword ? '🙈' : '👁️';
    }

    setLoading(loading) {
        const btnText = this.loginBtn.querySelector('.btn-text');
        const btnLoading = this.loginBtn.querySelector('.btn-loading');

        if (loading) {
            btnText.style.display = 'none';
            btnLoading.style.display = 'flex';
            this.loginBtn.disabled = true;
        } else {
            btnText.style.display = 'block';
            btnLoading.style.display = 'none';
            this.loginBtn.disabled = false;
        }
    }

    showError(message) {
        const errorText = this.errorMessage.querySelector('.error-text');
        errorText.textContent = message;
        this.errorMessage.style.display = 'flex';
        
        // 添加震动效果
        this.errorMessage.style.animation = 'shake 0.5s ease-in-out';
        setTimeout(() => {
            this.errorMessage.style.animation = '';
        }, 500);
    }

    showSuccess(message) {
        // 创建成功消息元素
        const successMessage = document.createElement('div');
        successMessage.className = 'success-message';
        successMessage.innerHTML = `
            <span class="success-icon">✅</span>
            <span class="success-text">${message}</span>
        `;
        
        // 添加成功消息样式
        successMessage.style.cssText = `
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 12px;
            margin-top: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
            color: #155724;
            font-size: 0.9em;
        `;

        // 隐藏错误消息，显示成功消息
        this.hideError();
        this.loginForm.appendChild(successMessage);
    }

    hideError() {
        this.errorMessage.style.display = 'none';
    }

}

// 添加震动动画样式
const style = document.createElement('style');
style.textContent = `
    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        25% { transform: translateX(-5px); }
        75% { transform: translateX(5px); }
    }
`;
document.head.appendChild(style);

// 页面加载完成后初始化登录管理器
document.addEventListener('DOMContentLoaded', () => {
    new LoginManager();
});

// 禁用自动登录功能 - 每次打开系统都需要重新登录
// 清除之前的登录状态
document.addEventListener('DOMContentLoaded', () => {
    // 清除登录状态，强制用户重新登录
    localStorage.removeItem('isLoggedIn');
    localStorage.removeItem('currentAdmin');
});
