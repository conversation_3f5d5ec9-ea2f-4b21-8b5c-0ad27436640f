<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Member管理系统 - 会员管理</title>
    <link rel="stylesheet" href="css/native-app.css">
    <link rel="stylesheet" href="css/management.css">
</head>
<body>
    <div class="app-container">
        <!-- 顶部导航栏 -->
        <header class="header">
            <div class="header-left">
                <h1 class="app-title">
                    Member管理系统
                </h1>
            </div>
            <div class="header-right">
                <div class="user-info">
                    <span class="welcome-text">欢迎，</span>
                    <span class="admin-name" id="adminName">管理员</span>
                    <button class="settings-btn" id="settingsBtn" title="系统设置">
                        <span>⚙️</span>
                    </button>
                    <button class="logout-btn" id="logoutBtn" title="退出登录">
                        <span>🚪</span>
                    </button>
                </div>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 功能导航 -->
            <nav class="nav-tabs">
                <button class="nav-tab active" data-tab="members">
                    <span class="tab-icon">👥</span>
                    会员管理
                </button>

                <button class="nav-tab" data-tab="hierarchy">
                    <span class="tab-icon">🌳</span>
                    层级关系
                </button>
                <button class="nav-tab" data-tab="points-config">
                    <span class="tab-icon">⚙️</span>
                    积分配置
                </button>
                <button class="nav-tab" data-tab="commission-stats">
                    <span class="tab-icon">💰</span>
                    分成统计
                </button>
                <button class="nav-tab" data-tab="records">
                    <span class="tab-icon">📊</span>
                    操作记录
                </button>
                <button class="nav-tab" data-tab="admin-manage" style="display: none;">
                    <span class="tab-icon">👤</span>
                    管理员管理
                </button>
            </nav>

            <!-- 会员管理面板 -->
            <div class="tab-content active" id="members-tab">
                <div class="panel-header">
                    <h2>会员管理</h2>
                    <div class="panel-actions">
                        <div class="search-box">
                            <input type="text" id="memberSearch" placeholder="搜索会员名称...">
                            <button class="search-btn" id="searchBtn">🔍</button>
                        </div>
                        <button class="btn btn-success" id="exportExcelBtn">
                            <span>📊</span>
                            导出Excel
                        </button>
                        <button class="btn btn-primary" id="addMemberBtn">
                            <span>➕</span>
                            新增会员
                        </button>
                    </div>
                </div>

                <div class="table-container">
                    <!-- 固定表头 -->
                    <div class="table-header-row">
                        <div class="header-cell">ID</div>
                        <div class="header-cell">会员名称</div>
                        <div class="header-cell">层级</div>
                        <div class="header-cell">会员积分</div>
                        <div class="header-cell">销售积分</div>
                        <div class="header-cell">生效日期</div>
                        <div class="header-cell">到期日期</div>
                        <div class="header-cell">创建时间</div>
                        <div class="header-cell">操作</div>
                    </div>

                    <!-- 可滚动表格内容 -->
                    <div class="table-wrapper">
                        <table class="members-table" id="membersTable">
                            <tbody id="membersTableBody">
                                <!-- 数据将通过JavaScript动态加载 -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="table-footer">
                    <div class="table-info">
                        共 <span id="totalMembers">0</span> 名会员
                    </div>
                    <div class="pagination" id="membersPagination">
                        <!-- 分页控件将通过JavaScript动态生成 -->
                    </div>
                </div>
            </div>



            <!-- 层级关系面板 -->
            <div class="tab-content" id="hierarchy-tab">
                <div class="panel-header">
                    <h2>会员层级关系图</h2>
                    <div class="panel-actions">
                        <div class="search-box">
                            <input type="text" id="hierarchySearchInput" placeholder="搜索会员姓名或ID...">
                            <button class="search-btn" id="searchHierarchyBtn">🔍</button>
                        </div>
                        <button class="btn btn-secondary" id="clearSearchBtn">清除</button>
                        <button class="btn btn-secondary" id="toggleCurrentBtn" disabled>展开当前</button>
                        <button class="btn btn-secondary" id="expandAllBtn">展开全部</button>
                        <button class="btn btn-secondary" id="collapseAllBtn">收起全部</button>
                        <button class="btn btn-primary" id="refreshTreeBtn">刷新</button>
                    </div>
                </div>
                <div class="panel-content">
                    <div class="tree-container">
                        <div id="memberTree" class="member-tree">
                            <!-- 树状图将通过JavaScript动态生成 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 积分配置面板 -->
            <div class="tab-content" id="points-config-tab">
                <div class="panel-header">
                    <h2>分成比例设置</h2>
                    <div class="panel-actions">
                        <button class="btn btn-secondary" id="resetBtn">重置为默认</button>
                        <button class="btn btn-primary" id="saveBtn">保存配置</button>
                    </div>
                </div>

                <!-- 配置表格 -->
                <div class="table-container">
                    <!-- 固定表头 -->
                    <div class="table-header-row">
                        <div class="header-cell">层级</div>
                        <div class="header-cell">层级说明</div>
                        <div class="header-cell">会员积分分成 (%)</div>
                        <div class="header-cell">销售积分分成 (%)</div>
                        <div class="header-cell">操作</div>
                    </div>

                    <!-- 可滚动表格内容 -->
                    <div class="table-wrapper">
                        <table class="config-table" id="configTable">
                            <tbody id="pointsConfigTableBody">
                                <!-- 动态生成表格内容 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 分成统计面板 -->
            <div class="tab-content" id="commission-stats-tab">
                <div class="panel-header">
                    <h2>分成统计</h2>
                    <div class="panel-actions">
                        <div class="date-range-filter">
                            <select id="pointsTypeFilter">
                                <option value="">全部积分类型</option>
                                <option value="member">会员积分分成</option>
                                <option value="sales">销售积分分成</option>
                            </select>
                            <input type="date" id="commissionStartDate" placeholder="开始日期">
                            <span>至</span>
                            <input type="date" id="commissionEndDate" placeholder="结束日期">
                            <button class="btn btn-primary" id="filterCommissionBtn">筛选</button>
                            <button class="btn btn-secondary" id="resetCommissionFilterBtn">重置</button>
                        </div>
                    </div>
                </div>

                <!-- 系统分成统计概览 -->
                <div class="stats-overview">
                    <div class="stats-card">
                        <h3>总分成统计</h3>
                        <div class="stats-grid" id="systemStatsGrid">
                            <div class="stat-item">
                                <label>会员积分分成总额：</label>
                                <span id="totalMemberCommission">0</span>
                            </div>
                            <div class="stat-item">
                                <label>销售积分分成总额：</label>
                                <span id="totalSalesCommission">0</span>
                            </div>
                            <div class="stat-item">
                                <label>分成记录总数：</label>
                                <span id="totalCommissionRecords">0</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 分成详情和排行榜Tab导航 -->
                <div class="commission-tabs">
                    <div class="commission-tab-nav">
                        <button class="commission-tab-btn active" data-commission-tab="details">分成记录详情</button>
                        <button class="commission-tab-btn" data-commission-tab="ranking">分成排行榜</button>
                    </div>

                    <!-- 分成记录详情Tab -->
                    <div class="commission-tab-content active" id="commission-details-tab">
                        <div class="commission-details">
                            <div class="table-container">
                                <table class="data-table" id="commissionDetailsTable">
                                    <thead>
                                        <tr>
                                            <th>时间</th>
                                            <th>来源会员</th>
                                            <th>受益会员</th>
                                            <th>层级</th>
                                            <th>积分类型</th>
                                            <th>基础金额</th>
                                            <th>分成比例</th>
                                            <th>分成金额</th>
                                            <th>触发类型</th>
                                        </tr>
                                    </thead>
                                    <tbody id="commissionDetailsBody">
                                        <!-- 动态生成内容 -->
                                    </tbody>
                                </table>
                            </div>

                            <!-- 分页控制 -->
                            <div class="pagination-container">
                                <div class="pagination-info">
                                    <span>共 <span id="commissionTotalCount">0</span> 条记录，每页显示 <span id="commissionPageSize">10</span> 条</span>
                                </div>
                                <div class="pagination" id="commissionPagination">
                                    <!-- 分页控件将通过JavaScript动态生成 -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 分成排行榜Tab -->
                    <div class="commission-tab-content" id="commission-ranking-tab">
                        <div class="commission-ranking">
                            <div class="ranking-content">
                                <div class="table-container">
                                    <table class="data-table" id="commissionRankingTable">
                                        <thead>
                                            <tr>
                                                <th>排名</th>
                                                <th>会员姓名</th>
                                                <th>分成总额</th>
                                                <th>分成次数</th>
                                                <th>平均分成</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="commissionRankingBody">
                                            <!-- 动态生成内容 -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            
                            <!-- 分页控制 -->
                            <div class="pagination-container">
                                <div class="pagination-info">
                                    <span>共 <span id="rankingTotalCount">0</span> 条记录，每页显示 <span id="rankingPageSize">10</span> 条</span>
                                </div>
                                <div class="pagination" id="rankingPagination">
                                    <!-- 分页控件将通过JavaScript动态生成 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 操作记录面板 -->
            <div class="tab-content" id="records-tab">
                <div class="panel-header">
                    <h2>操作记录</h2>
                    <div class="panel-actions">
                        <div class="search-box">
                            <input type="text" id="targetFilter" placeholder="搜索目标对象...">
                            <button class="search-btn" id="searchLogsBtn">🔍</button>
                        </div>
                        <div class="filter-controls">
                            <select id="operationTypeFilter">
                                <option value="">所有操作</option>
                                <option value="create">新增会员</option>
                                <option value="update">编辑会员</option>
                                <option value="delete">删除会员</option>
                                <option value="recharge">会员充值</option>
                                <option value="points_adjust">积分调整</option>
                            </select>
                            <input type="date" id="startDateFilter" placeholder="开始日期">
                            <input type="date" id="endDateFilter" placeholder="结束日期">
                            <button class="btn btn-secondary" id="clearFilterBtn">清除</button>
                        </div>
                    </div>
                </div>

                <div class="table-container">
                    <!-- 固定表头 -->
                    <div class="table-header-row">
                        <div class="header-cell">时间</div>
                        <div class="header-cell">操作员</div>
                        <div class="header-cell">操作类型</div>
                        <div class="header-cell">目标对象</div>
                        <div class="header-cell">操作详情</div>
                    </div>

                    <!-- 可滚动表格内容 -->
                    <div class="table-wrapper">
                        <table class="logs-table" id="operationLogsTable">
                            <tbody id="operationLogsTableBody">
                                <!-- 数据将通过JavaScript动态加载 -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="table-footer">
                    <div class="table-info">
                        共 <span id="totalLogs">0</span> 条记录
                    </div>
                    <div class="pagination" id="logsPagination">
                        <!-- 分页控件将通过JavaScript动态生成 -->
                    </div>
                </div>
            </div>

            <!-- 管理员管理面板 -->
            <div class="tab-content" id="admin-manage-tab" style="display: none;">
                <div class="panel-header">
                    <h2>管理员管理</h2>
                    <div class="panel-actions">
                        <button class="btn btn-primary" id="addAdminBtn">
                            <span class="btn-icon">➕</span>
                            新增管理员
                        </button>
                    </div>
                </div>

                <div class="table-container">
                    <table class="data-table" id="adminTable">
                        <thead>
                            <tr>
                                <th>编号</th>
                                <th>用户名</th>
                                <th>姓名</th>
                                <th>角色</th>
                                <th>创建者</th>
                                <th>创建时间</th>
                                <th>最后登录</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="adminTableBody">
                            <!-- 管理员数据将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>

                <div class="table-footer">
                    <div class="table-info">
                        共 <span id="totalAdmins">0</span> 个管理员
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 新增会员弹窗 -->
    <div class="modal" id="addMemberModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>新增会员</h3>
                <button class="modal-close" id="closeAddModal">&times;</button>
            </div>
            <div class="modal-body">
                <form id="addMemberForm">
                    <div class="form-group">
                        <label for="memberName">会员名称 *</label>
                        <input type="text" id="memberName" name="memberName" required placeholder="请输入会员名称">
                    </div>
                    <div class="form-group">
                        <label for="parentMember">上级会员</label>
                        <div class="select-with-search">
                            <input type="text" id="parentMemberSearch" placeholder="搜索上级会员..." class="search-input">
                            <select id="parentMember" name="parentMember" size="6">
                                <option value="">无上级（顶级会员）</option>
                                <!-- 选项将通过JavaScript动态加载 -->
                            </select>
                        </div>
                        <!-- 层级预览容器 -->
                        <div id="addMemberHierarchyPreview"></div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="cancelAddBtn">取消</button>
                <button type="submit" form="addMemberForm" class="btn btn-primary" id="confirmAddBtn">确认添加</button>
            </div>
        </div>
    </div>

    <!-- 编辑会员弹窗 -->
    <div class="modal" id="editMemberModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>编辑会员</h3>
                <button class="modal-close" id="closeEditModal">&times;</button>
            </div>
            <div class="modal-body">
                <form id="editMemberForm">
                    <input type="hidden" id="editMemberId" name="editMemberId">
                    <div class="form-group">
                        <label for="editMemberName">会员名称 *</label>
                        <input type="text" id="editMemberName" name="editMemberName" required placeholder="请输入会员名称">
                    </div>
                    <div class="form-group">
                        <label for="editParentMember">上级会员</label>
                        <div class="select-with-search">
                            <input type="text" id="editParentMemberSearch" placeholder="搜索上级会员..." class="search-input">
                            <select id="editParentMember" name="editParentMember" size="6">
                                <option value="">无上级（顶级会员）</option>
                                <!-- 选项将通过JavaScript动态加载 -->
                            </select>
                        </div>
                        <!-- 层级预览容器 -->
                        <div id="editMemberHierarchyPreview"></div>
                    </div>
                    <div class="form-row">
                        <div class="form-group form-group-half">
                            <label for="editEffectiveDate">生效日期</label>
                            <input type="date" id="editEffectiveDate" name="editEffectiveDate" placeholder="请选择生效日期">
                        </div>
                        <div class="form-group form-group-half">
                            <label for="editExpiryDate">到期日期</label>
                            <input type="date" id="editExpiryDate" name="editExpiryDate" placeholder="请选择到期日期">
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="cancelEditBtn">取消</button>
                <button type="submit" form="editMemberForm" class="btn btn-primary" id="confirmEditBtn">确认修改</button>
            </div>
        </div>
    </div>

    <!-- 加载提示 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>加载中...</p>
        </div>
    </div>

    <!-- 删除确认对话框 -->
    <div class="modal" id="deleteConfirmModal">
        <div class="modal-content" style="max-width: 400px;">
            <div class="modal-header">
                <h3>确认删除</h3>
                <button class="modal-close" id="closeDeleteModal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="confirm-content">
                    <div class="confirm-icon">⚠️</div>
                    <p class="confirm-message">
                        确定要删除会员 "<span id="deleteMemberName"></span>" 吗？
                    </p>
                    <p class="confirm-warning">
                        此操作不可撤销，请谨慎操作。
                    </p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="cancelDeleteBtn">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">确认删除</button>
            </div>
        </div>
    </div>

    <!-- 修改密码弹窗 -->
    <div class="modal" id="changePasswordModal">
        <div class="modal-content password-modal">
            <div class="modal-header">
                <h3>修改密码</h3>
                <button class="modal-close" id="closePasswordModal">&times;</button>
            </div>
            <div class="modal-body">
                <form id="changePasswordForm">
                    <div class="form-group">
                        <label for="currentPassword">当前密码 *</label>
                        <div class="input-wrapper">
                            <input type="password" id="currentPassword" name="currentPassword" required placeholder="请输入当前密码">
                            <button type="button" class="toggle-password" id="toggleCurrentPassword">
                                <span class="eye-icon">👁️</span>
                            </button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="newPassword">新密码 *</label>
                        <div class="input-wrapper">
                            <input type="password" id="newPassword" name="newPassword" required placeholder="请输入新密码（至少6位）" minlength="6">
                            <button type="button" class="toggle-password" id="toggleNewPassword">
                                <span class="eye-icon">👁️</span>
                            </button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="confirmPassword">确认新密码 *</label>
                        <div class="input-wrapper">
                            <input type="password" id="confirmPassword" name="confirmPassword" required placeholder="请再次输入新密码">
                            <button type="button" class="toggle-password" id="toggleConfirmPassword">
                                <span class="eye-icon">👁️</span>
                            </button>
                        </div>
                    </div>
                    <div class="password-tips">
                        <p class="tips-title">💡 密码要求</p>
                        <div class="tips-content">
                            <span class="tip-item">• 至少6位字符</span>
                            <span class="tip-item">• 建议字母+数字</span>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="cancelPasswordBtn">取消</button>
                <button type="submit" form="changePasswordForm" class="btn btn-primary" id="confirmPasswordBtn">确认修改</button>
            </div>
        </div>
    </div>

    <!-- 会员费用充值弹窗 -->
    <div class="modal" id="rechargeModal">
        <div class="modal-content recharge-modal">
            <div class="modal-header">
                <h3>会员费用充值</h3>
                <button class="modal-close" id="closeRechargeModal">&times;</button>
            </div>
            <div class="modal-body">
                <form id="rechargeForm">
                    <input type="hidden" id="rechargeTargetMemberId">
                    
                    <div class="form-group">
                        <label for="rechargeTargetMember">选择充值会员 *</label>
                        <div class="select-with-search">
                            <input type="text" id="rechargeTargetMemberSearch" placeholder="搜索会员..." class="search-input">
                            <select id="rechargeTargetMember" name="rechargeTargetMember" size="6" required>
                                <option value="">请选择要充值的会员</option>
                                <!-- 选项将通过JavaScript动态加载 -->
                            </select>
                        </div>
                    </div>

                    <div class="member-info-section" id="selectedMemberInfo" style="display: none;">
                        <h4>会员信息</h4>
                        <div class="member-info-card">
                            <div class="info-item">
                                <label>会员姓名：</label>
                                <span id="rechargeMemberName">-</span>
                            </div>
                            <div class="info-item">
                                <label>会员ID：</label>
                                <span id="rechargeMemberId">-</span>
                            </div>
                            <div class="info-item">
                                <label>当前会员积分：</label>
                                <span id="currentMemberPoints">0</span>
                            </div>
                            <div class="info-item">
                                <label>当前销售积分：</label>
                                <span id="currentSalesPoints">0</span>
                            </div>
                            <div class="info-item">
                                <label>到期日期：</label>
                                <span id="currentExpireDate">无</span>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="rechargePackage">会员套餐 *</label>
                        <select id="rechargePackage" name="rechargePackage" required autocomplete="off">
                            <option value="">请选择会员套餐</option>
                            <option value="half_month" data-amount="398" data-days="15">半月会员 - ¥398 (15天权益)</option>
                            <option value="one_month" data-amount="698" data-days="30">一月会员 - ¥698 (30天权益)</option>
                            <option value="two_months" data-amount="1298" data-days="60">两月会员 - ¥1298 (60天权益)</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="usePoints">积分抵扣 (10积分 = 1元)</label>
                        <div class="points-deduction">
                            <div class="points-input-group">
                                <input type="number" id="pointsToUse" name="pointsToUse" min="0" step="1" placeholder="输入要使用的积分数量">
                                <button type="button" class="btn btn-max" id="useMaxPointsBtn">最大</button>
                            </div>
                            <div class="points-info">
                                <span class="available-points">可用积分: <span id="availablePoints">0</span></span>
                                <span class="max-deduction">最大可抵扣: <span id="maxDeduction">¥0</span></span>
                            </div>
                        </div>
                    </div>

                    <div class="recharge-summary" id="rechargeSummary" style="display: none;">
                        <h4>购买详情</h4>
                        <div class="summary-item">
                            <label>套餐费用：</label>
                            <span id="summaryTotalAmount">¥0</span>
                        </div>
                        <div class="summary-item">
                            <label>积分抵扣：</label>
                            <span id="summaryPointsDeduction">¥0 (0积分)</span>
                        </div>
                        <div class="summary-item">
                            <label>实付金额：</label>
                            <span id="summaryCashAmount">¥0</span>
                        </div>
                        <div class="summary-item">
                            <label>权益天数：</label>
                            <span id="summaryDays">0天</span>
                        </div>
                        <div class="summary-item">
                            <label>生效日期：</label>
                            <span id="summaryStartDate">-</span>
                        </div>
                        <div class="summary-item">
                            <label>到期日期：</label>
                            <span id="summaryExpireDate">-</span>
                        </div>
                        <div class="summary-note">
                            <p>💡 管理员操作：确认会员已付费，权益将于T+1（明天）开始生效</p>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="cancelRechargeBtn">取消</button>
                <button type="submit" form="rechargeForm" class="btn btn-primary" id="confirmRechargeBtn">确认充值</button>
            </div>
        </div>
    </div>

    <!-- 手动调整积分弹窗 -->
    <div class="modal" id="adjustPointsModal">
        <div class="modal-content adjust-modal">
            <div class="modal-header">
                <h3>手动调整积分</h3>
                <button class="modal-close" id="closeAdjustModal">&times;</button>
            </div>
            <div class="modal-body">
                <form id="adjustPointsForm">
                    <div class="form-group">
                        <label for="selectedMemberInfo">调整会员</label>
                        <div class="selected-member-info">
                            <div class="member-info-display" id="selectedMemberInfo">
                                <span class="member-id">ID: <span id="displayMemberId">-</span></span>
                                <span class="member-name">姓名: <span id="displayMemberName">-</span></span>
                            </div>
                            <input type="hidden" id="adjustMember" name="adjustMember" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="pointsType">积分类型 *</label>
                        <select id="pointsType" name="pointsType" required>
                            <option value="">请选择积分类型</option>
                            <option value="member">会员积分</option>
                            <option value="sales">销售积分</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="enableCommission">分成设置</label>
                        <div class="commission-options inline">
                            <label class="radio-option">
                                <input type="radio" id="enableCommissionYes" name="enableCommission" value="true">
                                <span class="radio-label">启用分成</span>
                            </label>
                            <label class="radio-option">
                                <input type="radio" id="enableCommissionNo" name="enableCommission" value="false" checked>
                                <span class="radio-label">不启用分成（默认）</span>
                            </label>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="adjustAmount">调整数量 *</label>
                        <input type="text"
                               id="adjustAmount"
                               name="adjustAmount"
                               required
                               placeholder="正数为增加，负数为减少"
                               pattern="^-?[0-9]+$"
                               title="请输入整数，支持数字小键盘输入"
                               autocomplete="off"
                               spellcheck="false">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <div class="footer-left">
                    <button type="button" class="btn btn-warning" id="clearMemberPointsBtn" style="display: none;">会员积分清零</button>
                    <button type="button" class="btn btn-warning" id="clearSalesPointsBtn" style="display: none;">销售积分清零</button>
                </div>
                <div class="footer-right">
                    <button type="button" class="btn btn-secondary" id="cancelAdjustBtn">取消</button>
                    <button type="submit" form="adjustPointsForm" class="btn btn-primary" id="confirmAdjustBtn">确认调整</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 积分清零确认对话框 -->
    <div class="modal" id="clearPointsConfirmModal">
        <div class="modal-content" style="max-width: 400px;">
            <div class="modal-header">
                <h3>确认清零</h3>
                <button class="modal-close" id="closeClearConfirmModal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="confirm-content">
                    <div class="confirm-icon">⚠️</div>
                    <p class="confirm-message" id="clearConfirmMessage">
                        确定要将该会员的积分清零吗？
                    </p>
                    <p class="confirm-warning">
                        此操作不可撤销，请谨慎操作。
                    </p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="cancelClearBtn">取消</button>
                <button type="button" class="btn btn-danger" id="confirmClearBtn">确认清零</button>
            </div>
        </div>
    </div>

    <!-- 消息提示 -->
    <div class="toast" id="toast">
        <div class="toast-content">
            <span class="toast-icon" id="toastIcon"></span>
            <span class="toast-message" id="toastMessage"></span>
        </div>
    </div>

    <!-- 积分配置确认对话框 -->
    <div class="modal" id="confirmModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>确认操作</h3>
                <button class="modal-close" id="cancelBtn">&times;</button>
            </div>
            <div class="modal-body">
                <p id="confirmMessage">确定要保存这些配置吗？</p>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="cancelBtn2">取消</button>
                <button class="btn btn-primary" id="confirmBtn">确定</button>
            </div>
        </div>
    </div>

    <!-- 积分配置消息提示 -->
    <div class="message-toast" id="messageToast" style="display: none;">
        <span class="message-icon"></span>
        <span class="message-text"></span>
    </div>

    <!-- 层级关系图右键菜单 -->
    <div class="context-menu" id="treeContextMenu" style="display: none;">
        <div class="context-menu-item" id="addSubMemberItem">
            <span class="context-menu-icon">👥</span>
            <span class="context-menu-text">添加下级会员</span>
        </div>
        <div class="context-menu-item" id="editMemberItem">
            <span class="context-menu-icon">✏️</span>
            <span class="context-menu-text">编辑当前会员</span>
        </div>
    </div>

    <script src="../node_modules/xlsx/dist/xlsx.full.min.js"></script>
    <!-- 新增管理员弹窗 -->
    <div class="modal" id="addAdminModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>新增管理员</h3>
                <button class="modal-close" id="closeAddAdminModal">&times;</button>
            </div>
            <div class="modal-body">
                <form id="addAdminForm">
                    <div class="form-group">
                        <label for="adminUsername">用户名 *</label>
                        <input type="text" id="adminUsername" name="adminUsername" required placeholder="请输入用户名">
                    </div>
                    <div class="form-group">
                        <label for="adminPassword">密码 *</label>
                        <input type="password" id="adminPassword" name="adminPassword" required placeholder="请输入密码">
                    </div>
                    <div class="form-group">
                        <label for="adminName">姓名 *</label>
                        <input type="text" id="adminName" name="adminName" required placeholder="请输入姓名">
                    </div>
                    <div class="form-group">
                        <label>角色</label>
                        <div class="role-display">
                            <span class="role-badge role-2">普通管理员</span>
                            <small class="text-muted">新创建的管理员默认为普通管理员角色</small>
                        </div>
                        <input type="hidden" id="adminRole" name="adminRole" value="2">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="cancelAddAdminBtn">取消</button>
                <button type="submit" form="addAdminForm" class="btn btn-primary" id="confirmAddAdminBtn">确认添加</button>
            </div>
        </div>
    </div>

    <!-- 重置管理员密码弹窗 -->
    <div class="modal" id="resetPasswordModal">
        <div class="modal-content" style="max-width: 450px;">
            <div class="modal-header">
                <h3>重置管理员密码</h3>
                <button class="modal-close" id="closeResetPasswordModal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="reset-password-info">
                    <p class="info-text">
                        正在为管理员 <strong id="resetPasswordAdminName"></strong> 重置密码
                    </p>
                    <p class="warning-text">
                        ⚠️ 重置后，该管理员需要使用新密码重新登录
                    </p>
                </div>
                
                <form id="resetPasswordForm">
                    <div class="form-group">
                        <label for="newAdminPassword">新密码 *</label>
                        <div class="password-input-group">
                            <input type="password" id="newAdminPassword" name="newAdminPassword" required 
                                   placeholder="请输入新密码（至少6位）" minlength="6">
                            <button type="button" class="password-toggle" id="toggleNewAdminPassword" 
                                    title="显示/隐藏密码">👁️</button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="confirmAdminPassword">确认密码 *</label>
                        <div class="password-input-group">
                            <input type="password" id="confirmAdminPassword" name="confirmAdminPassword" required 
                                   placeholder="请再次输入新密码" minlength="6">
                            <button type="button" class="password-toggle" id="toggleConfirmAdminPassword" 
                                    title="显示/隐藏密码">👁️</button>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="cancelResetPasswordBtn">取消</button>
                <button type="submit" form="resetPasswordForm" class="btn btn-danger" id="confirmResetPasswordBtn">确认重置</button>
            </div>
        </div>
    </div>

    <!-- 管理员操作确认对话框 -->
    <div class="modal" id="adminActionConfirmModal">
        <div class="modal-content" style="max-width: 400px;">
            <div class="modal-header">
                <h3 id="adminActionTitle">确认操作</h3>
                <button class="modal-close" id="closeAdminActionModal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="confirm-content">
                    <p class="confirm-message" id="adminActionMessage">
                        确定要执行此操作吗？
                    </p>
                    <p class="confirm-warning" id="adminActionWarning" style="display: none;">
                        此操作不可撤销，请谨慎操作。
                    </p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="cancelAdminActionBtn">取消</button>
                <button type="button" class="btn btn-primary" id="confirmAdminActionBtn">确认</button>
            </div>
        </div>
    </div>

    <script src="js/member-management.js"></script>
</body>
</html>
