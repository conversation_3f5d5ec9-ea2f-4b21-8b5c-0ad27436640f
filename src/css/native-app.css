/* 原生应用样式 - 禁用网页特性，增强桌面应用体验 */

/* 全局禁用文本选择和拖拽 */
* {
    /* 禁用文本选择，让应用更像原生桌面软件 */
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    
    /* 禁用元素拖拽 */
    -webkit-user-drag: none;
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -o-user-drag: none;
    user-drag: none;
    
    /* 禁用文本高亮 */
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
}

/* 为需要文本选择的表单元素恢复选择功能 */
input,
textarea,
select,
[contenteditable="true"],
.selectable,
.text-selectable {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
}

/* 特殊处理：允许某些只读输入框的文本选择（如显示数据的输入框） */
input[readonly],
textarea[readonly] {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
}

/* 禁用图片和媒体元素的拖拽 */
img,
video,
audio,
canvas,
svg,
picture {
    -webkit-user-drag: none;
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -o-user-drag: none;
    user-drag: none;
    
    /* 防止图片被选中 */
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    
    /* 禁用图片的默认拖拽行为 */
    pointer-events: none;
}

/* 恢复交互式图片的指针事件 */
img.interactive,
svg.interactive {
    pointer-events: auto;
}

/* 禁用链接的默认拖拽行为 */
a {
    -webkit-user-drag: none;
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -o-user-drag: none;
    user-drag: none;
}

/* 禁用按钮的拖拽 */
button,
.btn {
    -webkit-user-drag: none;
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -o-user-drag: none;
    user-drag: none;
}

/* 禁用表格的文本选择（除了特定列） */
table {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* 允许表格中的可选择内容 */
table .selectable,
table .text-selectable {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
}

/* 禁用滚动条的选择 */
::-webkit-scrollbar {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* 禁用文本光标在非输入元素上的显示 */
body,
div,
span,
p,
h1, h2, h3, h4, h5, h6,
label,
.tree-node-content,
.tab-button,
.panel-header {
    cursor: default;
}

/* 为交互元素恢复正确的光标 */
button,
.btn,
a,
[role="button"],
.clickable {
    cursor: pointer;
}

input,
textarea,
select {
    cursor: text;
}

input[type="checkbox"],
input[type="radio"] {
    cursor: pointer;
}

/* 禁用选择高亮 */
::selection {
    background: transparent;
}

::-moz-selection {
    background: transparent;
}

/* 为允许选择的元素恢复选择高亮 */
input::selection,
textarea::selection,
[contenteditable="true"]::selection,
.selectable::selection,
.text-selectable::selection {
    background: #b3d4fc;
}

input::-moz-selection,
textarea::-moz-selection,
[contenteditable="true"]::-moz-selection,
.selectable::-moz-selection,
.text-selectable::-moz-selection {
    background: #b3d4fc;
}

/* 禁用outline在非焦点元素上 */
*:not(input):not(textarea):not(select):not([tabindex]) {
    outline: none;
}

/* 为键盘导航保留焦点样式 */
button:focus-visible,
.btn:focus-visible,
a:focus-visible,
[role="button"]:focus-visible {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* 禁用双击选择 */
* {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* 禁用长按选择（移动端） */
* {
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: rgba(0,0,0,0);
}

/* 禁用拖拽时的虚影 */
* {
    -webkit-user-drag: none;
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -o-user-drag: none;
    user-drag: none;
}

/* 确保表单元素可以正常交互 */
input,
textarea,
select,
button,
[contenteditable="true"] {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
}

/* 按钮不需要文本选择 */
button,
.btn,
input[type="button"],
input[type="submit"],
input[type="reset"] {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}
