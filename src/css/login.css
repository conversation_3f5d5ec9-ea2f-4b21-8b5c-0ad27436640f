/* 登录页面样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    height: 100vh;
    overflow: hidden;
    position: relative;
}

.login-container {
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    z-index: 10;
}

.login-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    width: 100%;
    max-width: 420px;
    padding: 0;
    overflow: hidden;
    animation: slideIn 0.8s ease-out;
    position: relative;
    z-index: 100; /* 确保登录卡片在最上层 */
}

.login-header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    padding: 30px;
    text-align: center;
    color: white;
}

.logo h1 {
    font-size: 2.5em;
    margin-bottom: 5px;
    font-weight: 700;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.logo p {
    font-size: 1.1em;
    opacity: 0.9;
    font-weight: 300;
}

.login-form {
    padding: 40px 30px 30px;
}

.login-form h2 {
    color: #333;
    font-size: 1.8em;
    margin-bottom: 8px;
    text-align: center;
    font-weight: 600;
}

.login-subtitle {
    color: #666;
    text-align: center;
    margin-bottom: 30px;
    font-size: 0.95em;
}

.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #555;
    font-weight: 500;
    font-size: 0.95em;
}

.input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.input-icon {
    position: absolute;
    left: 15px;
    font-size: 1.1em;
    z-index: 2;
    opacity: 0.7;
}

.input-wrapper input {
    width: 100%;
    padding: 15px 15px 15px 45px;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    font-size: 1em;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.input-wrapper input:focus {
    outline: none;
    border-color: #4facfe;
    background: white;
    box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
}

.toggle-password {
    position: absolute;
    right: 15px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    border-radius: 6px;
    transition: background-color 0.2s ease;
}

.toggle-password:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.eye-icon {
    font-size: 1.1em;
    opacity: 0.6;
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.checkbox-wrapper {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 0.9em;
    color: #666;
}

.checkbox-wrapper input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid #ddd;
    border-radius: 4px;
    margin-right: 8px;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-wrapper input[type="checkbox"]:checked + .checkmark {
    background-color: #4facfe;
    border-color: #4facfe;
}

.checkbox-wrapper input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: -2px;
    left: 2px;
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.login-btn {
    width: 100%;
    padding: 16px;
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 1.1em;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(79, 172, 254, 0.3);
}

.login-btn:active {
    transform: translateY(0);
}

.login-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.btn-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.error-message {
    background: #fee;
    border: 1px solid #fcc;
    border-radius: 8px;
    padding: 12px;
    margin-top: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
    color: #c33;
    font-size: 0.9em;
}

.error-icon {
    font-size: 1.1em;
}

.login-footer {
    background: #f8f9fa;
    padding: 20px 30px;
    text-align: center;
    border-top: 1px solid #e9ecef;
}

.default-account {
    color: #666;
    font-size: 0.85em;
    margin-bottom: 8px;
}

.default-account strong {
    color: #333;
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
}

.version-info {
    color: #999;
    font-size: 0.8em;
}

/* 背景装饰 */
.background-decoration {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
    pointer-events: none; /* 防止阻止交互 */
}

.circle {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;
    pointer-events: none; /* 防止阻止交互 */
}

.circle-1 {
    width: 200px;
    height: 200px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.circle-2 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 10%;
    animation-delay: 2s;
}

.circle-3 {
    width: 100px;
    height: 100px;
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

/* 动画 */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0) rotate(0deg);
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* 响应式设计 */
@media (max-width: 480px) {
    .login-card {
        margin: 20px;
        max-width: none;
    }
    
    .login-form {
        padding: 30px 20px 20px;
    }
    
    .login-header {
        padding: 25px 20px;
    }
    
    .logo h1 {
        font-size: 2em;
    }
}
