/* 会员管理界面样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #f5f7fa;
    height: 100vh;
    min-height: 900px; /* 增加最小高度，避免会员管理出现竖向滚动条 */
    overflow: hidden;
}

.app-container {
    height: 100vh;
    min-height: 900px; /* 增加最小高度，避免会员管理出现竖向滚动条 */
    display: flex;
    flex-direction: column;
}

/* 顶部导航栏 */
.header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    padding: 15px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 100;
}

.header-left {
    display: flex;
    align-items: center;
}

.app-title {
    font-size: 1.5em;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo {
    font-size: 1.2em;
}

.header-right {
    display: flex;
    align-items: center;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.welcome-text {
    opacity: 0.9;
}

.admin-name {
    font-weight: 600;
    background: rgba(255, 255, 255, 0.2);
    padding: 5px 12px;
    border-radius: 15px;
}

.settings-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    padding: 8px 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1.1em;
}

.settings-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

.logout-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    padding: 8px 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1.1em;
}

.logout-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

/* 主要内容区域 */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* 功能导航 */
.nav-tabs {
    background: white;
    display: flex;
    border-bottom: 1px solid #e9ecef;
    padding: 0 30px;
}

.nav-tab {
    background: none;
    border: none;
    padding: 15px 25px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 1em;
    color: #666;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
}

.nav-tab:hover {
    color: #4facfe;
    background: rgba(79, 172, 254, 0.05);
}

.nav-tab.active {
    color: #4facfe;
    border-bottom-color: #4facfe;
    background: rgba(79, 172, 254, 0.05);
}

.tab-icon {
    font-size: 1.1em;
}

/* 标签页内容 */
.tab-content {
    flex: 1;
    padding: 20px 30px;
    overflow: auto;
    display: none;
}

.tab-content.active {
    display: block;
}

/* 会员管理页面固定布局 */
#members-tab.active {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
}

#members-tab .panel-header {
    flex-shrink: 0;
    margin-bottom: 15px;
}

#members-tab .table-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
    overflow: hidden;
}

#members-tab .table-footer {
    flex-shrink: 0;
    margin-top: auto;
}

/* 操作记录页面固定布局 */
#records-tab.active {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
}

#records-tab .panel-header {
    flex-shrink: 0;
    margin-bottom: 15px;
}

#records-tab .table-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
}

#records-tab .table-footer {
    flex-shrink: 0;
    margin-top: auto;
}

/* 面板头部 */
.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
}

.panel-header h2 {
    color: #333;
    font-size: 1.8em;
    font-weight: 600;
}

.panel-actions {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

/* 搜索框 */
.search-box {
    display: flex;
    align-items: center;
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    transition: border-color 0.3s ease;
}

.search-box:focus-within {
    border-color: #4facfe;
}

.search-box input {
    border: none;
    padding: 12px 15px;
    font-size: 1em;
    outline: none;
    width: 250px;
}

.search-btn {
    background: #4facfe;
    border: none;
    color: white;
    padding: 12px 15px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.search-btn:hover {
    background: #3d9bfe;
}

/* 按钮样式 */
.btn {
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-size: 1em;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.btn-primary {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(79, 172, 254, 0.3);
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(220, 53, 69, 0.3);
}

/* 表格容器 */
.table-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    margin-bottom: 20px;
}

/* 数据表格 */
.data-table {
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed;
}

.data-table th {
    background: #f8f9fa;
    padding: 12px 15px;
    text-align: left;
    font-weight: 600;
    color: #495057;
    border-bottom: 1px solid #dee2e6;
    font-size: 0.9em;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.data-table td {
    padding: 12px 15px;
    border-bottom: 1px solid #f1f3f4;
    color: #333;
    font-size: 0.9em;
    word-wrap: break-word;
    overflow: hidden;
}

/* 表格列宽设置 */
.data-table th:nth-child(1),
.data-table td:nth-child(1) { width: 50px; }   /* ID */

.data-table th:nth-child(2),
.data-table td:nth-child(2) { width: 120px; }  /* 会员名称 */

.data-table th:nth-child(3),
.data-table td:nth-child(3) {
    width: 180px;
    min-width: 180px;
    overflow: visible;
}   /* 层级 */

.data-table th:nth-child(4),
.data-table td:nth-child(4) { width: 80px; }   /* 会员积分 */

.data-table th:nth-child(5),
.data-table td:nth-child(5) { width: 80px; }   /* 销售积分 */

.data-table th:nth-child(6),
.data-table td:nth-child(6) { width: 120px; }  /* 生效日期 */

.data-table th:nth-child(7),
.data-table td:nth-child(7) { width: 120px; }  /* 到期日期/状态 */

.data-table th:nth-child(8),
.data-table td:nth-child(8) { width: 100px; }  /* 创建时间 */

.data-table th:nth-child(9),
.data-table td:nth-child(9) { width: 280px; } /* 操作 */

.data-table tbody tr:hover {
    background: rgba(79, 172, 254, 0.05);
}

/* 操作按钮样式 */
.action-buttons {
    display: flex;
    gap: 4px;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
    min-height: 32px;
}

.btn-edit {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 6px 10px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.8em;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 50px;
    height: 26px;
    box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
    position: relative;
    overflow: hidden;
    margin: 1px;
    white-space: nowrap;
}

.btn-edit::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-edit:hover::before {
    left: 100%;
}

.btn-edit:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(102, 126, 234, 0.4);
}

.btn-edit:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
}

.btn-delete {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    color: white;
    border: none;
    padding: 6px 10px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.8em;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 50px;
    height: 26px;
    box-shadow: 0 2px 4px rgba(255, 107, 107, 0.3);
    position: relative;
    overflow: hidden;
    margin: 1px;
    white-space: nowrap;
}

.btn-delete::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-delete:hover::before {
    left: 100%;
}

.btn-delete:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(255, 107, 107, 0.4);
}

.btn-delete:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(255, 107, 107, 0.3);
}

.btn-recharge {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border: none;
    padding: 6px 10px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.8em;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 60px;
    height: 26px;
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
    position: relative;
    overflow: hidden;
    margin: 1px;
    white-space: nowrap;
}

.btn-recharge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-recharge:hover::before {
    left: 100%;
}

.btn-recharge:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.4);
}

.btn-recharge:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

.btn-points {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
    color: white;
    border: none;
    padding: 6px 10px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.8em;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 50px;
    height: 26px;
    box-shadow: 0 2px 4px rgba(243, 156, 18, 0.3);
    position: relative;
    overflow: hidden;
    margin: 1px;
    white-space: nowrap;
}

.btn-points::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-points:hover::before {
    left: 100%;
}

.btn-points:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(243, 156, 18, 0.4);
}

.btn-points:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(243, 156, 18, 0.3);
}

/* 会员积分按钮样式 */
.btn-member-points {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
    border: none;
    padding: 6px 8px;
    border-radius: 6px;
    font-size: 0.75em;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 1px;
    white-space: nowrap;
    box-shadow: 0 2px 4px rgba(52, 152, 219, 0.3);
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 55px;
    height: 26px;
}

.btn-member-points::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-member-points:hover::before {
    left: 100%;
}

.btn-member-points:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(52, 152, 219, 0.4);
}

.btn-member-points:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(52, 152, 219, 0.3);
}

/* 销售积分按钮样式 */
.btn-sales-points {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    color: white;
    border: none;
    padding: 6px 8px;
    border-radius: 6px;
    font-size: 0.75em;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 1px;
    white-space: nowrap;
    box-shadow: 0 2px 4px rgba(231, 76, 60, 0.3);
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 55px;
    height: 26px;
}

.btn-sales-points::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-sales-points:hover::before {
    left: 100%;
}

.btn-sales-points:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(231, 76, 60, 0.4);
}

.btn-sales-points:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(231, 76, 60, 0.3);
}

/* 会员状态样式 */
.member-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8em;
    font-weight: 500;
    display: inline-block;
}

.member-status.ordinary {
    background: #f8f9fa;
    color: #6c757d;
    border: 1px solid #dee2e6;
}

.member-status.active {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.member-status.expired {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.member-status.pending {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.member-status.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.member-status.unknown {
    background: #e2e3e5;
    color: #383d41;
    border: 1px solid #d6d8db;
}

/* 积分抵扣样式 */
.points-deduction {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.points-input-group {
    display: flex;
    gap: 8px;
    align-items: center;
}

.points-input-group input {
    flex: 1;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 0.9em;
}

.points-deduction input {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 0.9em;
}

.btn-max {
    background: #28a745;
    color: white;
    border: none;
    padding: 10px 16px;
    border-radius: 6px;
    font-size: 0.9em;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.btn-max:hover {
    background: #218838;
    transform: translateY(-1px);
}

.btn-max:active {
    transform: translateY(0);
}

.points-info {
    display: flex;
    justify-content: space-between;
    font-size: 0.85em;
    color: #666;
}

.available-points {
    color: #28a745;
    font-weight: 500;
}

.max-deduction {
    color: #007bff;
    font-weight: 500;
}

/* 生效日期状态样式 */
.effective-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8em;
    font-weight: 500;
    display: inline-block;
}

.effective-status.none {
    background: #f8f9fa;
    color: #6c757d;
    border: 1px solid #dee2e6;
}

.effective-status.pending {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.effective-status.active {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

/* 筛选控件样式 */
.filter-controls {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: nowrap;
}

.filter-controls select,
.filter-controls input[type="date"] {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    min-width: 120px;
}

.filter-controls input[type="date"] {
    min-width: 140px;
}

.filter-controls .btn {
    padding: 8px 16px;
    white-space: nowrap;
}

/* 操作记录表格样式 */
.logs-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.95em;
    table-layout: fixed;
}

.logs-table td {
    padding: 12px 12px;
    border-bottom: 1px solid #e9ecef;
    vertical-align: middle;
    text-align: left;
    box-sizing: border-box;
    word-wrap: break-word;
    overflow-wrap: break-word;
    line-height: 1.4;
}

.logs-table tbody tr {
    display: table-row;
}

.logs-table tbody tr:hover {
    background: #f8f9fa;
}

/* 操作记录表格列宽设置 */
.logs-table td:nth-child(1) { width: 18%; } /* 时间 */
.logs-table td:nth-child(2) { width: 12%; } /* 操作员 */
.logs-table td:nth-child(3) { width: 15%; } /* 操作类型 */
.logs-table td:nth-child(4) { width: 20%; } /* 目标对象 */
.logs-table td:nth-child(5) { width: 35%; } /* 操作详情 */

/* 操作记录表头列宽设置 */
#records-tab .table-header-row .header-cell:nth-child(1) { width: 18%; } /* 时间 */
#records-tab .table-header-row .header-cell:nth-child(2) { width: 12%; } /* 操作员 */
#records-tab .table-header-row .header-cell:nth-child(3) { width: 15%; } /* 操作类型 */
#records-tab .table-header-row .header-cell:nth-child(4) { width: 20%; } /* 目标对象 */
#records-tab .table-header-row .header-cell:nth-child(5) { width: 35%; } /* 操作详情 */

/* 表格底部 */
.table-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 12px 20px;
    border-radius: 0 0 12px 12px;
    border-top: 1px solid #f1f3f4;
}

.table-info {
    color: #666;
    font-size: 0.9em;
}

/* 分页控件 */
.pagination {
    display: flex;
    gap: 5px;
}

.pagination button {
    padding: 8px 12px;
    border: 1px solid #dee2e6;
    background: white;
    color: #495057;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.pagination button:hover {
    background: #e9ecef;
}

.pagination button.active {
    background: #4facfe;
    color: white;
    border-color: #4facfe;
}

/* 模态框 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal.show {
    display: flex;
}

.modal-content {
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow: auto;
    animation: modalSlideIn 0.3s ease-out;
}

/* 模态框滚动条样式 */
.modal-content::-webkit-scrollbar {
    width: 8px;
}

.modal-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.modal-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
    transition: background 0.3s ease;
}

.modal-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.modal-header {
    padding: 20px 25px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    color: #333;
    font-size: 1.3em;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5em;
    cursor: pointer;
    color: #999;
    padding: 5px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background: #f8f9fa;
    color: #333;
}

.modal-body {
    padding: 25px;
}

.modal-footer {
    padding: 20px 25px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10px;
}

.footer-left {
    display: flex;
    gap: 10px;
}

.footer-right {
    display: flex;
    gap: 10px;
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

/* 分成选项样式 */
.commission-options.inline {
    display: flex;
    gap: 20px;
    align-items: center;
}

.commission-options .radio-option {
    display: flex;
    align-items: center;
    gap: 6px;
    cursor: pointer;
    margin: 0;
}

.commission-options .radio-option input[type="radio"] {
    margin: 0;
}

.commission-options .radio-option .radio-label {
    font-size: 14px;
    color: #333;
    white-space: nowrap;
}

.form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

.form-group-half {
    flex: 1;
    margin-bottom: 0;
}

/* 层级显示样式 */
.level-display {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    padding: 4px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

.level-icon {
    font-size: 16px;
    line-height: 1;
}

.level-text {
    font-size: 12px;
    font-weight: 600;
    color: #495057;
    white-space: nowrap;
}

.level-bar {
    flex: 1;
    height: 4px;
    background: #e9ecef;
    border-radius: 2px;
    overflow: hidden;
    position: relative;
}

.level-progress {
    height: 100%;
    background: linear-gradient(90deg, #28a745 0%, #20c997 50%, #17a2b8 100%);
    border-radius: 2px;
    transition: width 0.3s ease;
}

/* 不同层级的颜色主题 */
.level-display.level-1 {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    border-color: #dc3545;
}

.level-display.level-1 .level-progress {
    background: linear-gradient(90deg, #dc3545 0%, #c82333 100%);
}

.level-display.level-2 {
    background: linear-gradient(135deg, #fde2d1 0%, #fcd5b4 100%);
    border-color: #fd7e14;
}

.level-display.level-2 .level-progress {
    background: linear-gradient(90deg, #fd7e14 0%, #e8690b 100%);
}

.level-display.level-3 {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border-color: #ffc107;
}

.level-display.level-3 .level-progress {
    background: linear-gradient(90deg, #ffc107 0%, #e0a800 100%);
}

.level-display.level-4 {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    border-color: #28a745;
}

.level-display.level-4 .level-progress {
    background: linear-gradient(90deg, #28a745 0%, #1e7e34 100%);
}

.level-display.level-5 {
    background: linear-gradient(135deg, #d1f2eb 0%, #a3e4d7 100%);
    border-color: #20c997;
}

.level-display.level-5 .level-progress {
    background: linear-gradient(90deg, #20c997 0%, #1aa179 100%);
}

.level-display.level-6 {
    background: linear-gradient(135deg, #cff4fc 0%, #9eeaf9 100%);
    border-color: #0dcaf0;
}

.level-display.level-6 .level-progress {
    background: linear-gradient(90deg, #0dcaf0 0%, #0aa2c0 100%);
}

.level-display.level-7 {
    background: linear-gradient(135deg, #e2d9f3 0%, #d1c4e9 100%);
    border-color: #6f42c1;
}

.level-display.level-7 .level-progress {
    background: linear-gradient(90deg, #6f42c1 0%, #59359a 100%);
}

.level-display.level-8 {
    background: linear-gradient(135deg, #f7d6e6 0%, #f2b5d4 100%);
    border-color: #d63384;
}

.level-display.level-8 .level-progress {
    background: linear-gradient(90deg, #d63384 0%, #b02a5b 100%);
}

.level-display.level-9 {
    background: linear-gradient(135deg, #e0cffc 0%, #d1b3ff 100%);
    border-color: #6610f2;
}

.level-display.level-9 .level-progress {
    background: linear-gradient(90deg, #6610f2 0%, #520dc2 100%);
}

.level-display.level-10 {
    background: linear-gradient(135deg, #f6d6e7 0%, #f0b7d1 100%);
    border-color: #e83e8c;
}

.level-display.level-10 .level-progress {
    background: linear-gradient(90deg, #e83e8c 0%, #c2185b 100%);
}

.level-display.level-high {
    background: linear-gradient(135deg, #e2e3e5 0%, #d6d8db 100%);
    border-color: #6c757d;
}

.level-display.level-high .level-progress {
    background: linear-gradient(90deg, #6c757d 0%, #495057 100%);
}

/* 层级预览样式 */
.hierarchy-preview {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 12px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    margin-top: 8px;
    font-size: 12px;
}

.hierarchy-preview .arrow {
    color: #6c757d;
    font-weight: bold;
}

.hierarchy-preview .new-level {
    color: #28a745;
    font-weight: 600;
}

.parent-level-info {
    color: #495057;
}

/* 层级选择提示 */
.level-hint {
    font-size: 11px;
    color: #6c757d;
    margin-top: 4px;
    font-style: italic;
}

/* 树状图样式 */
.tree-container {
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    max-height: 600px;
    overflow: auto;
}

/* 层级关系图滚动条样式 */
.tree-container::-webkit-scrollbar {
    width: 8px;
}

.tree-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.tree-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
    transition: background 0.3s ease;
}

.tree-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.member-tree {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.tree-node {
    margin: 8px 0;
    position: relative;
}

.tree-node-content {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 8px;
    border: 2px solid #dee2e6;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    margin: 4px 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

/* 不同层级的节点背景色 */
.tree-node-content[data-level="1"] {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    border-color: #dc3545;
}

.tree-node-content[data-level="2"] {
    background: linear-gradient(135deg, #fde2d1 0%, #fcd5b4 100%);
    border-color: #fd7e14;
}

.tree-node-content[data-level="3"] {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border-color: #ffc107;
}

.tree-node-content[data-level="4"] {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    border-color: #28a745;
}

.tree-node-content[data-level="5"] {
    background: linear-gradient(135deg, #d1f2eb 0%, #a3e4d7 100%);
    border-color: #20c997;
}

.tree-node-content[data-level="6"] {
    background: linear-gradient(135deg, #cff4fc 0%, #9eeaf9 100%);
    border-color: #0dcaf0;
}

.tree-node-content[data-level="7"] {
    background: linear-gradient(135deg, #e2d9f3 0%, #d1c4e9 100%);
    border-color: #6f42c1;
}

.tree-node-content[data-level="8"] {
    background: linear-gradient(135deg, #f7d6e6 0%, #f2b5d4 100%);
    border-color: #d63384;
}

.tree-node-content[data-level="9"] {
    background: linear-gradient(135deg, #e0cffc 0%, #d1b3ff 100%);
    border-color: #6610f2;
}

.tree-node-content[data-level="10"] {
    background: linear-gradient(135deg, #f6d6e7 0%, #f0b7d1 100%);
    border-color: #e83e8c;
}

.tree-node-content[data-level="11"], .tree-node-content[data-level="12"] {
    background: linear-gradient(135deg, #e2e3e5 0%, #d6d8db 100%);
    border-color: #6c757d;
}

.tree-node-content:hover {
    background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.tree-node-content.expanded {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-color: #2196f3;
}

.tree-node-content.selected {
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
    border-color: #4caf50;
    box-shadow: 0 2px 6px rgba(76, 175, 80, 0.3);
}

.tree-node-content.selected:hover {
    background: linear-gradient(135deg, #c8e6c9 0%, #a5d6a7 100%);
    border-color: #388e3c;
}

.tree-toggle {
    width: 20px;
    height: 20px;
    border: none;
    background: #007bff;
    color: white;
    border-radius: 50%;
    font-size: 12px;
    cursor: pointer;
    margin-right: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.tree-toggle:hover {
    background: #0056b3;
    transform: scale(1.1);
}

.tree-toggle.no-children {
    background: #6c757d;
    cursor: default;
}

.tree-toggle.no-children:hover {
    background: #6c757d;
    transform: none;
}

.tree-member-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.tree-member-name {
    font-weight: 600;
    color: #212529;
    font-size: 14px;
}

.tree-member-level {
    font-size: 12px;
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 500;
}

.tree-member-level.level-1 {
    background: #dc3545;
    color: #fff;
    font-weight: 700;
    box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
    border: 2px solid #dc3545;
}

.tree-member-level.level-2 {
    background: #fd7e14;
    color: #fff;
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(253, 126, 20, 0.3);
    border: 2px solid #fd7e14;
}

.tree-member-level.level-3 {
    background: #ffc107;
    color: #000;
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);
    border: 2px solid #ffc107;
}

.tree-member-level.level-4 {
    background: #28a745;
    color: #fff;
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
    border: 2px solid #28a745;
}

.tree-member-level.level-5 {
    background: #20c997;
    color: #fff;
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(32, 201, 151, 0.3);
    border: 2px solid #20c997;
}

.tree-member-level.level-6 {
    background: #0dcaf0;
    color: #000;
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(13, 202, 240, 0.3);
    border: 2px solid #0dcaf0;
}

.tree-member-level.level-7 {
    background: #6f42c1;
    color: #fff;
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(111, 66, 193, 0.3);
    border: 2px solid #6f42c1;
}

.tree-member-level.level-8 {
    background: #d63384;
    color: #fff;
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(214, 51, 132, 0.3);
    border: 2px solid #d63384;
}

.tree-member-level.level-9 {
    background: #6610f2;
    color: #fff;
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(102, 16, 242, 0.3);
    border: 2px solid #6610f2;
}

.tree-member-level.level-10 {
    background: #e83e8c;
    color: #fff;
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(232, 62, 140, 0.3);
    border: 2px solid #e83e8c;
}

.tree-member-level.level-high {
    background: #6c757d;
    color: #fff;
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(108, 117, 125, 0.3);
    border: 2px solid #6c757d;
}

.tree-member-stats {
    display: flex;
    gap: 8px;
    font-size: 12px;
    color: #6c757d;
}

.tree-children {
    margin-left: 40px;
    padding-left: 20px;
    border-left: 3px solid #007bff;
    position: relative;
    transition: all 0.3s ease;
    background: linear-gradient(to right, rgba(0, 123, 255, 0.05) 0%, transparent 50%);
}

.tree-children::before {
    content: '';
    position: absolute;
    left: -3px;
    top: 0;
    bottom: 0;
    width: 6px;
    background: linear-gradient(to bottom, #007bff 0%, rgba(0, 123, 255, 0.6) 50%, rgba(0, 123, 255, 0.2) 100%);
    border-radius: 3px;
    box-shadow: 0 0 8px rgba(0, 123, 255, 0.3);
}

/* 不同层级的连接线颜色 */
.tree-children[data-member-id] {
    border-left-color: #6c757d;
}

.tree-node[data-member-id] .tree-children {
    border-left-color: #28a745;
}

.tree-node[data-member-id] .tree-node[data-member-id] .tree-children {
    border-left-color: #ffc107;
}

.tree-node[data-member-id] .tree-node[data-member-id] .tree-node[data-member-id] .tree-children {
    border-left-color: #dc3545;
}

.tree-children.collapsed {
    display: none;
}

.tree-loading {
    text-align: center;
    padding: 40px;
    color: #6c757d;
    font-style: italic;
}

.tree-empty {
    text-align: center;
    padding: 40px;
    color: #6c757d;
    font-style: italic;
}

.tree-error {
    text-align: center;
    padding: 40px;
    color: #dc3545;
    background: #f8d7da;
    border-radius: 6px;
    border: 1px solid #f5c6cb;
}

/* 搜索高亮样式 */
.tree-node-content.search-highlight {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%) !important;
    border: 3px solid #ffc107 !important;
    box-shadow: 0 0 20px rgba(255, 193, 7, 0.6) !important;
    transform: scale(1.02) !important;
    animation: searchPulse 1.5s ease-in-out;
}

@keyframes searchPulse {
    0% {
        box-shadow: 0 0 20px rgba(255, 193, 7, 0.6);
    }
    50% {
        box-shadow: 0 0 30px rgba(255, 193, 7, 0.8);
    }
    100% {
        box-shadow: 0 0 20px rgba(255, 193, 7, 0.6);
    }
}

/* 右键菜单样式 */
.context-menu {
    position: fixed;
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    min-width: 160px;
    padding: 4px 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.context-menu-item {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    font-size: 14px;
    color: #333;
}

.context-menu-item:hover {
    background: #f8f9fa;
}

.context-menu-item:active {
    background: #e9ecef;
}

.context-menu-icon {
    margin-right: 8px;
    font-size: 16px;
    width: 20px;
    text-align: center;
}

.context-menu-text {
    flex: 1;
    white-space: nowrap;
}

/* 防止右键菜单被选中 */
.context-menu {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #555;
    font-weight: 500;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1em;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #4facfe;
}

/* 带搜索的选择框样式 */
.select-with-search {
    position: relative;
}

.select-with-search .search-input {
    width: 100%;
    padding: 10px 15px;
    border: 2px solid #e9ecef;
    border-bottom: 1px solid #dee2e6;
    border-radius: 8px 8px 0 0;
    font-size: 0.9em;
    background: #f8f9fa;
    transition: border-color 0.3s ease;
}

.select-with-search .search-input:focus {
    outline: none;
    border-color: #4facfe;
    background: white;
}

.select-with-search select {
    width: 100%;
    border-radius: 0 0 8px 8px;
    border-top: none;
    max-height: 150px;
    overflow-y: auto;
    background: white;
}

.select-with-search select option {
    padding: 8px 12px;
    border-bottom: 1px solid #f1f3f4;
}

.select-with-search select option:hover {
    background: #f8f9fa;
}

.select-with-search select option:last-child {
    border-bottom: none;
}

/* 确认对话框样式 */
.confirm-content {
    text-align: center;
    padding: 20px 0;
}

.confirm-icon {
    font-size: 3em;
    margin-bottom: 15px;
}

.confirm-message {
    font-size: 1.1em;
    color: #333;
    margin-bottom: 10px;
    font-weight: 500;
}

.confirm-warning {
    font-size: 0.9em;
    color: #666;
    margin: 0;
}

/* 密码修改弹窗样式 */
.password-modal {
    max-width: 420px;
    max-height: 90vh;
}

.password-modal .modal-body {
    padding: 20px 25px;
}

.password-modal .form-group {
    margin-bottom: 16px;
}

.password-modal .form-group:last-of-type {
    margin-bottom: 0;
}

.password-modal .input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.password-modal .input-wrapper input {
    width: 100%;
    padding-right: 45px;
}

.password-modal .toggle-password {
    position: absolute;
    right: 15px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    border-radius: 6px;
    transition: background-color 0.2s ease;
}

.password-modal .toggle-password:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.password-modal .eye-icon {
    font-size: 1.1em;
    opacity: 0.6;
}

.password-tips {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 8px;
    padding: 12px 15px;
    margin-top: 12px;
    border-left: 3px solid #4facfe;
}

.tips-title {
    font-size: 0.85em;
    font-weight: 600;
    color: #333;
    margin: 0 0 8px 0;
    display: flex;
    align-items: center;
    gap: 5px;
}

.tips-content {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.tip-item {
    font-size: 0.8em;
    color: #666;
    line-height: 1.3;
}

/* 加载和提示 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 2000;
}

.loading-overlay.show {
    display: flex;
}

.loading-spinner {
    text-align: center;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #4facfe;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 15px;
}

.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    padding: 15px 20px;
    display: none;
    align-items: center;
    gap: 10px;
    z-index: 3000;
    animation: slideInRight 0.3s ease-out;
}

.toast.show {
    display: flex;
}

.toast-icon {
    font-size: 1.2em;
}

/* 动画 */
@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 积分管理样式 */
.points-management-container {
    display: flex;
    gap: 30px;
    margin-top: 20px;
}

.recharge-section,
.recent-operations {
    flex: 1;
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.recharge-section h3,
.recent-operations h3 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 1.1em;
    border-bottom: 2px solid #4facfe;
    padding-bottom: 8px;
}

.search-hint,
.no-data {
    text-align: center;
    color: #999;
    padding: 40px 20px;
    font-style: italic;
}

.recharge-search-results {
    min-height: 200px;
}

.member-search-item {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.member-search-item:hover {
    border-color: #4facfe;
    background: #f8f9fa;
}

.member-search-item h4 {
    margin: 0 0 8px 0;
    color: #333;
}

.member-search-item .member-details {
    display: flex;
    gap: 20px;
    font-size: 0.9em;
    color: #666;
}

/* 充值弹窗样式 */
.recharge-modal,
.adjust-modal {
    max-width: 600px;
    max-height: 90vh;
}

.member-info-section {
    margin-bottom: 20px;
}

.member-info-section h4 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 1em;
}

.member-info-card {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    border-left: 4px solid #4facfe;
}

.info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 0.9em;
}

.info-item:last-child {
    margin-bottom: 0;
}

.info-item label {
    font-weight: 500;
    color: #555;
}

.info-item span {
    color: #333;
    font-weight: 600;
}

.recharge-summary {
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
    border-left: 4px solid #4facfe;
}

.recharge-summary h4 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 1em;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 0.9em;
}

.summary-item:last-child {
    margin-bottom: 0;
}

.summary-item label {
    color: #555;
}

.summary-item span {
    color: #333;
    font-weight: 600;
}

.summary-note {
    margin-top: 15px;
    padding: 10px;
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    border-left: 4px solid #f39c12;
}

.summary-note p {
    margin: 0;
    font-size: 0.85em;
    color: #856404;
    font-weight: 500;
}

/* 操作记录样式 */
.operations-list {
    max-height: 300px;
    overflow-y: auto;
}

.operation-item {
    border-bottom: 1px solid #f1f3f4;
    padding: 12px 0;
}

.operation-item:last-child {
    border-bottom: none;
}

.operation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.operation-type {
    font-weight: 600;
    color: #333;
}

.operation-time {
    font-size: 0.8em;
    color: #999;
}

.operation-details {
    font-size: 0.9em;
    color: #666;
}

/* 积分配置相关样式 */
.table-container {
    position: relative;
    max-height: 600px;
    overflow: hidden;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

/* 分成比例设置页面的表格容器 */
#points-config-tab .table-container {
    position: relative;
    height: calc(100vh - 150px) !important; /* 设置固定高度 */
    overflow: hidden !important;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
}

.table-header-row {
    display: table;
    width: 100%;
    background: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
    position: sticky;
    top: 0;
    z-index: 10;
    font-size: 0.95em;
    table-layout: fixed;
}

.header-cell {
    display: table-cell;
    padding: 12px 15px;
    text-align: left;
    white-space: nowrap;
    box-sizing: border-box;
    vertical-align: middle;
}

/* 通用表头列宽设置已移至各页面专用设置 */

.table-wrapper {
    max-height: 500px;
    overflow-y: auto;
    overflow-x: hidden;
}

/* 会员管理页面的表格包装器 */
#members-tab .table-wrapper {
    flex: 1;
    min-height: 500px; /* 增加最小高度 */
    max-height: calc(100vh - 250px); /* 减少顶部预留空间，增加表格可用高度 */
    overflow-y: auto;
    overflow-x: hidden;
}

/* 自定义滚动条样式 */
.table-wrapper::-webkit-scrollbar {
    width: 8px;
}

.table-wrapper::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.table-wrapper::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
    transition: background 0.3s ease;
}

.table-wrapper::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 操作记录页面的表格包装器 */
#records-tab .table-wrapper {
    flex: 1;
    min-height: 500px; /* 增加最小高度 */
    max-height: calc(100vh - 250px); /* 减少顶部预留空间，增加表格可用高度 */
    overflow-y: auto;
    overflow-x: hidden;
    /* 热重载测试注释 */
}

/* 操作记录页面滚动条样式 */
#records-tab .table-wrapper::-webkit-scrollbar {
    width: 8px;
}

#records-tab .table-wrapper::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

#records-tab .table-wrapper::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
    transition: background 0.3s ease;
}

#records-tab .table-wrapper::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 会员管理表格样式 */
.members-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.95em;
    table-layout: fixed;
}

.members-table td {
    padding: 8px 12px;
    border-bottom: 1px solid #e9ecef;
    vertical-align: middle;
    text-align: left;
    box-sizing: border-box;
    word-wrap: break-word;
    overflow-wrap: break-word;
    line-height: 1.3;
}

.members-table tbody tr {
    display: table-row;
}

.members-table tbody tr:hover {
    background: #f8f9fa;
}

/* 会员管理表格列宽设置 */
.members-table td:nth-child(1) { width: 5%; }   /* ID */
.members-table td:nth-child(2) { width: 10%; }  /* 会员名称 */
.members-table td:nth-child(3) { width: 12%; }  /* 层级 - 减小宽度 */
.members-table td:nth-child(4) { width: 7%; }   /* 会员积分 */
.members-table td:nth-child(5) { width: 7%; }   /* 销售积分 */
.members-table td:nth-child(6) { width: 10%; }  /* 生效日期 */
.members-table td:nth-child(7) { width: 10%; }  /* 到期日期 */
.members-table td:nth-child(8) { width: 9%; }   /* 创建时间 */
.members-table td:nth-child(9) { width: 30%; }  /* 操作 - 增加宽度 */

/* 会员管理表头列宽设置 */
#members-tab .table-header-row .header-cell:nth-child(1) { width: 5%; }   /* ID */
#members-tab .table-header-row .header-cell:nth-child(2) { width: 10%; }  /* 会员名称 */
#members-tab .table-header-row .header-cell:nth-child(3) { width: 12%; }  /* 层级 - 减小宽度 */
#members-tab .table-header-row .header-cell:nth-child(4) { width: 7%; }   /* 会员积分 */
#members-tab .table-header-row .header-cell:nth-child(5) { width: 7%; }   /* 销售积分 */
#members-tab .table-header-row .header-cell:nth-child(6) { width: 10%; }  /* 生效日期 */
#members-tab .table-header-row .header-cell:nth-child(7) { width: 10%; }  /* 到期日期 */
#members-tab .table-header-row .header-cell:nth-child(8) { width: 9%; }   /* 创建时间 */
#members-tab .table-header-row .header-cell:nth-child(9) { width: 30%; }  /* 操作 - 增加宽度 */

.config-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.95em;
    table-layout: fixed;
}

.config-table td {
    padding: 12px 15px;
    border-bottom: 1px solid #e9ecef;
    vertical-align: middle;
    text-align: left;
    box-sizing: border-box;
}

.config-table tbody tr {
    display: table-row;
}

.config-table tbody tr:hover {
    background: #f8f9fa;
}

/* 确保表格列宽与表头对齐 */
.config-table td:nth-child(1) { width: 20%; } /* 层级 */
.config-table td:nth-child(2) { width: 30%; } /* 层级说明 */
.config-table td:nth-child(3) { width: 25%; } /* 会员积分分成 */
.config-table td:nth-child(4) { width: 25%; } /* 销售积分分成 */

/* 积分配置表头列宽设置 */
#points-config-tab .table-header-row .header-cell:nth-child(1) { width: 20%; } /* 层级 */
#points-config-tab .table-header-row .header-cell:nth-child(2) { width: 30%; } /* 层级说明 */
#points-config-tab .table-header-row .header-cell:nth-child(3) { width: 25%; } /* 会员积分分成 */
#points-config-tab .table-header-row .header-cell:nth-child(4) { width: 25%; } /* 销售积分分成 */

/* 分成比例设置页面的表格包装器 */
#points-config-tab .table-wrapper {
    flex: 1 !important; /* 占据剩余空间 */
    min-height: 400px !important;
    max-height: none !important; /* 移除最大高度限制，让它占满容器 */
    overflow-y: auto !important;
    overflow-x: hidden !important;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
}

/* 分成比例设置页面滚动条样式 */
#points-config-tab .table-wrapper::-webkit-scrollbar {
    width: 8px;
}

#points-config-tab .table-wrapper::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

#points-config-tab .table-wrapper::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
    transition: background 0.3s ease;
}

#points-config-tab .table-wrapper::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 层级标识 */
.level-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    border-radius: 50%;
    font-weight: 600;
    font-size: 0.85em;
}

/* 输入框样式 */
.ratio-input {
    width: 80px;
    padding: 8px 12px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    text-align: center;
    font-size: 0.9em;
    transition: all 0.3s ease;
}

.ratio-input:focus {
    outline: none;
    border-color: #4facfe;
    box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
}

.ratio-input.error {
    border-color: #dc3545;
    background: #fff5f5;
}

/* 比例显示样式（只读文本） */
.ratio-display {
    display: inline-block;
    padding: 8px 12px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    text-align: center;
    font-size: 0.9em;
    font-weight: 500;
    color: #495057;
    min-width: 60px;
}

.ratio-display.member-ratio {
    background: #e3f2fd;
    border-color: #bbdefb;
    color: #1976d2;
}

.ratio-display.sales-ratio {
    background: #fff3e0;
    border-color: #ffcc02;
    color: #f57c00;
}

/* 操作按钮 */
.action-btn {
    padding: 5px 10px;
    border: none;
    border-radius: 4px;
    font-size: 0.8em;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    white-space: nowrap;
    min-width: 50px;
}

.edit-btn {
    background: #17a2b8;
    color: white;
}

.edit-btn:hover {
    background: #138496;
    transform: translateY(-1px);
}

.save-btn {
    background: #28a745;
    color: white;
}

.save-btn:hover {
    background: #218838;
}

.cancel-btn {
    background: #6c757d;
    color: white;
    margin-left: 5px;
}

.cancel-btn:hover {
    background: #5a6268;
}

/* 小尺寸按钮样式 - 用于表格内的按钮 */
.btn-sm {
    padding: 6px 12px;
    font-size: 0.85em;
    min-height: 28px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

/* 编辑状态样式 */
.editing-row {
    background: #fff3cd !important;
    border-left: 4px solid #ffc107;
}

.editing-row .ratio-input {
    border-color: #ffc107;
    background: white;
}

/* 成功/错误状态 */
.success-row {
    background: #d4edda !important;
    border-left: 4px solid #28a745;
}

.error-row {
    background: #f8d7da !important;
    border-left: 4px solid #dc3545;
}

/* 消息提示样式 */
.message-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    padding: 15px 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    z-index: 1000;
    min-width: 300px;
    border-left: 4px solid #28a745;
    animation: slideInRight 0.3s ease;
}

.message-toast.error {
    border-left-color: #dc3545;
}

.message-toast.warning {
    border-left-color: #ffc107;
}

.message-icon {
    font-size: 1.2em;
}

.message-text {
    flex: 1;
    color: #333;
    font-weight: 500;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header {
        padding: 10px 15px;
    }

    .main-content {
        padding: 15px;
    }

    .panel-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .panel-actions {
        width: 100%;
        justify-content: space-between;
    }

    .search-box input {
        width: 200px;
    }

    .points-management-container {
        flex-direction: column;
        gap: 20px;
    }

    .member-details {
        flex-direction: column;
        gap: 5px !important;
    }
    
    .config-table {
        font-size: 0.85em;
    }
    
    .config-table td {
        padding: 12px 8px;
    }
    
    .header-cell {
        padding: 12px 8px;
    }
}

/* ==================== 分成统计样式 ==================== */

/* 日期范围筛选 */
.date-range-filter {
    display: flex;
    align-items: center;
    gap: 10px;
}

.date-range-filter input[type="date"],
.date-range-filter select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    background: white;
}

.date-range-filter select {
    min-width: 140px;
}

.date-range-filter span {
    color: #666;
    font-weight: 500;
}

/* 统计概览卡片 */
.stats-overview {
    margin-bottom: 30px;
}

.stats-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
}

.stats-card h3 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 18px;
    font-weight: 600;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #007bff;
}

.stat-item label {
    font-weight: 500;
    color: #555;
}

.stat-item span {
    font-weight: 600;
    color: #007bff;
    font-size: 16px;
}

/* 分成排行榜 */
.commission-ranking {
    margin-bottom: 30px;
}

.commission-ranking h3 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 18px;
    font-weight: 600;
}

.ranking-tabs {
    display: flex;
    gap: 5px;
    margin-bottom: 15px;
}

.ranking-tab {
    padding: 10px 20px;
    border: 1px solid #ddd;
    background: #f8f9fa;
    border-radius: 6px 6px 0 0;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s ease;
}

.ranking-tab:hover {
    background: #e9ecef;
}

.ranking-tab.active {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.ranking-content {
    border: 1px solid #ddd;
    border-radius: 0 6px 6px 6px;
    background: white;
}

/* 分成记录详情 */
.commission-details h3 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 18px;
    font-weight: 600;
}

/* 分成统计表格特殊样式 */
#commission-stats-tab .data-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #495057;
    border-bottom: 2px solid #dee2e6;
}

#commission-stats-tab .data-table td {
    vertical-align: middle;
}

/* 分成金额高亮 */
.commission-amount {
    font-weight: 600;
    color: #28a745;
}

.commission-amount.negative {
    color: #dc3545;
}

/* 积分类型标签 */
.points-type-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
    min-width: 60px;
}

.points-type-badge.member {
    background: #e3f2fd;
    color: #1976d2;
}

.points-type-badge.sales {
    background: #f3e5f5;
    color: #7b1fa2;
}

/* 触发类型标签 */
.trigger-type-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
    min-width: 60px;
}

.trigger-type-badge.recharge {
    background: #e8f5e8;
    color: #2e7d32;
}

.trigger-type-badge.manual_adjust {
    background: #fff3e0;
    color: #f57c00;
}

/* 层级标签 */
.level-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    font-size: 12px;
    font-weight: 600;
    text-align: center;
    background: #6c757d;
    color: white;
    line-height: 1;
}

.level-badge.level-1 { background: #dc3545; }
.level-badge.level-2 { background: #fd7e14; }
.level-badge.level-3 { background: #ffc107; color: #000; }
.level-badge.level-4 { background: #28a745; }
.level-badge.level-5 { background: #20c997; }
.level-badge.level-6 { background: #0dcaf0; color: #000; }
.level-badge.level-7 { background: #6f42c1; }
.level-badge.level-8 { background: #d63384; }
.level-badge.level-9 { background: #6610f2; }
.level-badge.level-10 { background: #e83e8c; }
.level-badge.level-11 { background: #495057; }
.level-badge.level-12 { background: #212529; }

/* 分成统计分页控制样式 */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 15px 20px;
    border-top: 1px solid #e9ecef;
    margin-top: 0;
}

.pagination-info {
    color: #666;
    font-size: 0.9em;
}

.pagination-controls,
.pagination {
    display: flex;
    gap: 5px;
    align-items: center;
}

.pagination-controls button,
.pagination button {
    padding: 8px 12px;
    border: 1px solid #dee2e6;
    background: white;
    color: #495057;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9em;
}

.pagination-controls button:hover:not(:disabled),
.pagination button:hover {
    background: #e9ecef;
}

.pagination-controls button:disabled {
    background: #f8f9fa;
    color: #6c757d;
    cursor: not-allowed;
    opacity: 0.6;
}

.pagination-controls button.active,
.pagination button.active {
    background: #4facfe;
    color: white;
    border-color: #4facfe;
}

.pagination span {
    margin: 0 5px;
    color: #666;
    font-size: 0.9em;
}

.page-info {
    margin: 0 10px;
    color: #666;
    font-size: 0.9em;
    font-weight: 500;
}

/* 分成统计表格容器样式 */
.commission-details .table-container,
.commission-ranking .table-container {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
}

/* 分成排行榜表格样式 */
#commissionRankingTable {
    table-layout: fixed;
    width: 100%;
}

/* 分成排行榜表格列宽控制 */
#commissionRankingTable th:nth-child(1),
#commissionRankingTable td:nth-child(1) {
    width: 40px !important; /* 排名列 */
    min-width: 40px !important;
    max-width: 40px !important;
}

#commissionRankingTable th:nth-child(2),
#commissionRankingTable td:nth-child(2) {
    width: 150px !important; /* 会员姓名列 */
    min-width: 150px !important;
    max-width: 150px !important;
}

#commissionRankingTable th:nth-child(3),
#commissionRankingTable td:nth-child(3) {
    width: 120px !important; /* 分成总额列 */
    min-width: 120px !important;
    max-width: 120px !important;
}

#commissionRankingTable th:nth-child(4),
#commissionRankingTable td:nth-child(4) {
    width: 100px !important; /* 分成次数列 */
    min-width: 100px !important;
    max-width: 100px !important;
}

#commissionRankingTable th:nth-child(5),
#commissionRankingTable td:nth-child(5) {
    width: 120px !important; /* 平均分成列 */
    min-width: 120px !important;
    max-width: 120px !important;
}

#commissionRankingTable th:nth-child(6),
#commissionRankingTable td:nth-child(6) {
    width: 100px !important; /* 操作列 */
    min-width: 100px !important;
    max-width: 100px !important;
}

/* 分成统计数据表格样式 */
#commission-stats-tab .data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9em;
}

#commission-stats-tab .data-table th,
#commission-stats-tab .data-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
}

/* 分成统计表格列宽设置 */
#commission-stats-tab .data-table th:nth-child(1),
#commission-stats-tab .data-table td:nth-child(1) {
    width: 150px;
    min-width: 150px;
    white-space: nowrap;
}

#commission-stats-tab .data-table th:nth-child(2),
#commission-stats-tab .data-table td:nth-child(2) {
    width: 100px;
    min-width: 100px;
}

#commission-stats-tab .data-table th:nth-child(3),
#commission-stats-tab .data-table td:nth-child(3) {
    width: 100px;
    min-width: 100px;
}

#commission-stats-tab .data-table th:nth-child(4),
#commission-stats-tab .data-table td:nth-child(4) {
    width: 60px;
    min-width: 60px;
    text-align: center;
}

#commission-stats-tab .data-table th:nth-child(5),
#commission-stats-tab .data-table td:nth-child(5) {
    width: 100px;
    min-width: 100px;
    text-align: center;
}

#commission-stats-tab .data-table th:nth-child(6),
#commission-stats-tab .data-table td:nth-child(6) {
    width: 80px;
    min-width: 80px;
    text-align: right;
}

#commission-stats-tab .data-table th:nth-child(7),
#commission-stats-tab .data-table td:nth-child(7) {
    width: 80px;
    min-width: 80px;
    text-align: center;
}

#commission-stats-tab .data-table th:nth-child(8),
#commission-stats-tab .data-table td:nth-child(8) {
    width: 80px;
    min-width: 80px;
    text-align: right;
}

#commission-stats-tab .data-table th:nth-child(9),
#commission-stats-tab .data-table td:nth-child(9) {
    width: 100px;
    min-width: 100px;
    text-align: center;
}

#commission-stats-tab .data-table tbody tr:hover {
    background: #f8f9fa;
}

#commission-stats-tab .data-table tbody tr:last-child td {
    border-bottom: none;
}

/* 分成统计Tab导航样式 */
.commission-tabs {
    margin-top: 20px;
}

.commission-tab-nav {
    display: flex;
    border-bottom: 2px solid #e9ecef;
    margin-bottom: 20px;
}

.commission-tab-btn {
    padding: 12px 24px;
    border: none;
    background: transparent;
    color: #6c757d;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
    position: relative;
}

.commission-tab-btn:hover {
    color: #4facfe;
    background: #f8f9fa;
}

.commission-tab-btn.active {
    color: #4facfe;
    border-bottom-color: #4facfe;
    background: white;
}

.commission-tab-content {
    display: none;
}

.commission-tab-content.active {
    display: block;
}

/* 选中会员信息显示样式 */
.selected-member-info {
    margin-bottom: 15px;
}

.member-info-display {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 12px 15px;
    display: flex;
    gap: 20px;
    align-items: center;
}

.member-info-display .member-id,
.member-info-display .member-name {
    font-size: 14px;
    color: #495057;
}

.member-info-display .member-id span,
.member-info-display .member-name span {
    font-weight: 600;
    color: #212529;
}

/* ==================== 管理员管理样式 ==================== */

/* 角色标签样式 */
.role-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
    display: inline-block;
    min-width: 80px;
    white-space: nowrap;
}

.role-0 {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: white;
}

.role-1 {
    background: linear-gradient(45deg, #4834d4, #686de0);
    color: white;
}

.role-2 {
    background: linear-gradient(45deg, #00d2d3, #54a0ff);
    color: white;
}

/* 状态标签样式 */
.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
    display: inline-block;
    min-width: 50px;
    white-space: nowrap;
}

.status-active {
    background: linear-gradient(45deg, #2ed573, #7bed9f);
    color: white;
}

.status-inactive {
    background: linear-gradient(45deg, #ff6b6b, #ff7675);
    color: white;
}

/* 管理员管理表格特殊样式 */
#adminTable .action-buttons {
    display: flex;
    justify-content: flex-start;
    gap: 6px;
    flex-wrap: nowrap;
}

#adminTable .action-buttons .btn {
    min-width: 50px;
    padding: 4px 8px;
    font-size: 12px;
    white-space: nowrap;
}

#adminTable .role-badge,
#adminTable .status-badge {
    white-space: nowrap;
}

#adminTable td {
    vertical-align: middle;
}

/* 管理员表格列宽设置 */
#adminTable th:nth-child(1),
#adminTable td:nth-child(1) { width: 6%; }   /* 编号 */
#adminTable th:nth-child(2),
#adminTable td:nth-child(2) { width: 10%; }  /* 用户名 */
#adminTable th:nth-child(3),
#adminTable td:nth-child(3) { width: 10%; }  /* 姓名 */
#adminTable th:nth-child(4),
#adminTable td:nth-child(4) { width: 10%; }  /* 角色 */
#adminTable th:nth-child(5),
#adminTable td:nth-child(5) { width: 10%; }  /* 创建者 */
#adminTable th:nth-child(6),
#adminTable td:nth-child(6) { width: 13%; }  /* 创建时间 */
#adminTable th:nth-child(7),
#adminTable td:nth-child(7) { width: 13%; }  /* 最后登录 */
#adminTable th:nth-child(8),
#adminTable td:nth-child(8) { width: 8%; }   /* 状态 */
#adminTable th:nth-child(9),
#adminTable td:nth-child(9) { width: 20%; }  /* 操作 */

/* 新增管理员弹窗样式 */
#addAdminModal .form-group select {
    width: 100%;
    padding: 12px;
    border: 2px solid #e1e8ed;
    border-radius: 8px;
    font-size: 14px;
    background: white;
    transition: all 0.3s ease;
}

#addAdminModal .form-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 角色显示样式 */
.role-display {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.role-display .role-badge {
    align-self: flex-start;
}

.role-display .text-muted {
    color: #6c757d;
    font-size: 0.875em;
    margin: 0;
}

/* 管理员管理面板样式 */
#admin-manage-tab .panel-header {
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 15px;
    margin-bottom: 20px;
}

#admin-manage-tab .table-container {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
}

#admin-manage-tab .table-footer {
    background: #f8f9fa;
    padding: 15px 20px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

#admin-manage-tab .table-info {
    color: #666;
    font-size: 0.9em;
    font-weight: 500;
}

/* 响应式设计 - 管理员管理 */
@media (max-width: 768px) {
    .role-badge,
    .status-badge {
        font-size: 10px;
        padding: 2px 6px;
        min-width: 60px;
    }

    #adminTable th:nth-child(5),
    #adminTable td:nth-child(5),
    #adminTable th:nth-child(6),
    #adminTable td:nth-child(6),
    #adminTable th:nth-child(7),
    #adminTable td:nth-child(7) {
        display: none; /* 在小屏幕上隐藏创建者、创建时间、最后登录列 */
    }

    #adminTable th:nth-child(1),
    #adminTable td:nth-child(1) { width: 8%; }  /* 编号 */
    #adminTable th:nth-child(2),
    #adminTable td:nth-child(2) { width: 18%; }  /* 用户名 */
    #adminTable th:nth-child(3),
    #adminTable td:nth-child(3) { width: 18%; }  /* 姓名 */
    #adminTable th:nth-child(4),
    #adminTable td:nth-child(4) { width: 18%; }  /* 角色 */
    #adminTable th:nth-child(8),
    #adminTable td:nth-child(8) { width: 13%; }  /* 状态 */
    #adminTable th:nth-child(9),
    #adminTable td:nth-child(9) { width: 25%; }  /* 操作 */
}

/* ==================== 自定义 Tooltip 样式 ==================== */
/* 自定义tooltip容器 */
.custom-tooltip {
    position: relative;
    cursor: help;
}

/* 自定义tooltip内容 - 右侧显示 */
.custom-tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    top: 50%;
    left: 100%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 6px 10px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s ease, visibility 0.2s ease;
    z-index: 9999;
    pointer-events: none;
    margin-left: 8px;
}

/* 自定义tooltip箭头 - 右侧显示 */
.custom-tooltip::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 100%;
    transform: translateY(-50%);
    border: 5px solid transparent;
    border-right-color: rgba(0, 0, 0, 0.9);
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s ease, visibility 0.2s ease;
    z-index: 9999;
    pointer-events: none;
    margin-left: 3px;
}

/* 鼠标悬停时显示tooltip - 快速响应 */
.custom-tooltip:hover::after,
.custom-tooltip:hover::before {
    opacity: 1;
    visibility: visible;
    transition-delay: 0.1s; /* 只有0.1秒延迟 */
}

/* 针对过期会员日期的特殊样式 */
.member-status.expired.custom-tooltip {
    color: red;
    font-weight: 500;
}

/* 确保tooltip不会被表格边界裁剪 */
.table-container {
    overflow: visible;
}

.table-wrapper {
    overflow-x: auto;
    overflow-y: visible;
}

/* ========== 重置密码弹窗样式 ========== */
.reset-password-info {
    margin-bottom: 20px;
}

.reset-password-info .info-text {
    margin-bottom: 8px;
    font-size: 14px;
    color: #333;
}

.reset-password-info .warning-text {
    font-size: 13px;
    color: #e74c3c;
    background: #ffeaea;
    padding: 8px 12px;
    border-radius: 4px;
    border-left: 3px solid #e74c3c;
}

.password-input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.password-input-group input {
    flex: 1;
    padding-right: 45px;
}

.password-toggle {
    position: absolute;
    right: 10px;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 16px;
    color: #666;
    padding: 4px;
    border-radius: 2px;
    transition: color 0.2s ease;
}

.password-toggle:hover {
    color: #333;
    background: rgba(0, 0, 0, 0.05);
}

.password-toggle:focus {
    outline: none;
    color: #007bff;
}

/* 重置密码按钮样式 */
.btn-info {
    background-color: #17a2b8;
    border-color: #17a2b8;
    color: white;
}

.btn-info:hover {
    background-color: #138496;
    border-color: #117a8b;
}

/* 重置密码弹窗特殊样式 */
#resetPasswordModal .modal-footer .btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;
}

#resetPasswordModal .modal-footer .btn-danger:hover {
    background-color: #c82333;
    border-color: #bd2130;
}
