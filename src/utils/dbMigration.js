const fs = require('fs');
const path = require('path');
const { app } = require('electron');

/**
 * 数据库迁移工具
 * 在应用首次启动时，将开发环境的数据库文件复制到用户数据目录
 */
class DatabaseMigration {
    constructor() {
        this.devDbPath = path.join(__dirname, '../../data/member.db');
        // 打包后使用应用目录下的 data 文件夹
        const appPath = path.dirname(process.execPath);
        this.prodDbDir = path.join(appPath, 'data');
        this.prodDbPath = path.join(this.prodDbDir, 'member.db');
    }

    /**
     * 检查并执行数据库迁移
     */
    async migrate() {
        try {
            // 确保应用数据目录存在
            if (!fs.existsSync(this.prodDbDir)) {
                fs.mkdirSync(this.prodDbDir, { recursive: true });
                console.log('创建应用数据目录:', this.prodDbDir);
            }

            // 如果生产环境数据库不存在，且开发环境数据库存在，则复制
            if (!fs.existsSync(this.prodDbPath) && fs.existsSync(this.devDbPath)) {
                await this.copyDatabase();
                console.log('数据库迁移完成');
            } else if (fs.existsSync(this.prodDbPath)) {
                console.log('应用数据库已存在，跳过迁移');
            } else {
                console.log('开发环境数据库不存在，将创建新数据库');
            }

            return this.prodDbPath;
        } catch (error) {
            console.error('数据库迁移失败:', error);
            throw error;
        }
    }

    /**
     * 复制数据库文件及相关WAL文件
     */
    async copyDatabase() {
        try {
            // 复制主数据库文件
            await this.copyFile(this.devDbPath, this.prodDbPath);
            console.log(`数据库已复制: ${this.devDbPath} -> ${this.prodDbPath}`);

            // 检查并复制WAL文件（如果存在）
            const devWalPath = this.devDbPath + '-wal';
            const prodWalPath = this.prodDbPath + '-wal';
            if (fs.existsSync(devWalPath)) {
                await this.copyFile(devWalPath, prodWalPath);
                console.log(`WAL文件已复制: ${devWalPath} -> ${prodWalPath}`);
            }

            // 注意：SHM文件不需要复制，因为它是临时的共享内存文件
            // 当数据库重新打开时会自动重建
            console.log('数据库复制完成（SHM文件将在数据库打开时自动重建）');
        } catch (error) {
            console.error('数据库复制失败:', error);
            throw error;
        }
    }

    /**
     * 复制单个文件的辅助方法
     */
    async copyFile(sourcePath, destPath) {
        return new Promise((resolve, reject) => {
            const readStream = fs.createReadStream(sourcePath);
            const writeStream = fs.createWriteStream(destPath);

            readStream.on('error', reject);
            writeStream.on('error', reject);
            writeStream.on('finish', resolve);

            readStream.pipe(writeStream);
        });
    }

    /**
     * 获取生产环境数据库路径
     */
    getProductionDbPath() {
        return this.prodDbPath;
    }

    /**
     * 检查是否为打包环境
     */
    isPackaged() {
        return app.isPackaged;
    }
}

module.exports = DatabaseMigration;