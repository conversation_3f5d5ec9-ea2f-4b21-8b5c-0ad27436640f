const util = require('util');

class Logger {
    constructor() {
        // 在Windows上设置控制台编码
        if (process.platform === 'win32') {
            try {
                // 尝试设置控制台代码页为UTF-8
                const { execSync } = require('child_process');
                execSync('chcp 65001', { stdio: 'ignore' });
            } catch (error) {
                // 忽略错误
            }
        }
    }

    // 安全的日志输出方法
    log(...args) {
        try {
            // 将所有参数转换为字符串，避免编码问题
            const message = args.map(arg => {
                if (typeof arg === 'string') {
                    return arg;
                } else if (typeof arg === 'object') {
                    return JSON.stringify(arg, null, 2);
                } else {
                    return String(arg);
                }
            }).join(' ');
            
            console.log(message);
        } catch (error) {
            // 如果还是有编码问题，输出错误信息
            console.log('日志输出错误:', error.message);
        }
    }

    error(...args) {
        try {
            const message = args.map(arg => {
                if (typeof arg === 'string') {
                    return arg;
                } else if (typeof arg === 'object') {
                    return JSON.stringify(arg, null, 2);
                } else {
                    return String(arg);
                }
            }).join(' ');
            
            console.error(message);
        } catch (error) {
            console.error('错误输出错误:', error.message);
        }
    }

    info(...args) {
        this.log('[INFO]', ...args);
    }

    warn(...args) {
        this.log('[WARN]', ...args);
    }

    debug(...args) {
        if (process.env.NODE_ENV === 'development') {
            this.log('[DEBUG]', ...args);
        }
    }
}

// 创建单例实例
const logger = new Logger();

module.exports = logger;
