<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4A90E2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#357ABD;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="256" cy="256" r="240" fill="url(#gradient)" stroke="#2C5F8A" stroke-width="8"/>
  
  <!-- 用户图标 -->
  <g transform="translate(256,256)">
    <!-- 头部 -->
    <circle cx="0" cy="-40" r="50" fill="white" opacity="0.9"/>
    
    <!-- 身体 -->
    <path d="M -80 40 Q -80 -20 -40 -20 L 40 -20 Q 80 -20 80 40 L 80 120 L -80 120 Z" 
          fill="white" opacity="0.9"/>
    
    <!-- 装饰线条 -->
    <rect x="-60" y="60" width="120" height="4" fill="#4A90E2" rx="2"/>
    <rect x="-40" y="80" width="80" height="3" fill="#4A90E2" rx="1.5"/>
    <rect x="-30" y="95" width="60" height="3" fill="#4A90E2" rx="1.5"/>
  </g>
  
  <!-- 标题文字 -->
  <text x="256" y="450" text-anchor="middle" font-family="Arial, sans-serif" 
        font-size="36" font-weight="bold" fill="white">会员系统</text>
</svg>