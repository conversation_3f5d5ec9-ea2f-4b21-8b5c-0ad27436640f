name: Build macOS App

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]
  workflow_dispatch:

jobs:
  build-macos:
    runs-on: macos-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm install
      
    - name: Build macOS app
      run: npm run build-mac
      
    - name: Upload macOS artifact
      uses: actions/upload-artifact@v4
      with:
        name: macos-app
        path: dist/*.dmg
        retention-days: 30
        
  build-windows:
    runs-on: windows-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm install
      
    - name: Build Windows app
      run: npm run build-win
      
    - name: Upload Windows artifact
      uses: actions/upload-artifact@v4
      with:
        name: windows-app
        path: dist/*.exe
        retention-days: 30
        
  build-linux:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm install
      
    - name: Build Linux app
      run: npm run build-linux
      
    - name: Upload Linux artifact
      uses: actions/upload-artifact@v4
      with:
        name: linux-app
        path: dist/*.AppImage
        retention-days: 30